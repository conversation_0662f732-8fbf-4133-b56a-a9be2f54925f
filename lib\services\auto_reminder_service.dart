import 'dart:async';
import '../models/order_model.dart';
import '../services/order_service.dart';
import '../services/smart_notification_service.dart';
import '../services/timeline_service.dart';
import '../services/chat_service.dart';
import '../models/notification_model.dart';

class AutoReminderService {
  static Timer? _reminderTimer;
  static const Duration reminderInterval = Duration(hours: 1); // Check every hour
  static const Duration firstReminderDelay = Duration(days: 1); // First reminder after 1 day
  static const Duration secondReminderDelay = Duration(days: 2); // Second reminder after 2 days
  static const Duration autoConfirmDelay = Duration(days: 3); // Auto-confirm after 3 days

  /// Start the auto-reminder service
  static void startService() {
    stopService(); // Stop any existing timer

    _reminderTimer = Timer.periodic(reminderInterval, (timer) {
      _checkPendingDeliveries();
    });
  }

  /// Stop the auto-reminder service
  static void stopService() {
    _reminderTimer?.cancel();
    _reminderTimer = null;
  }

  /// Check for pending deliveries and send reminders/auto-confirm
  static Future<void> _checkPendingDeliveries() async {
    try {
      // Get all delivered orders that haven't been confirmed
      final deliveredOrders = await OrderService.getOrdersByStatus(OrderStatus.delivered);

      for (final order in deliveredOrders) {
        if (order.deliveryDate != null) {
          final daysSinceDelivery = DateTime.now().difference(order.deliveryDate!).inDays;

          if (daysSinceDelivery >= autoConfirmDelay.inDays) {
            // Auto-confirm after 3 days
            await _autoConfirmOrder(order);
          } else if (daysSinceDelivery >= secondReminderDelay.inDays) {
            // Send second reminder after 2 days
            await _sendSecondReminder(order);
          } else if (daysSinceDelivery >= firstReminderDelay.inDays) {
            // Send first reminder after 1 day
            await _sendFirstReminder(order);
          }
        }
      }
    } catch (e) {
      print('Error in auto-reminder service: $e');
    }
  }

  /// Send first reminder to client
  static Future<void> _sendFirstReminder(OrderModel order) async {
    try {
      // Check if first reminder was already sent
      if (await _wasReminderSent(order.id, 'first_reminder')) {
        return;
      }

      // Send notification to client
      await SmartNotificationService.createSmartNotification(
        userId: order.clientId,
        titleEn: 'Work Delivered - Please Review',
        titleAr: 'تم تسليم العمل - يرجى المراجعة',
        descriptionEn: 'Your freelancer has delivered the work. Please review and confirm receipt.',
        descriptionAr: 'قام المستقل بتسليم العمل. يرجى المراجعة وتأكيد الاستلام.',
        type: NotificationType.reminderReviewPending,
        relatedId: order.id,
        priority: NotificationPriority.high,
        metadata: {'order_id': order.id, 'reminder_type': 'first'},
      );

      // Create timeline message
      final chat = await ChatService.getChatByRequestId(order.requestId);
      if (chat != null) {
        await TimelineService.createDeliveryReminderTimeline(chatId: chat.id, orderId: order.id, reminderType: 'first');
      }

      // Mark reminder as sent
      await _markReminderSent(order.id, 'first_reminder');
    } catch (e) {
      print('Error sending first reminder for order ${order.id}: $e');
    }
  }

  /// Send second reminder to client
  static Future<void> _sendSecondReminder(OrderModel order) async {
    try {
      // Check if second reminder was already sent
      if (await _wasReminderSent(order.id, 'second_reminder')) {
        return;
      }

      // Send urgent notification to client
      await SmartNotificationService.createSmartNotification(
        userId: order.clientId,
        titleEn: 'Urgent: Please Confirm Work Receipt',
        titleAr: 'عاجل: يرجى تأكيد استلام العمل',
        descriptionEn: 'Your work will be auto-confirmed in 24 hours if no action is taken.',
        descriptionAr: 'سيتم تأكيد العمل تلقائياً خلال 24 ساعة إذا لم يتم اتخاذ أي إجراء.',
        type: NotificationType.reminderReviewPending,
        relatedId: order.id,
        priority: NotificationPriority.urgent,
        metadata: {'order_id': order.id, 'reminder_type': 'second', 'auto_confirm_in_hours': '24'},
      );

      // Create timeline message
      final chat = await ChatService.getChatByRequestId(order.requestId);
      if (chat != null) {
        await TimelineService.createDeliveryReminderTimeline(
          chatId: chat.id,
          orderId: order.id,
          reminderType: 'second',
        );
      }

      // Mark reminder as sent
      await _markReminderSent(order.id, 'second_reminder');
    } catch (e) {
      print('Error sending second reminder for order ${order.id}: $e');
    }
  }

  /// Auto-confirm order after deadline
  static Future<void> _autoConfirmOrder(OrderModel order) async {
    try {
      // Check if auto-confirmation was already done
      if (await _wasReminderSent(order.id, 'auto_confirmed')) {
        return;
      }

      // Auto-complete the order with default rating
      await OrderService.completeOrder(
        order.id,
        4.0, // Default rating of 4 stars
        'Order auto-confirmed after delivery deadline.',
      );

      // Send notification to both client and freelancer
      await SmartNotificationService.createSmartNotification(
        userId: order.clientId,
        titleEn: 'Order Auto-Confirmed',
        titleAr: 'تم تأكيد الطلب تلقائياً',
        descriptionEn: 'Your order has been automatically confirmed and payment released.',
        descriptionAr: 'تم تأكيد طلبك تلقائياً وتم إطلاق الدفعة.',
        type: NotificationType.orderCompleted,
        relatedId: order.id,
        priority: NotificationPriority.high,
        metadata: {'order_id': order.id},
      );

      await SmartNotificationService.createSmartNotification(
        userId: order.freelancerId,
        titleEn: 'Payment Released',
        titleAr: 'تم إطلاق الدفعة',
        descriptionEn: 'Your payment has been automatically released after delivery confirmation deadline.',
        descriptionAr: 'تم إطلاق دفعتك تلقائياً بعد انتهاء مهلة تأكيد التسليم.',
        type: NotificationType.paymentConfirmed,
        relatedId: order.id,
        priority: NotificationPriority.high,
        metadata: {'order_id': order.id},
      );

      // Create timeline message
      final chat = await ChatService.getChatByRequestId(order.requestId);
      if (chat != null) {
        await TimelineService.createAutoConfirmationTimeline(chatId: chat.id, orderId: order.id);
      }

      // Mark as auto-confirmed
      await _markReminderSent(order.id, 'auto_confirmed');
    } catch (e) {
      print('Error auto-confirming order ${order.id}: $e');
    }
  }

  /// Check if a specific reminder was already sent
  static Future<bool> _wasReminderSent(String orderId, String reminderType) async {
    // In a real app, this would check a database table for sent reminders
    // For demo purposes, we'll use a simple in-memory cache
    return _sentReminders.contains('${orderId}_$reminderType');
  }

  /// Mark a reminder as sent
  static Future<void> _markReminderSent(String orderId, String reminderType) async {
    // In a real app, this would store in database
    // For demo purposes, we'll use a simple in-memory cache
    _sentReminders.add('${orderId}_$reminderType');
  }

  // Simple in-memory cache for demo purposes
  static final Set<String> _sentReminders = <String>{};

  /// Get reminder status for an order
  static Future<Map<String, bool>> getReminderStatus(String orderId) async {
    return {
      'first_reminder_sent': await _wasReminderSent(orderId, 'first_reminder'),
      'second_reminder_sent': await _wasReminderSent(orderId, 'second_reminder'),
      'auto_confirmed': await _wasReminderSent(orderId, 'auto_confirmed'),
    };
  }

  /// Calculate time remaining until auto-confirmation
  static Duration? getTimeUntilAutoConfirm(OrderModel order) {
    if (order.status != OrderStatus.delivered || order.deliveryDate == null) {
      return null;
    }

    final autoConfirmTime = order.deliveryDate!.add(autoConfirmDelay);
    final now = DateTime.now();

    if (now.isAfter(autoConfirmTime)) {
      return Duration.zero;
    }

    return autoConfirmTime.difference(now);
  }

  /// Format time remaining for display
  static String formatTimeRemaining(Duration duration) {
    if (duration.inDays > 0) {
      return '${duration.inDays} day${duration.inDays > 1 ? 's' : ''}';
    } else if (duration.inHours > 0) {
      return '${duration.inHours} hour${duration.inHours > 1 ? 's' : ''}';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes} minute${duration.inMinutes > 1 ? 's' : ''}';
    } else {
      return 'Less than a minute';
    }
  }

  /// Send manual reminder (can be triggered by freelancer)
  static Future<void> sendManualReminder(String orderId) async {
    try {
      final order = await OrderService.getOrderById(orderId);
      if (order == null || order.status != OrderStatus.delivered) {
        throw Exception('Order not found or not in delivered status');
      }

      // Send notification to client
      await SmartNotificationService.createSmartNotification(
        userId: order.clientId,
        titleEn: 'Reminder: Please Confirm Work Receipt',
        titleAr: 'تذكير: يرجى تأكيد استلام العمل',
        descriptionEn: 'Your freelancer is waiting for you to confirm the delivered work.',
        descriptionAr: 'المستقل الخاص بك ينتظر منك تأكيد استلام العمل المسلم.',
        type: NotificationType.reminderReviewPending,
        relatedId: order.id,
        priority: NotificationPriority.normal,
        metadata: {'order_id': order.id},
      );

      // Create timeline message
      final chat = await ChatService.getChatByRequestId(order.requestId);
      if (chat != null) {
        await TimelineService.createManualReminderTimeline(chatId: chat.id, orderId: order.id);
      }
    } catch (e) {
      throw Exception('Failed to send manual reminder: $e');
    }
  }
}

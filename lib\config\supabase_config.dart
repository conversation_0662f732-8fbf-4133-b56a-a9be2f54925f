import 'package:supabase_flutter/supabase_flutter.dart';

class SupabaseConfig {
  // TODO: Replace with your actual Supabase project URL and anon key
  // Get these from: https://supabase.com/dashboard/project/YOUR_PROJECT/settings/api
  static const String supabaseUrl = 'YOUR_SUPABASE_PROJECT_URL';
  static const String supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';

  static Future<void> initialize() async {
    // TEMPORARY: Skip Supabase initialization for demo mode
    if (supabaseUrl == 'YOUR_SUPABASE_PROJECT_URL' || supabaseAnonKey == 'YOUR_SUPABASE_ANON_KEY') {
      print('⚠️ Running in DEMO MODE - Supabase not configured');
      print('📝 To enable full functionality, update lib/config/supabase_config.dart with your Supabase credentials');
      return; // Skip initialization for demo
    }

    try {
      await Supabase.initialize(url: supabaseUrl, anonKey: supabaseAnonKey);
      print('✅ Supabase initialized successfully');
    } catch (e) {
      print('❌ Failed to initialize Supabase: $e');
      print('⚠️ Running in DEMO MODE');
    }
  }

  static SupabaseClient get client {
    try {
      return Supabase.instance.client;
    } catch (e) {
      print('⚠️ Supabase client not available - running in demo mode');
      // Return a mock client or handle gracefully for demo mode
      rethrow; // For now, let services handle the error
    }
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/language_provider.dart';
import '../../utils/app_localizations.dart';
import '../../models/service_request_model.dart';
import '../chat/chat_screen.dart';

class BrowseRequestsScreen extends StatefulWidget {
  const BrowseRequestsScreen({super.key});

  @override
  State<BrowseRequestsScreen> createState() => _BrowseRequestsScreenState();
}

class _BrowseRequestsScreenState extends State<BrowseRequestsScreen> {
  List<ServiceRequestModel> _requests = [];
  bool _isLoading = true;
  String _selectedCategory = 'All';
  Priority? _selectedPriority;

  @override
  void initState() {
    super.initState();
    _loadRequests();
  }

  void _loadRequests() {
    // Demo data - in real app, this would come from API
    setState(() {
      _requests = [
        ServiceRequestModel(
          id: '1',
          clientId: 'client1',
          title: 'E-commerce Mobile App Development',
          description:
              'I need a Flutter mobile app for my online store. The app should have user authentication, product catalog, shopping cart, and payment integration. Looking for an experienced Flutter developer.',
          category: 'Mobile Development',
          budget: 3500.0,
          deadline: DateTime.now().add(const Duration(days: 30)),
          priority: Priority.urgent,
          status: RequestStatus.pending,
          createdAt: DateTime.now().subtract(const Duration(hours: 2)),
          attachments: ['requirements.pdf', 'mockups.png'],
        ),
        ServiceRequestModel(
          id: '2',
          clientId: 'client2',
          title: 'Logo Design for Tech Startup',
          description:
              'Need a modern, professional logo for my tech startup. The company focuses on AI solutions. I want something clean and memorable that works well in both digital and print formats.',
          category: 'Graphic Design',
          budget: 500.0,
          deadline: DateTime.now().add(const Duration(days: 7)),
          priority: Priority.normal,
          status: RequestStatus.pending,
          createdAt: DateTime.now().subtract(const Duration(hours: 5)),
          attachments: ['brand_guidelines.pdf'],
        ),
        ServiceRequestModel(
          id: '3',
          clientId: 'client3',
          title: 'Content Writing for Blog',
          description:
              'Looking for a skilled content writer to create 10 high-quality blog posts about digital marketing trends. Each post should be 1500-2000 words, SEO optimized, and engaging.',
          category: 'Content Writing',
          budget: 800.0,
          deadline: DateTime.now().add(const Duration(days: 14)),
          priority: Priority.vip,
          status: RequestStatus.pending,
          createdAt: DateTime.now().subtract(const Duration(hours: 8)),
          attachments: ['content_brief.docx'],
        ),
        ServiceRequestModel(
          id: '4',
          clientId: 'client4',
          title: 'WordPress Website Development',
          description:
              'Need a professional WordPress website for my consulting business. Should include homepage, about, services, blog, and contact pages. Must be responsive and SEO-friendly.',
          category: 'Web Development',
          budget: 1200.0,
          deadline: DateTime.now().add(const Duration(days: 21)),
          priority: Priority.normal,
          status: RequestStatus.pending,
          createdAt: DateTime.now().subtract(const Duration(days: 1)),
          attachments: ['site_structure.pdf'],
        ),
        ServiceRequestModel(
          id: '5',
          clientId: 'client5',
          title: 'Social Media Marketing Campaign',
          description:
              'Looking for a digital marketing expert to create and manage a 3-month social media campaign for my restaurant. Need content creation, posting schedule, and performance tracking.',
          category: 'Digital Marketing',
          budget: 2000.0,
          deadline: DateTime.now().add(const Duration(days: 5)),
          priority: Priority.urgent,
          status: RequestStatus.pending,
          createdAt: DateTime.now().subtract(const Duration(days: 2)),
          attachments: ['brand_assets.zip'],
        ),
      ];
      _isLoading = false;
    });
  }

  List<ServiceRequestModel> get _filteredRequests {
    var filtered = _requests;

    if (_selectedCategory != 'All') {
      filtered =
          filtered.where((request) {
            return request.category == _selectedCategory;
          }).toList();
    }

    if (_selectedPriority != null) {
      filtered =
          filtered.where((request) {
            return request.priority == _selectedPriority;
          }).toList();
    }

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final languageProvider = Provider.of<LanguageProvider>(context);

    return Directionality(
      textDirection: languageProvider.textDirection,
      child:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _filteredRequests.isEmpty
              ? _buildEmptyState(l10n)
              : RefreshIndicator(
                onRefresh: () async {
                  _loadRequests();
                },
                child: ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _filteredRequests.length,
                  itemBuilder: (context, index) {
                    final request = _filteredRequests[index];
                    return _buildRequestCard(request, l10n, languageProvider);
                  },
                ),
              ),
    );
  }

  Widget _buildEmptyState(AppLocalizations l10n) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.search_off, size: 64),
          const SizedBox(height: 16),
          Text(l10n.noOpportunitiesAvailable, style: Theme.of(context).textTheme.titleLarge),
          const SizedBox(height: 8),
          Text(
            l10n.checkBackLater,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7)),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              setState(() {
                _selectedCategory = 'All';
                _selectedPriority = null;
              });
            },
            icon: const Icon(Icons.refresh),
            label: Text(
              Provider.of<LanguageProvider>(context, listen: false).isArabic ? 'مسح الفلاتر' : 'Clear Filters',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRequestCard(ServiceRequestModel request, AppLocalizations l10n, LanguageProvider languageProvider) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with priority badge
            Row(
              children: [
                Expanded(child: Text(request.title, style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold))),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getPriorityColor(request.priority).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getPriorityText(request.priority, languageProvider.isArabic),
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: _getPriorityColor(request.priority),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Category
            if (request.category != null)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  request.category!,
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),

            const SizedBox(height: 12),

            // Description
            Text(
              request.description,
              style: Theme.of(context).textTheme.bodyMedium,
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),

            const SizedBox(height: 16),

            // Budget and deadline
            Row(
              children: [
                if (request.budget != null) ...[
                  Icon(Icons.attach_money, size: 16, color: Colors.green[700]),
                  Text(
                    languageProvider.formatCurrency(request.budget!),
                    style: TextStyle(fontWeight: FontWeight.bold, color: Colors.green[700]),
                  ),
                  const SizedBox(width: 16),
                ],
                const Icon(Icons.schedule, size: 16),
                const SizedBox(width: 4),
                Text(
                  languageProvider.isArabic
                      ? 'الموعد النهائي: ${languageProvider.formatDate(request.deadline)}'
                      : 'Due: ${languageProvider.formatDate(request.deadline)}',
                  style: TextStyle(color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6)),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Posted time and attachments
            Row(
              children: [
                Text(
                  languageProvider.isArabic
                      ? 'تم النشر ${languageProvider.getTimeAgo(request.createdAt)}'
                      : 'Posted ${languageProvider.getTimeAgo(request.createdAt)}',
                  style: TextStyle(fontSize: 12, color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6)),
                ),
                if (request.attachments != null && request.attachments!.isNotEmpty) ...[
                  const SizedBox(width: 16),
                  const Icon(Icons.attach_file, size: 16),
                  const SizedBox(width: 4),
                  Text(
                    languageProvider.isArabic
                        ? '${request.attachments!.length} ${request.attachments!.length > 1 ? 'ملفات' : 'ملف'}'
                        : '${request.attachments!.length} file${request.attachments!.length > 1 ? 's' : ''}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ],
            ),

            const SizedBox(height: 16),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      _showRequestDetails(request);
                    },
                    icon: const Icon(Icons.visibility),
                    label: Text(languageProvider.isArabic ? 'عرض التفاصيل' : 'View Details'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      _sendOffer(request);
                    },
                    icon: const Icon(Icons.send),
                    label: Text(languageProvider.isArabic ? 'إرسال عرض' : 'Send Offer'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getPriorityColor(Priority priority) {
    switch (priority) {
      case Priority.normal:
        return Colors.blue;
      case Priority.urgent:
        return Colors.orange;
      case Priority.vip:
        return Colors.purple;
    }
  }

  String _getPriorityText(Priority priority, bool isArabic) {
    switch (priority) {
      case Priority.normal:
        return isArabic ? 'عادي' : 'NORMAL';
      case Priority.urgent:
        return isArabic ? 'عاجل' : 'URGENT';
      case Priority.vip:
        return isArabic ? 'مميز' : 'VIP';
    }
  }

  void _showRequestDetails(ServiceRequestModel request) {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            child: Container(
              padding: const EdgeInsets.all(24),
              constraints: const BoxConstraints(maxWidth: 500),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    request.title,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    languageProvider.isArabic ? 'الوصف:' : 'Description:',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(height: 8),
                  Text(request.description),
                  const SizedBox(height: 16),
                  if (request.budget != null) ...[
                    Text(
                      languageProvider.isArabic
                          ? 'الميزانية: \$${request.budget!.toStringAsFixed(0)}'
                          : 'Budget: \$${request.budget!.toStringAsFixed(0)}',
                      style: TextStyle(fontWeight: FontWeight.bold, color: Colors.green[700]),
                    ),
                    const SizedBox(height: 8),
                  ],
                  Text(
                    languageProvider.isArabic
                        ? 'الموعد النهائي: ${request.deadline.toString().split(' ')[0]}'
                        : 'Deadline: ${request.deadline.toString().split(' ')[0]}',
                  ),
                  if (request.attachments != null && request.attachments!.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    Text(
                      languageProvider.isArabic ? 'المرفقات:' : 'Attachments:',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
                    ),
                    const SizedBox(height: 8),
                    ...request.attachments!.map(
                      (attachment) => Padding(
                        padding: const EdgeInsets.only(bottom: 4),
                        child: Row(
                          children: [
                            const Icon(Icons.attach_file, size: 16),
                            const SizedBox(width: 8),
                            Text(attachment),
                          ],
                        ),
                      ),
                    ),
                  ],
                  const SizedBox(height: 24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text(languageProvider.isArabic ? 'إغلاق' : 'Close'),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          _sendOffer(request);
                        },
                        child: Text(languageProvider.isArabic ? 'إرسال عرض' : 'Send Offer'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
    );
  }

  void _sendOffer(ServiceRequestModel request) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatScreen(chatId: request.id, recipientName: 'Client', requestTitle: request.title),
      ),
    );
  }
}

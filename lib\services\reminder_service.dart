import 'dart:async';
import '../models/order_model.dart';
import '../models/user_model.dart';
import '../models/notification_model.dart';
import '../services/smart_notification_service.dart';
import '../services/order_service.dart';
import '../services/chat_service.dart';
import '../config/supabase_config.dart';

/// Service to handle smart reminders for pending actions
class ReminderService {
  static Timer? _reminderTimer;
  static const Duration _checkInterval = Duration(hours: 1);

  /// Initialize the reminder service
  static void initialize() {
    _startReminderEngine();
  }

  /// Start the reminder engine that checks for pending actions
  static void _startReminderEngine() {
    _reminderTimer?.cancel();
    _reminderTimer = Timer.periodic(_checkInterval, (timer) {
      _checkPendingReminders();
    });
    print('🔔 Reminder Service started - checking every ${_checkInterval.inHours} hours');
  }

  /// Stop the reminder service
  static void stop() {
    _reminderTimer?.cancel();
    _reminderTimer = null;
  }

  /// Check all types of pending reminders
  static Future<void> _checkPendingReminders() async {
    try {
      await Future.wait([
        _checkPaymentReminders(),
        _checkDeliveryReviewReminders(),
        _checkResponseReminders(),
        _checkOfferExpirationReminders(),
      ]);
    } catch (e) {
      print('Error checking pending reminders: $e');
    }
  }

  /// Check for pending payment reminders
  static Future<void> _checkPaymentReminders() async {
    try {
      // Get orders with pending payments
      final orders = await OrderService.getOrders(status: OrderStatus.paymentPending);

      for (final order in orders) {
        final daysPending = DateTime.now().difference(order.createdAt).inDays;

        // Send reminders at 1, 3, and 7 days
        if (daysPending == 1 || daysPending == 3 || daysPending == 7) {
          await SmartNotificationService.createPaymentReminder(
            clientId: order.clientId,
            orderId: order.id,
            amount: order.amount,
            daysPending: daysPending,
          );
        }
      }
    } catch (e) {
      print('Error checking payment reminders: $e');
    }
  }

  /// Check for delivery review reminders
  static Future<void> _checkDeliveryReviewReminders() async {
    try {
      // Get delivered orders that haven't been reviewed
      final orders = await OrderService.getOrders(status: OrderStatus.delivered);

      for (final order in orders) {
        if (order.deliveryDate != null) {
          final hoursWaiting = DateTime.now().difference(order.deliveryDate!).inHours;

          // Send reminders at 24, 48, and 72 hours
          if (hoursWaiting == 24 || hoursWaiting == 48 || hoursWaiting == 72) {
            await SmartNotificationService.createDeliveryReviewReminder(
              clientId: order.clientId,
              orderId: order.id,
              hoursWaiting: hoursWaiting,
            );
          }
        }
      }
    } catch (e) {
      print('Error checking delivery review reminders: $e');
    }
  }

  /// Check for response reminders in chats
  static Future<void> _checkResponseReminders() async {
    try {
      // This would typically query the database for chats with unread messages
      // For demo purposes, we'll get chats for a demo user
      final chats = await ChatService.getChats('demo_user_id');

      for (final chat in chats) {
        final messages = await ChatService.getMessages(chat.id);
        if (messages.isNotEmpty) {
          final lastMessage = messages.first;
          final hoursWaiting = DateTime.now().difference(lastMessage.createdAt).inHours;

          // Send reminder if no response for 12, 24, or 48 hours
          if (hoursWaiting == 12 || hoursWaiting == 24 || hoursWaiting == 48) {
            // Determine who should get the reminder (the person who didn't send the last message)
            final recipientId = lastMessage.senderId == chat.clientId ? chat.freelancerId : chat.clientId;
            final senderProfile = await _getUserProfile(lastMessage.senderId);
            final senderName = senderProfile?.fullName ?? 'User';

            await SmartNotificationService.createResponseReminder(
              userId: recipientId,
              chatId: chat.id,
              senderName: senderName,
              hoursWaiting: hoursWaiting,
            );
          }
        }
      }
    } catch (e) {
      print('Error checking response reminders: $e');
    }
  }

  /// Check for offer expiration reminders
  static Future<void> _checkOfferExpirationReminders() async {
    try {
      // This would query offers that are about to expire
      // For demo purposes, we'll simulate this
      print('Checking offer expiration reminders...');
      // Implementation would go here when offer system is fully integrated
    } catch (e) {
      print('Error checking offer expiration reminders: $e');
    }
  }

  /// Get user profile by ID
  static Future<UserModel?> _getUserProfile(String userId) async {
    try {
      final response = await SupabaseConfig.client.from('profiles').select().eq('id', userId).single();
      return UserModel.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  // ==================== MANUAL REMINDER METHODS ====================

  /// Manually create a payment reminder
  static Future<void> createManualPaymentReminder({
    required String clientId,
    required String orderId,
    required double amount,
  }) async {
    final order = await OrderService.getOrderById(orderId);
    if (order != null) {
      final daysPending = DateTime.now().difference(order.createdAt).inDays;
      await SmartNotificationService.createPaymentReminder(
        clientId: clientId,
        orderId: orderId,
        amount: amount,
        daysPending: daysPending,
      );
    }
  }

  /// Manually create a delivery review reminder
  static Future<void> createManualDeliveryReviewReminder({required String clientId, required String orderId}) async {
    final order = await OrderService.getOrderById(orderId);
    if (order?.deliveryDate != null) {
      final hoursWaiting = DateTime.now().difference(order!.deliveryDate!).inHours;
      await SmartNotificationService.createDeliveryReviewReminder(
        clientId: clientId,
        orderId: orderId,
        hoursWaiting: hoursWaiting,
      );
    }
  }

  /// Create a custom reminder
  static Future<void> createCustomReminder({
    required String userId,
    required String titleEn,
    required String titleAr,
    required String messageEn,
    required String messageAr,
    String? relatedId,
    String? actionUrl,
    NotificationPriority priority = NotificationPriority.normal,
  }) async {
    await SmartNotificationService.createSmartNotification(
      userId: userId,
      titleEn: titleEn,
      titleAr: titleAr,
      descriptionEn: messageEn,
      descriptionAr: messageAr,
      type: NotificationType.reminderGeneral,
      relatedId: relatedId,
      priority: priority,
      actionUrl: actionUrl,
    );
  }

  /// Create reminder for freelancer to update progress
  static Future<void> createProgressUpdateReminder({
    required String freelancerId,
    required String orderId,
    required int daysSinceStart,
  }) async {
    await SmartNotificationService.createSmartNotification(
      userId: freelancerId,
      titleEn: 'Progress Update Reminder',
      titleAr: 'تذكير بتحديث التقدم',
      descriptionEn:
          'Please update the client on your progress for order #$orderId. It\'s been $daysSinceStart days since work started.',
      descriptionAr: 'يرجى تحديث العميل حول تقدمك في الطلب #$orderId. لقد مر $daysSinceStart أيام منذ بدء العمل.',
      type: NotificationType.reminderGeneral,
      relatedId: orderId,
      priority: NotificationPriority.normal,
      actionUrl: '/orders/$orderId',
    );
  }

  /// Create reminder for client to provide feedback
  static Future<void> createFeedbackReminder({
    required String clientId,
    required String orderId,
    required String freelancerName,
  }) async {
    await SmartNotificationService.createSmartNotification(
      userId: clientId,
      titleEn: 'Feedback Reminder',
      titleAr: 'تذكير بالتقييم',
      descriptionEn: 'Please provide feedback and rating for $freelancerName\'s work on order #$orderId.',
      descriptionAr: 'يرجى تقديم التقييم والملاحظات لعمل $freelancerName في الطلب #$orderId.',
      type: NotificationType.reminderGeneral,
      relatedId: orderId,
      priority: NotificationPriority.normal,
      actionUrl: '/orders/$orderId/feedback',
    );
  }

  /// Get reminder statistics
  static Map<String, dynamic> getReminderStats() {
    return {
      'reminder_engine_active': _reminderTimer?.isActive ?? false,
      'check_interval_hours': _checkInterval.inHours,
      'last_check': DateTime.now().toIso8601String(),
    };
  }
}

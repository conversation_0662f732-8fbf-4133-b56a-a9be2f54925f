import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/notification_model.dart';
import '../../providers/notification_provider.dart';
import '../../providers/language_provider.dart';
import '../../providers/theme_provider.dart';
import '../common/enhanced_widgets.dart';

/// Enhanced notification preview widget that shows recent notifications
class NotificationPreview extends StatelessWidget {
  final VoidCallback? onViewAll;
  final int maxItems;

  const NotificationPreview({super.key, this.onViewAll, this.maxItems = 3});

  @override
  Widget build(BuildContext context) {
    return Consumer3<NotificationProvider, LanguageProvider, ThemeProvider>(
      builder: (context, notificationProvider, languageProvider, themeProvider, child) {
        final recentNotifications = notificationProvider.recentNotifications.take(maxItems).toList();
        final isDark = themeProvider.isDarkMode;
        final isArabic = languageProvider.isArabic;

        if (recentNotifications.isEmpty) {
          return _buildEmptyState(isArabic, isDark);
        }

        return EnhancedCard(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    isArabic ? 'الإشعارات الحديثة' : 'Recent Notifications',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                    ),
                  ),
                  if (onViewAll != null)
                    TextButton(
                      onPressed: onViewAll,
                      child: Text(
                        isArabic ? 'عرض الكل' : 'View All',
                        style: const TextStyle(
                          color: ThemeProvider.primaryBlue,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 12),

              // Notifications List
              ...recentNotifications.map(
                (notification) => _buildNotificationItem(notification, isArabic, isDark, notificationProvider),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildNotificationItem(
    NotificationModel notification,
    bool isArabic,
    bool isDark,
    NotificationProvider notificationProvider,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _handleNotificationTap(notification, notificationProvider),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color:
                notification.isRead
                    ? Colors.transparent
                    : (isDark ? ThemeProvider.primaryBlue.withValues(alpha: 0.1) : Colors.blue.withValues(alpha: 0.05)),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: isDark ? Colors.grey[700]! : Colors.grey[200]!, width: 0.5),
          ),
          child: Row(
            children: [
              // Notification Icon
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: _getNotificationColor(notification.type).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  _getNotificationIcon(notification.type),
                  color: _getNotificationColor(notification.type),
                  size: 16,
                ),
              ),
              const SizedBox(width: 12),

              // Notification Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      notification.getTitle(isArabic ? 'ar' : 'en'),
                      style: TextStyle(
                        fontSize: 13,
                        fontWeight: notification.isRead ? FontWeight.normal : FontWeight.w600,
                        color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      notification.getDescription(isArabic ? 'ar' : 'en'),
                      style: TextStyle(
                        fontSize: 11,
                        color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      notification.getTimeAgo(isArabic ? 'ar' : 'en'),
                      style: TextStyle(
                        fontSize: 10,
                        color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[500],
                      ),
                    ),
                  ],
                ),
              ),

              // Unread Indicator
              if (!notification.isRead)
                Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: _getNotificationColor(notification.type),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(bool isArabic, bool isDark) {
    return EnhancedCard(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Icon(Icons.notifications_none, size: 48, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[400]),
          const SizedBox(height: 12),
          Text(
            isArabic ? 'لا توجد إشعارات حديثة' : 'No recent notifications',
            style: TextStyle(fontSize: 14, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _handleNotificationTap(NotificationModel notification, NotificationProvider notificationProvider) {
    // Mark as read if not already read
    if (!notification.isRead) {
      notificationProvider.markAsRead(notification.id);
    }

    // Handle navigation based on notification type and action URL
    if (notification.actionUrl != null) {
      // Navigate to the specific screen
      // This would be implemented based on your routing system
      print('Navigate to: ${notification.actionUrl}');
    }
  }

  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.orderNew:
      case NotificationType.offerReceived:
        return Colors.blue;
      case NotificationType.orderAccepted:
      case NotificationType.paymentConfirmed:
      case NotificationType.orderCompleted:
      case NotificationType.offerAccepted:
        return Colors.green;
      case NotificationType.orderDelivered:
      case NotificationType.workDelivered:
        return Colors.orange;
      case NotificationType.orderCancelled:
      case NotificationType.offerRejected:
        return Colors.red;
      case NotificationType.newMessage:
      case NotificationType.messageReceived:
        return Colors.blue;
      case NotificationType.systemAnnouncement:
      case NotificationType.adminMessage:
        return Colors.purple;
      case NotificationType.reminderPaymentDue:
      case NotificationType.reminderDeliveryPending:
      case NotificationType.reminderReviewPending:
      case NotificationType.reminderResponseNeeded:
      case NotificationType.reminderGeneral:
        return Colors.amber;
      default:
        return ThemeProvider.primaryBlue;
    }
  }

  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.orderNew:
      case NotificationType.orderAccepted:
      case NotificationType.orderDelivered:
      case NotificationType.orderCompleted:
      case NotificationType.orderCancelled:
        return Icons.shopping_bag;
      case NotificationType.offerReceived:
      case NotificationType.offerAccepted:
      case NotificationType.offerRejected:
        return Icons.local_offer;
      case NotificationType.paymentConfirmed:
      case NotificationType.paymentPending:
        return Icons.payment;
      case NotificationType.newMessage:
      case NotificationType.messageReceived:
        return Icons.message;
      case NotificationType.workDelivered:
        return Icons.file_download;
      case NotificationType.systemAnnouncement:
      case NotificationType.adminMessage:
        return Icons.campaign;
      case NotificationType.reminderPaymentDue:
      case NotificationType.reminderDeliveryPending:
      case NotificationType.reminderReviewPending:
      case NotificationType.reminderResponseNeeded:
      case NotificationType.reminderGeneral:
        return Icons.schedule;
      case NotificationType.revisionRequested:
        return Icons.edit;
      case NotificationType.accountVerification:
        return Icons.verified_user;
      default:
        return Icons.notifications;
    }
  }
}

/// Floating notification preview that appears on new notifications
class FloatingNotificationPreview extends StatefulWidget {
  final NotificationModel notification;
  final VoidCallback? onTap;
  final VoidCallback? onDismiss;
  final bool isArabic;

  const FloatingNotificationPreview({
    super.key,
    required this.notification,
    this.onTap,
    this.onDismiss,
    required this.isArabic,
  });

  @override
  State<FloatingNotificationPreview> createState() => _FloatingNotificationPreviewState();
}

class _FloatingNotificationPreviewState extends State<FloatingNotificationPreview> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  Timer? _autoDismissTimer;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: const Duration(milliseconds: 300), vsync: this);

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOutBack));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOut));

    _controller.forward();

    // Auto dismiss after 3 seconds with proper cleanup
    _autoDismissTimer = Timer(const Duration(seconds: 3), () {
      if (mounted) {
        _dismiss();
      }
    });
  }

  @override
  void dispose() {
    _autoDismissTimer?.cancel();
    _controller.dispose();
    super.dispose();
  }

  void _dismiss() async {
    await _controller.reverse();
    if (mounted) {
      widget.onDismiss?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return ScaleTransition(
          scale: _scaleAnimation,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Container(
              margin: const EdgeInsets.all(16),
              child: Material(
                elevation: 12,
                borderRadius: BorderRadius.circular(16),
                color: isDark ? ThemeProvider.darkCardBackground : Colors.white,
                child: InkWell(
                  onTap: () {
                    widget.onTap?.call();
                    _dismiss();
                  },
                  borderRadius: BorderRadius.circular(16),
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    constraints: const BoxConstraints(maxWidth: 300),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(Icons.notifications_active, color: ThemeProvider.primaryBlue, size: 20),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                widget.notification.getTitle(widget.isArabic ? 'ar' : 'en'),
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            IconButton(
                              onPressed: _dismiss,
                              icon: Icon(
                                Icons.close,
                                size: 16,
                                color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
                              ),
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          widget.notification.getDescription(widget.isArabic ? 'ar' : 'en'),
                          style: TextStyle(
                            fontSize: 12,
                            color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
                          ),
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class FreelancerEarningsModel {
  final String freelancerId;
  final double totalEarnings;
  final double availableBalance;
  final double pendingBalance;
  final double withdrawnAmount;
  final int completedJobs;
  final double averageJobValue;
  final DateTime lastUpdated;
  final List<EarningTransaction> recentTransactions;

  FreelancerEarningsModel({
    required this.freelancerId,
    required this.totalEarnings,
    required this.availableBalance,
    required this.pendingBalance,
    required this.withdrawnAmount,
    required this.completedJobs,
    required this.averageJobValue,
    required this.lastUpdated,
    this.recentTransactions = const [],
  });

  factory FreelancerEarningsModel.fromJson(Map<String, dynamic> json) {
    return FreelancerEarningsModel(
      freelancerId: json['freelancer_id'],
      totalEarnings: json['total_earnings'].toDouble(),
      availableBalance: json['available_balance'].toDouble(),
      pendingBalance: json['pending_balance'].toDouble(),
      withdrawnAmount: json['withdrawn_amount'].toDouble(),
      completedJobs: json['completed_jobs'],
      averageJobValue: json['average_job_value'].toDouble(),
      lastUpdated: DateTime.parse(json['last_updated']),
      recentTransactions: (json['recent_transactions'] as List<dynamic>?)
          ?.map((e) => EarningTransaction.fromJson(e))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'freelancer_id': freelancerId,
      'total_earnings': totalEarnings,
      'available_balance': availableBalance,
      'pending_balance': pendingBalance,
      'withdrawn_amount': withdrawnAmount,
      'completed_jobs': completedJobs,
      'average_job_value': averageJobValue,
      'last_updated': lastUpdated.toIso8601String(),
      'recent_transactions': recentTransactions.map((e) => e.toJson()).toList(),
    };
  }

  bool get canWithdraw => availableBalance >= 500.0; // Minimum withdrawal threshold

  double get withdrawalThreshold => 500.0;
}

enum TransactionType { earning, withdrawal, refund, bonus }

class EarningTransaction {
  final String id;
  final String freelancerId;
  final TransactionType type;
  final double amount;
  final String description;
  final String? orderId;
  final String? payoutRequestId;
  final DateTime createdAt;

  EarningTransaction({
    required this.id,
    required this.freelancerId,
    required this.type,
    required this.amount,
    required this.description,
    this.orderId,
    this.payoutRequestId,
    required this.createdAt,
  });

  factory EarningTransaction.fromJson(Map<String, dynamic> json) {
    return EarningTransaction(
      id: json['id'],
      freelancerId: json['freelancer_id'],
      type: TransactionType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
      ),
      amount: json['amount'].toDouble(),
      description: json['description'],
      orderId: json['order_id'],
      payoutRequestId: json['payout_request_id'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'freelancer_id': freelancerId,
      'type': type.toString().split('.').last,
      'amount': amount,
      'description': description,
      'order_id': orderId,
      'payout_request_id': payoutRequestId,
      'created_at': createdAt.toIso8601String(),
    };
  }

  String get typeDisplayName {
    switch (type) {
      case TransactionType.earning:
        return 'Earning';
      case TransactionType.withdrawal:
        return 'Withdrawal';
      case TransactionType.refund:
        return 'Refund';
      case TransactionType.bonus:
        return 'Bonus';
    }
  }

  bool get isPositive => type == TransactionType.earning || type == TransactionType.bonus || type == TransactionType.refund;
}

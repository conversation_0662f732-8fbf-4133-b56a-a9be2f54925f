import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/theme_provider.dart';
import '../../providers/language_provider.dart';
import '../../models/message_model.dart';
import '../../models/chat_model.dart';
import '../../models/order_model.dart';

/// Enhanced Chat Message Bubble
class EnhancedChatBubble extends StatelessWidget {
  final String message;
  final bool isMe;
  final String time;
  final bool isRead;
  final bool isArabic;

  const EnhancedChatBubble({
    super.key,
    required this.message,
    required this.isMe,
    required this.time,
    this.isRead = false,
    this.isArabic = false,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Row(
        mainAxisAlignment: isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          if (!isMe) ...[
            CircleAvatar(
              radius: 16,
              backgroundColor: ThemeProvider.primaryBlue.withValues(alpha: 0.1),
              child: const Icon(Icons.person, size: 18),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              constraints: BoxConstraints(maxWidth: MediaQuery.of(context).size.width * 0.7),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                gradient:
                    isMe
                        ? LinearGradient(
                          colors: [ThemeProvider.primaryBlue, ThemeProvider.primaryBlue.withValues(alpha: 0.8)],
                        )
                        : null,
                color: isMe ? null : (isDark ? ThemeProvider.darkCardBackground : Colors.grey[100]),
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(20),
                  topRight: const Radius.circular(20),
                  bottomLeft: Radius.circular(isMe ? 20 : 4),
                  bottomRight: Radius.circular(isMe ? 4 : 20),
                ),
                boxShadow: [
                  BoxShadow(color: Colors.black.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 2)),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message,
                    style: TextStyle(
                      color: isMe ? Colors.white : (isDark ? ThemeProvider.darkTextPrimary : Colors.black87),
                      fontSize: 16,
                      height: 1.4,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        time,
                        style: TextStyle(
                          color:
                              isMe
                                  ? Colors.white.withValues(alpha: 0.8)
                                  : (isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
                          fontSize: 12,
                        ),
                      ),
                      if (isMe) ...[const SizedBox(width: 4), Icon(isRead ? Icons.done_all : Icons.done, size: 16)],
                    ],
                  ),
                ],
              ),
            ),
          ),
          if (isMe) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              radius: 16,
              backgroundColor: ThemeProvider.successGreen.withValues(alpha: 0.1),
              child: const Icon(Icons.person, size: 18, color: ThemeProvider.successGreen),
            ),
          ],
        ],
      ),
    );
  }
}

/// Enhanced Chat Input Bar
class EnhancedChatInputBar extends StatefulWidget {
  final Function(String) onSendMessage;
  final VoidCallback? onAttachFile;
  final VoidCallback? onTakePhoto;
  final VoidCallback? onRecordVoice;
  final bool isArabic;

  const EnhancedChatInputBar({
    super.key,
    required this.onSendMessage,
    this.onAttachFile,
    this.onTakePhoto,
    this.onRecordVoice,
    this.isArabic = false,
  });

  @override
  State<EnhancedChatInputBar> createState() => _EnhancedChatInputBarState();
}

class _EnhancedChatInputBarState extends State<EnhancedChatInputBar> {
  final TextEditingController _controller = TextEditingController();
  bool _hasText = false;

  @override
  void initState() {
    super.initState();
    _controller.addListener(() {
      setState(() {
        _hasText = _controller.text.trim().isNotEmpty;
      });
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _sendMessage() {
    if (_hasText) {
      widget.onSendMessage(_controller.text.trim());
      _controller.clear();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? ThemeProvider.darkCardBackground : ThemeProvider.cardBackground,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.1),
            blurRadius: 20,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // Document attachment button
            if (widget.onAttachFile != null)
              IconButton(
                onPressed: widget.onAttachFile,
                icon: const Icon(Icons.attach_file, color: ThemeProvider.primaryBlue),
                style: IconButton.styleFrom(
                  backgroundColor: ThemeProvider.primaryBlue.withValues(alpha: 0.1),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
              ),

            const SizedBox(width: 8),

            // Camera button
            if (widget.onTakePhoto != null)
              IconButton(
                onPressed: widget.onTakePhoto,
                icon: const Icon(Icons.camera_alt, color: ThemeProvider.primaryBlue),
                style: IconButton.styleFrom(
                  backgroundColor: ThemeProvider.primaryBlue.withValues(alpha: 0.1),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
              ),

            const SizedBox(width: 8),

            // Text input field
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: isDark ? ThemeProvider.darkBackground : Colors.grey[100],
                  borderRadius: BorderRadius.circular(24),
                  border: Border.all(color: ThemeProvider.primaryBlue.withValues(alpha: 0.2)),
                ),
                child: TextField(
                  controller: _controller,
                  textDirection: widget.isArabic ? TextDirection.rtl : TextDirection.ltr,
                  decoration: InputDecoration(
                    hintText: widget.isArabic ? 'اكتب رسالة...' : 'Type a message...',
                    hintStyle: TextStyle(color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                  ),
                  style: TextStyle(color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87),
                  maxLines: null,
                  textCapitalization: TextCapitalization.sentences,
                  onSubmitted: (_) => _sendMessage(),
                ),
              ),
            ),

            const SizedBox(width: 8),

            // Send/Voice button
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              child:
                  _hasText
                      ? IconButton(
                        key: const ValueKey('send'),
                        onPressed: _sendMessage,
                        icon: const Icon(Icons.send),
                        style: IconButton.styleFrom(
                          backgroundColor: ThemeProvider.primaryBlue,
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                        ),
                      )
                      : IconButton(
                        key: const ValueKey('voice'),
                        onPressed: widget.onRecordVoice,
                        icon: const Icon(Icons.mic),
                        style: IconButton.styleFrom(
                          backgroundColor: ThemeProvider.primaryBlue.withValues(alpha: 0.1),
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                        ),
                      ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Enhanced Chat List Item with unread badge
class EnhancedChatListItem extends StatelessWidget {
  final ChatModel chat;
  final int unreadCount;
  final String recipientName;
  final String? recipientAvatar;
  final VoidCallback? onTap;
  final bool isArabic;

  const EnhancedChatListItem({
    super.key,
    required this.chat,
    required this.unreadCount,
    required this.recipientName,
    this.recipientAvatar,
    this.onTap,
    this.isArabic = false,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final isArabic = languageProvider.locale.languageCode == 'ar';

    return Directionality(
      textDirection: isArabic ? TextDirection.rtl : TextDirection.ltr,
      child: Card(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        elevation: 2,
        color: isDark ? ThemeProvider.darkCardBackground : ThemeProvider.cardBackground,
        child: ListTile(
          onTap: onTap,
          leading: Stack(
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: ThemeProvider.primaryBlue.withValues(alpha: 0.1),
                child:
                    recipientAvatar != null
                        ? Text(recipientAvatar!, style: const TextStyle(fontSize: 20))
                        : const Icon(Icons.person, color: ThemeProvider.primaryBlue),
              ),
              if (unreadCount > 0)
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: ThemeProvider.primaryBlue,
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: Theme.of(context).scaffoldBackgroundColor, width: 2),
                    ),
                    constraints: const BoxConstraints(minWidth: 20, minHeight: 20),
                    child: Text(
                      unreadCount > 99 ? '99+' : unreadCount.toString(),
                      style: const TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
          title: Text(
            recipientName,
            style: TextStyle(
              fontWeight: unreadCount > 0 ? FontWeight.bold : FontWeight.normal,
              color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
            ),
          ),
          subtitle: Text(
            chat.lastMessage ??
                (languageProvider.locale.languageCode == 'ar' ? 'لا توجد رسائل بعد' : 'No messages yet'),
            style: TextStyle(
              color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
              fontWeight: unreadCount > 0 ? FontWeight.w500 : FontWeight.normal,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          trailing: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              if (chat.lastMessageTime != null)
                Text(
                  _formatTime(chat.lastMessageTime!, languageProvider.locale.languageCode),
                  style: TextStyle(
                    color:
                        unreadCount > 0
                            ? ThemeProvider.primaryBlue
                            : (isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
                    fontSize: 12,
                    fontWeight: unreadCount > 0 ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              if (unreadCount > 0) const SizedBox(height: 4),
            ],
          ),
        ),
      ),
    );
  }

  String _formatTime(DateTime time, String languageCode) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inDays > 0) {
      return languageCode == 'ar' ? '${difference.inDays} يوم' : '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return languageCode == 'ar' ? '${difference.inHours} ساعة' : '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return languageCode == 'ar' ? '${difference.inMinutes} دقيقة' : '${difference.inMinutes}m';
    } else {
      return languageCode == 'ar' ? 'الآن' : 'now';
    }
  }
}

/// Enhanced Message Bubble with timeline support
class EnhancedMessageBubble extends StatelessWidget {
  final MessageModel message;
  final bool isMe;
  final String? senderName;
  final bool showAvatar;
  final VoidCallback? onTap;

  const EnhancedMessageBubble({
    super.key,
    required this.message,
    required this.isMe,
    this.senderName,
    this.showAvatar = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);

    // Handle timeline messages differently
    if (message.isTimelineMessage) {
      return TimelineMessageWidget(message: message, languageCode: languageProvider.locale.languageCode);
    }

    // Handle system messages
    if (message.isSystemMessage) {
      return SystemMessageWidget(message: message, languageCode: languageProvider.locale.languageCode);
    }

    // Regular message bubble
    return RegularMessageBubble(
      message: message,
      isMe: isMe,
      senderName: senderName,
      showAvatar: showAvatar,
      onTap: onTap,
    );
  }
}

/// Timeline message widget with distinct styling
class TimelineMessageWidget extends StatelessWidget {
  final MessageModel message;
  final String languageCode;

  const TimelineMessageWidget({super.key, required this.message, required this.languageCode});

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final timelineType = message.metadata?['type'] as String?;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          // Timeline line
          Container(width: 2, height: 60, color: _getTimelineColor(timelineType).withValues(alpha: 0.3)),
          const SizedBox(width: 12),

          // Timeline content
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isDark ? ThemeProvider.darkCardBackground.withValues(alpha: 0.5) : Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: _getTimelineColor(timelineType).withValues(alpha: 0.3), width: 1),
              ),
              child: Row(
                children: [
                  // Timeline icon
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getTimelineColor(timelineType).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(_getTimelineIcon(timelineType), style: const TextStyle(fontSize: 16)),
                  ),
                  const SizedBox(width: 12),

                  // Timeline text
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _getLocalizedContent(),
                          style: TextStyle(
                            color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _formatTime(),
                          style: TextStyle(
                            color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getLocalizedContent() {
    // Use TimelineService to get localized content
    return message.content; // For now, will be enhanced with TimelineService
  }

  String _formatTime() {
    final now = DateTime.now();
    final difference = now.difference(message.createdAt);

    if (difference.inDays > 0) {
      return languageCode == 'ar' ? 'منذ ${difference.inDays} يوم' : '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return languageCode == 'ar' ? 'منذ ${difference.inHours} ساعة' : '${difference.inHours} hours ago';
    } else if (difference.inMinutes > 0) {
      return languageCode == 'ar' ? 'منذ ${difference.inMinutes} دقيقة' : '${difference.inMinutes} minutes ago';
    } else {
      return languageCode == 'ar' ? 'الآن' : 'Just now';
    }
  }

  String _getTimelineIcon(String? type) {
    switch (type) {
      case 'order_created':
        return '📋';
      case 'order_accepted':
        return '✅';
      case 'payment_confirmed':
        return '💳';
      case 'work_delivered':
        return '📦';
      case 'order_completed':
        return '🎉';
      case 'order_cancelled':
        return '❌';
      case 'revision_requested':
        return '🔄';
      case 'deadline_extended':
        return '⏰';
      case 'milestone_reached':
        return '🎯';
      default:
        return '📌';
    }
  }

  Color _getTimelineColor(String? type) {
    switch (type) {
      case 'order_created':
        return Colors.blue;
      case 'order_accepted':
        return Colors.green;
      case 'payment_confirmed':
        return Colors.green;
      case 'work_delivered':
        return Colors.orange;
      case 'order_completed':
        return Colors.green;
      case 'order_cancelled':
        return Colors.red;
      case 'revision_requested':
        return Colors.orange;
      case 'deadline_extended':
        return Colors.blue;
      case 'milestone_reached':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }
}

/// System message widget for general system messages
class SystemMessageWidget extends StatelessWidget {
  final MessageModel message;
  final String languageCode;

  const SystemMessageWidget({super.key, required this.message, required this.languageCode});

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: isDark ? ThemeProvider.darkCardBackground.withValues(alpha: 0.3) : Colors.grey[200],
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            message.content,
            style: TextStyle(
              color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
              fontSize: 12,
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
}

/// Regular message bubble for text messages
class RegularMessageBubble extends StatelessWidget {
  final MessageModel message;
  final bool isMe;
  final String? senderName;
  final bool showAvatar;
  final VoidCallback? onTap;

  const RegularMessageBubble({
    super.key,
    required this.message,
    required this.isMe,
    this.senderName,
    this.showAvatar = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Row(
        mainAxisAlignment: isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          if (!isMe && showAvatar) ...[
            CircleAvatar(
              radius: 16,
              backgroundColor: ThemeProvider.primaryBlue.withValues(alpha: 0.1),
              child: const Icon(Icons.person, size: 18, color: ThemeProvider.primaryBlue),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: GestureDetector(
              onTap: onTap,
              child: Container(
                constraints: BoxConstraints(maxWidth: MediaQuery.of(context).size.width * 0.7),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  gradient:
                      isMe
                          ? LinearGradient(
                            colors: [ThemeProvider.primaryBlue, ThemeProvider.primaryBlue.withValues(alpha: 0.8)],
                          )
                          : null,
                  color: isMe ? null : (isDark ? ThemeProvider.darkCardBackground : Colors.grey[100]),
                  borderRadius: BorderRadius.only(
                    topLeft: const Radius.circular(20),
                    topRight: const Radius.circular(20),
                    bottomLeft: Radius.circular(isMe ? 20 : 4),
                    bottomRight: Radius.circular(isMe ? 4 : 20),
                  ),
                  boxShadow: [
                    BoxShadow(color: Colors.black.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 2)),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (!isMe && senderName != null) ...[
                      Text(
                        senderName!,
                        style: const TextStyle(
                          color: ThemeProvider.primaryBlue,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                    ],
                    Text(
                      message.content,
                      style: TextStyle(
                        color: isMe ? Colors.white : (isDark ? ThemeProvider.darkTextPrimary : Colors.black87),
                        fontSize: 16,
                        height: 1.4,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          _formatTime(message.createdAt),
                          style: TextStyle(
                            color:
                                isMe
                                    ? Colors.white.withValues(alpha: 0.8)
                                    : (isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
                            fontSize: 12,
                          ),
                        ),
                        if (isMe) ...[const SizedBox(width: 4), Icon(_getStatusIcon(message.status), size: 16)],
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
          if (isMe && showAvatar) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              radius: 16,
              backgroundColor: ThemeProvider.successGreen.withValues(alpha: 0.1),
              child: const Icon(Icons.person, size: 18),
            ),
          ],
        ],
      ),
    );
  }

  String _formatTime(DateTime time) {
    final hour = time.hour;
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  IconData _getStatusIcon(MessageStatus status) {
    switch (status) {
      case MessageStatus.sent:
        return Icons.done;
      case MessageStatus.delivered:
        return Icons.done_all;
      case MessageStatus.read:
        return Icons.done_all;
    }
  }
}

/// Quick action buttons for workflow actions
class ChatQuickActions extends StatelessWidget {
  final String orderId;
  final OrderStatus orderStatus;
  final bool isClient;
  final VoidCallback? onApproveDelivery;
  final VoidCallback? onRequestRevision;
  final VoidCallback? onMarkComplete;
  final VoidCallback? onViewDelivery;
  final VoidCallback? onMakePayment;
  final VoidCallback? onCancelOrder;

  const ChatQuickActions({
    super.key,
    required this.orderId,
    required this.orderStatus,
    required this.isClient,
    this.onApproveDelivery,
    this.onRequestRevision,
    this.onMarkComplete,
    this.onViewDelivery,
    this.onMakePayment,
    this.onCancelOrder,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isDark = Theme.of(context).brightness == Brightness.dark;

    final actions = _getAvailableActions(languageProvider.locale.languageCode);

    if (actions.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? ThemeProvider.darkCardBackground : ThemeProvider.cardBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: ThemeProvider.primaryBlue.withValues(alpha: 0.2), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.flash_on, color: ThemeProvider.primaryBlue, size: 20),
              const SizedBox(width: 8),
              Text(
                languageProvider.locale.languageCode == 'ar' ? 'إجراءات سريعة' : 'Quick Actions',
                style: TextStyle(
                  color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children:
                actions
                    .map(
                      (action) => _buildActionButton(
                        context,
                        action['label'] as String,
                        action['icon'] as IconData,
                        action['color'] as Color,
                        action['onTap'] as VoidCallback?,
                      ),
                    )
                    .toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(BuildContext context, String label, IconData icon, Color color, VoidCallback? onTap) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, color: color, size: 16),
              const SizedBox(width: 6),
              Text(label, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.w600)),
            ],
          ),
        ),
      ),
    );
  }

  List<Map<String, dynamic>> _getAvailableActions(String languageCode) {
    final actions = <Map<String, dynamic>>[];

    switch (orderStatus) {
      case OrderStatus.paymentPending:
        if (isClient) {
          actions.add({
            'label': languageCode == 'ar' ? 'دفع الآن' : 'Pay Now',
            'icon': Icons.payment,
            'color': Colors.green,
            'onTap': onMakePayment,
          });
        }
        break;

      case OrderStatus.delivered:
        if (isClient) {
          actions.addAll([
            {
              'label': languageCode == 'ar' ? 'عرض التسليم' : 'View Delivery',
              'icon': Icons.visibility,
              'color': Colors.blue,
              'onTap': onViewDelivery,
            },
            {
              'label': languageCode == 'ar' ? 'قبول التسليم' : 'Approve Delivery',
              'icon': Icons.check_circle,
              'color': Colors.green,
              'onTap': onApproveDelivery,
            },
            {
              'label': languageCode == 'ar' ? 'طلب مراجعة' : 'Request Revision',
              'icon': Icons.edit,
              'color': Colors.orange,
              'onTap': onRequestRevision,
            },
          ]);
        }
        break;

      case OrderStatus.inProgress:
        actions.add({
          'label': languageCode == 'ar' ? 'إلغاء الطلب' : 'Cancel Order',
          'icon': Icons.cancel,
          'color': Colors.red,
          'onTap': onCancelOrder,
        });
        break;

      default:
        break;
    }

    return actions;
  }
}

/// Order status indicator widget
class OrderStatusIndicator extends StatelessWidget {
  final OrderStatus status;
  final String orderId;

  const OrderStatusIndicator({super.key, required this.status, required this.orderId});

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final languageCode = languageProvider.locale.languageCode;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _getStatusColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _getStatusColor().withValues(alpha: 0.3), width: 1),
      ),
      child: Row(
        children: [
          Icon(_getStatusIcon(), color: _getStatusColor(), size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  languageCode == 'ar' ? 'حالة الطلب' : 'Order Status',
                  style: TextStyle(color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600], fontSize: 12),
                ),
                const SizedBox(height: 2),
                Text(
                  _getStatusText(languageCode),
                  style: TextStyle(color: _getStatusColor(), fontSize: 14, fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
          Text(
            '#$orderId',
            style: TextStyle(
              color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getStatusIcon() {
    switch (status) {
      case OrderStatus.created:
        return Icons.assignment;
      case OrderStatus.paymentPending:
        return Icons.payment;
      case OrderStatus.paymentConfirmed:
        return Icons.check_circle;
      case OrderStatus.inProgress:
        return Icons.work;
      case OrderStatus.submitted:
        return Icons.upload;
      case OrderStatus.delivered:
        return Icons.local_shipping;
      case OrderStatus.editing:
        return Icons.edit;
      case OrderStatus.completed:
        return Icons.done_all;
      case OrderStatus.cancelled:
        return Icons.cancel;
    }
  }

  Color _getStatusColor() {
    switch (status) {
      case OrderStatus.created:
        return Colors.blue;
      case OrderStatus.paymentPending:
        return Colors.orange;
      case OrderStatus.paymentConfirmed:
        return Colors.green;
      case OrderStatus.inProgress:
        return Colors.blue;
      case OrderStatus.submitted:
        return Colors.purple;
      case OrderStatus.delivered:
        return Colors.orange;
      case OrderStatus.editing:
        return Colors.amber;
      case OrderStatus.completed:
        return Colors.green;
      case OrderStatus.cancelled:
        return Colors.red;
    }
  }

  String _getStatusText(String languageCode) {
    if (languageCode == 'ar') {
      switch (status) {
        case OrderStatus.created:
          return 'تم الإنشاء';
        case OrderStatus.paymentPending:
          return 'في انتظار الدفع';
        case OrderStatus.paymentConfirmed:
          return 'تم تأكيد الدفع';
        case OrderStatus.inProgress:
          return 'قيد التنفيذ';
        case OrderStatus.submitted:
          return 'تم التسليم للمراجعة';
        case OrderStatus.delivered:
          return 'تم التسليم';
        case OrderStatus.editing:
          return 'قيد التعديل';
        case OrderStatus.completed:
          return 'مكتمل';
        case OrderStatus.cancelled:
          return 'ملغي';
      }
    } else {
      switch (status) {
        case OrderStatus.created:
          return 'Created';
        case OrderStatus.paymentPending:
          return 'Payment Pending';
        case OrderStatus.paymentConfirmed:
          return 'Payment Confirmed';
        case OrderStatus.inProgress:
          return 'In Progress';
        case OrderStatus.submitted:
          return 'Submitted for Review';
        case OrderStatus.delivered:
          return 'Delivered';
        case OrderStatus.editing:
          return 'Editing';
        case OrderStatus.completed:
          return 'Completed';
        case OrderStatus.cancelled:
          return 'Cancelled';
      }
    }
  }
}

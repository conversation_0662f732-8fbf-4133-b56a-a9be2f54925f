import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/language_provider.dart';
import '../../providers/theme_provider.dart';
import '../../providers/demo_auth_provider.dart';
import 'language_selection_screen.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final themeProvider = Provider.of<ThemeProvider>(context);
    final authProvider = Provider.of<DemoAuthProvider>(context);
    final isArabic = languageProvider.isArabic;

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        appBar: AppBar(
          title: Text(isArabic ? 'الإعدادات' : 'Settings'),
          elevation: 0,
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // User profile section
              _buildProfileSection(context, authProvider, isArabic),

              const SizedBox(height: 24),

              // App preferences
              _buildSectionHeader(isArabic ? 'تفضيلات التطبيق' : 'App Preferences'),
              _buildLanguageOption(context, languageProvider, isArabic),
              _buildThemeOption(context, themeProvider, isArabic),
              _buildNotificationOption(context, isArabic),

              const SizedBox(height: 24),

              // Account settings
              _buildSectionHeader(isArabic ? 'إعدادات الحساب' : 'Account Settings'),
              _buildProfileOption(context, isArabic),
              _buildPrivacyOption(context, isArabic),
              _buildSecurityOption(context, isArabic),

              const SizedBox(height: 24),

              // Support & Info
              _buildSectionHeader(isArabic ? 'الدعم والمعلومات' : 'Support & Info'),
              _buildHelpOption(context, isArabic),
              _buildAboutOption(context, isArabic),
              _buildContactOption(context, isArabic),

              const SizedBox(height: 24),

              // Logout
              _buildLogoutOption(context, authProvider, isArabic),

              const SizedBox(height: 32),

              // App version
              _buildVersionInfo(isArabic),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileSection(BuildContext context, DemoAuthProvider authProvider, bool isArabic) {
    final user = authProvider.userProfile;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: Theme.of(context).colorScheme.primary,
              child: user?.avatarUrl != null
                  ? ClipOval(
                      child: Image.network(
                        user!.avatarUrl!,
                        width: 60,
                        height: 60,
                        fit: BoxFit.cover,
                      ),
                    )
                  : Text(
                      user?.fullName?.substring(0, 1).toUpperCase() ?? 'U',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    user?.fullName ?? (isArabic ? 'المستخدم' : 'User'),
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    user?.email ?? '',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getRoleColor(user?.role).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _getRoleText(user?.role, isArabic),
                      style: TextStyle(
                        fontSize: 12,
                        color: _getRoleColor(user?.role),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.edit,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildLanguageOption(BuildContext context, LanguageProvider languageProvider, bool isArabic) {
    return _buildSettingsTile(
      context,
      icon: Icons.language,
      title: isArabic ? 'اللغة' : 'Language',
      subtitle: isArabic ? 'العربية / English' : 'Arabic / English',
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              isArabic ? 'العربية' : 'English',
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(width: 8),
          const Icon(Icons.arrow_forward_ios, size: 16),
        ],
      ),
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const LanguageSelectionScreen(),
          ),
        );
      },
    );
  }

  Widget _buildThemeOption(BuildContext context, ThemeProvider themeProvider, bool isArabic) {
    return _buildSettingsTile(
      context,
      icon: themeProvider.isDarkMode ? Icons.dark_mode : Icons.light_mode,
      title: isArabic ? 'المظهر' : 'Theme',
      subtitle: isArabic
          ? (themeProvider.isDarkMode ? 'المظهر الداكن' : 'المظهر الفاتح')
          : (themeProvider.isDarkMode ? 'Dark Mode' : 'Light Mode'),
      trailing: Switch(
        value: themeProvider.isDarkMode,
        onChanged: (value) => themeProvider.toggleTheme(),
      ),
      onTap: () => themeProvider.toggleTheme(),
    );
  }

  Widget _buildNotificationOption(BuildContext context, bool isArabic) {
    return _buildSettingsTile(
      context,
      icon: Icons.notifications,
      title: isArabic ? 'الإشعارات' : 'Notifications',
      subtitle: isArabic ? 'إدارة إشعارات التطبيق' : 'Manage app notifications',
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isArabic ? 'قريباً...' : 'Coming soon...'),
          ),
        );
      },
    );
  }

  Widget _buildProfileOption(BuildContext context, bool isArabic) {
    return _buildSettingsTile(
      context,
      icon: Icons.person,
      title: isArabic ? 'الملف الشخصي' : 'Profile',
      subtitle: isArabic ? 'تحديث معلوماتك الشخصية' : 'Update your personal information',
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isArabic ? 'قريباً...' : 'Coming soon...'),
          ),
        );
      },
    );
  }

  Widget _buildPrivacyOption(BuildContext context, bool isArabic) {
    return _buildSettingsTile(
      context,
      icon: Icons.privacy_tip,
      title: isArabic ? 'الخصوصية' : 'Privacy',
      subtitle: isArabic ? 'إعدادات الخصوصية والأمان' : 'Privacy and security settings',
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isArabic ? 'قريباً...' : 'Coming soon...'),
          ),
        );
      },
    );
  }

  Widget _buildSecurityOption(BuildContext context, bool isArabic) {
    return _buildSettingsTile(
      context,
      icon: Icons.security,
      title: isArabic ? 'الأمان' : 'Security',
      subtitle: isArabic ? 'كلمة المرور والمصادقة' : 'Password and authentication',
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isArabic ? 'قريباً...' : 'Coming soon...'),
          ),
        );
      },
    );
  }

  Widget _buildHelpOption(BuildContext context, bool isArabic) {
    return _buildSettingsTile(
      context,
      icon: Icons.help,
      title: isArabic ? 'المساعدة' : 'Help',
      subtitle: isArabic ? 'الأسئلة الشائعة والدعم' : 'FAQ and support',
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isArabic ? 'قريباً...' : 'Coming soon...'),
          ),
        );
      },
    );
  }

  Widget _buildLogoutOption(BuildContext context, DemoAuthProvider authProvider, bool isArabic) {
    return Card(
      color: Colors.red.withValues(alpha: 0.05),
      child: _buildSettingsTile(
        context,
        icon: Icons.logout,
        title: isArabic ? 'تسجيل الخروج' : 'Logout',
        subtitle: isArabic ? 'الخروج من حسابك' : 'Sign out of your account',
        trailing: const Icon(Icons.arrow_forward_ios, size: 16, color: Colors.red),
        iconColor: Colors.red,
        titleColor: Colors.red,
        onTap: () {
          _showLogoutDialog(context, authProvider, isArabic);
        },
      ),
    );
  }

  Widget _buildVersionInfo(bool isArabic) {
    return Center(
      child: Column(
        children: [
          Text(
            isArabic ? 'منصة الخدمات الأكاديمية' : 'Academic Services Platform',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            isArabic ? 'الإصدار 1.0.0' : 'Version 1.0.0',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsTile(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Widget trailing,
    required VoidCallback onTap,
    Color? iconColor,
    Color? titleColor,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: (iconColor ?? Theme.of(context).colorScheme.primary).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: iconColor ?? Theme.of(context).colorScheme.primary,
            size: 20,
          ),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.w500,
            color: titleColor,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        trailing: trailing,
        onTap: onTap,
      ),
    );
  }

  Color _getRoleColor(role) {
    switch (role?.toString()) {
      case 'UserRole.admin':
        return Colors.red;
      case 'UserRole.freelancer':
        return Colors.blue;
      case 'UserRole.client':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  String _getRoleText(role, bool isArabic) {
    switch (role?.toString()) {
      case 'UserRole.admin':
        return isArabic ? 'مدير' : 'Admin';
      case 'UserRole.freelancer':
        return isArabic ? 'مستقل' : 'Freelancer';
      case 'UserRole.client':
        return isArabic ? 'عميل' : 'Client';
      default:
        return isArabic ? 'مستخدم' : 'User';
    }
  }

  void _showAboutDialog(BuildContext context, bool isArabic) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isArabic ? 'حول التطبيق' : 'About App'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isArabic ? 'منصة الخدمات الأكاديمية' : 'Academic Services Platform',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              isArabic
                  ? 'منصة شاملة تربط بين العملاء والمستقلين لتقديم الخدمات الأكاديمية والتقنية عالية الجودة.'
                  : 'A comprehensive platform connecting clients with freelancers for high-quality academic and technical services.',
            ),
            const SizedBox(height: 16),
            Text(
              isArabic ? 'الميزات:' : 'Features:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            ...([
              isArabic ? '• دعم اللغتين العربية والإنجليزية' : '• Bilingual support (Arabic/English)',
              isArabic ? '• نظام تقييم شامل' : '• Comprehensive review system',
              isArabic ? '• محادثات فورية' : '• Real-time messaging',
              isArabic ? '• إدارة المدفوعات' : '• Payment management',
              isArabic ? '• لوحة تحكم متقدمة' : '• Advanced dashboard',
            ].map((feature) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Text(feature),
            ))),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isArabic ? 'إغلاق' : 'Close'),
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog(BuildContext context, DemoAuthProvider authProvider, bool isArabic) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isArabic ? 'تسجيل الخروج' : 'Logout'),
        content: Text(
          isArabic
              ? 'هل أنت متأكد من أنك تريد تسجيل الخروج؟'
              : 'Are you sure you want to logout?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isArabic ? 'إلغاء' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              authProvider.signOut();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text(
              isArabic ? 'تسجيل الخروج' : 'Logout',
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAboutOption(BuildContext context, bool isArabic) {
    return _buildSettingsTile(
      context,
      icon: Icons.info,
      title: isArabic ? 'حول التطبيق' : 'About',
      subtitle: isArabic ? 'معلومات التطبيق والإصدار' : 'App information and version',
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () {
        _showAboutDialog(context, isArabic);
      },
    );
  }

  Widget _buildContactOption(BuildContext context, bool isArabic) {
    return _buildSettingsTile(
      context,
      icon: Icons.contact_support,
      title: isArabic ? 'اتصل بنا' : 'Contact Us',
      subtitle: isArabic ? 'تواصل مع فريق الدعم' : 'Get in touch with support',
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isArabic ? 'قريباً...' : 'Coming soon...'),
          ),
        );
      },
    );
  }
}

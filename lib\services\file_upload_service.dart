import 'dart:io';
import 'dart:typed_data';
import 'package:file_picker/file_picker.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';

class FileUploadService {
  static const int maxFileSize = 50 * 1024 * 1024; // 50MB
  static const List<String> allowedExtensions = [
    'pdf', 'doc', 'docx', 'txt', 'rtf', // Documents
    'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', // Images
    'zip', 'rar', '7z', 'tar', 'gz', // Archives
    'mp4', 'avi', 'mov', 'wmv', 'flv', // Videos
    'mp3', 'wav', 'aac', 'flac', // Audio
    'xls', 'xlsx', 'csv', // Spreadsheets
    'ppt', 'pptx', // Presentations
    'psd', 'ai', 'sketch', 'fig', // Design files
    'html', 'css', 'js', 'json', 'xml', // Web files
  ];

  /// Upload a single file to Supabase storage
  static Future<String> uploadFile({
    required PlatformFile file,
    required String bucket,
    required String folder,
    Function(double)? onProgress,
  }) async {
    try {
      // Validate file
      _validateFile(file);

      // Generate unique filename
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = file.extension ?? '';
      final fileName = '${timestamp}_${file.name}';
      final filePath = '$folder/$fileName';

      // Get file bytes
      Uint8List? fileBytes;
      if (file.bytes != null) {
        fileBytes = file.bytes!;
      } else if (file.path != null) {
        final fileObj = File(file.path!);
        fileBytes = await fileObj.readAsBytes();
      } else {
        throw Exception('File data not available');
      }

      // Upload to Supabase storage
      await SupabaseConfig.client.storage
          .from(bucket)
          .uploadBinary(
            filePath,
            fileBytes,
            fileOptions: FileOptions(contentType: _getContentType(extension), upsert: false),
          );

      // Get public URL
      final publicUrl = SupabaseConfig.client.storage.from(bucket).getPublicUrl(filePath);

      return publicUrl;
    } catch (e) {
      throw Exception('Failed to upload file: $e');
    }
  }

  /// Upload multiple files
  static Future<List<String>> uploadFiles({
    required List<PlatformFile> files,
    required String bucket,
    required String folder,
    Function(int, int)? onProgress,
  }) async {
    final List<String> uploadedUrls = [];

    for (int i = 0; i < files.length; i++) {
      try {
        final url = await uploadFile(file: files[i], bucket: bucket, folder: folder);
        uploadedUrls.add(url);
        onProgress?.call(i + 1, files.length);
      } catch (e) {
        // Continue with other files even if one fails
        print('Failed to upload file ${files[i].name}: $e');
      }
    }

    return uploadedUrls;
  }

  /// Upload delivery files for an order
  static Future<List<String>> uploadDeliveryFiles({
    required String orderId,
    required List<PlatformFile> files,
    Function(int, int)? onProgress,
  }) async {
    return uploadFiles(files: files, bucket: 'deliveries', folder: 'orders/$orderId', onProgress: onProgress);
  }

  /// Delete a file from storage
  static Future<void> deleteFile({required String bucket, required String filePath}) async {
    try {
      await SupabaseConfig.client.storage.from(bucket).remove([filePath]);
    } catch (e) {
      throw Exception('Failed to delete file: $e');
    }
  }

  /// Validate file before upload
  static void _validateFile(PlatformFile file) {
    // Check file size
    if (file.size > maxFileSize) {
      throw Exception('File size exceeds maximum limit of ${maxFileSize ~/ (1024 * 1024)}MB');
    }

    // Check file extension
    final extension = file.extension?.toLowerCase();
    if (extension != null && !allowedExtensions.contains(extension)) {
      throw Exception('File type not allowed. Allowed types: ${allowedExtensions.join(', ')}');
    }

    // Check if file has content
    if (file.size == 0) {
      throw Exception('File is empty');
    }
  }

  /// Get content type based on file extension
  static String _getContentType(String extension) {
    switch (extension.toLowerCase()) {
      // Documents
      case 'pdf':
        return 'application/pdf';
      case 'doc':
        return 'application/msword';
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case 'txt':
        return 'text/plain';
      case 'rtf':
        return 'application/rtf';

      // Images
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'bmp':
        return 'image/bmp';
      case 'webp':
        return 'image/webp';

      // Archives
      case 'zip':
        return 'application/zip';
      case 'rar':
        return 'application/x-rar-compressed';
      case '7z':
        return 'application/x-7z-compressed';
      case 'tar':
        return 'application/x-tar';
      case 'gz':
        return 'application/gzip';

      // Videos
      case 'mp4':
        return 'video/mp4';
      case 'avi':
        return 'video/x-msvideo';
      case 'mov':
        return 'video/quicktime';
      case 'wmv':
        return 'video/x-ms-wmv';
      case 'flv':
        return 'video/x-flv';

      // Audio
      case 'mp3':
        return 'audio/mpeg';
      case 'wav':
        return 'audio/wav';
      case 'aac':
        return 'audio/aac';
      case 'flac':
        return 'audio/flac';

      // Spreadsheets
      case 'xls':
        return 'application/vnd.ms-excel';
      case 'xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case 'csv':
        return 'text/csv';

      // Presentations
      case 'ppt':
        return 'application/vnd.ms-powerpoint';
      case 'pptx':
        return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';

      // Web files
      case 'html':
        return 'text/html';
      case 'css':
        return 'text/css';
      case 'js':
        return 'application/javascript';
      case 'json':
        return 'application/json';
      case 'xml':
        return 'application/xml';

      default:
        return 'application/octet-stream';
    }
  }

  /// Format file size for display
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// Get file icon based on extension
  static String getFileIcon(String? extension) {
    switch (extension?.toLowerCase()) {
      case 'pdf':
        return '📄';
      case 'doc':
      case 'docx':
        return '📝';
      case 'txt':
        return '📄';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        return '🖼️';
      case 'zip':
      case 'rar':
      case '7z':
      case 'tar':
      case 'gz':
        return '🗜️';
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
      case 'flv':
        return '🎥';
      case 'mp3':
      case 'wav':
      case 'aac':
      case 'flac':
        return '🎵';
      case 'xls':
      case 'xlsx':
      case 'csv':
        return '📊';
      case 'ppt':
      case 'pptx':
        return '📊';
      default:
        return '📎';
    }
  }
}

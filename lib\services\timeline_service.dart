import '../models/message_model.dart';
import 'chat_service.dart';

/// Service for managing timeline messages in chat conversations
class TimelineService {
  /// Create timeline message for order creation
  static Future<void> createOrderCreatedTimeline({
    required String chatId,
    required String orderId,
    required double amount,
    required String serviceTitle,
  }) async {
    final content = 'Order #$orderId created for "$serviceTitle" - \$${amount.toStringAsFixed(2)}';

    await _createTimelineMessage(
      chatId: chatId,
      content: content,
      metadata: {
        'type': 'order_created',
        'order_id': orderId,
        'amount': amount,
        'service_title': serviceTitle,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Create timeline message for order acceptance
  static Future<void> createOrderAcceptedTimeline({
    required String chatId,
    required String orderId,
    required String freelancerName,
  }) async {
    final content = 'Order #$orderId accepted by $freelancerName';

    await _createTimelineMessage(
      chatId: chatId,
      content: content,
      metadata: {
        'type': 'order_accepted',
        'order_id': orderId,
        'freelancer_name': freelancerName,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Create timeline message for payment confirmation
  static Future<void> createPaymentConfirmedTimeline({
    required String chatId,
    required String orderId,
    required double amount,
  }) async {
    final content = 'Payment confirmed for Order #$orderId - \$${amount.toStringAsFixed(2)}';

    await _createTimelineMessage(
      chatId: chatId,
      content: content,
      metadata: {
        'type': 'payment_confirmed',
        'order_id': orderId,
        'amount': amount,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Create timeline message for task submission
  static Future<void> createTaskSubmittedTimeline({required String chatId, required String orderId}) async {
    await ChatService.sendSystemMessage(chatId, 'Freelancer has submitted the task for review.');
  }

  /// Create timeline message for work delivery
  static Future<void> createWorkDeliveredTimeline({
    required String chatId,
    required String orderId,
    required String deliveryNotes,
    List<String>? fileUrls,
  }) async {
    final content = 'Work delivered for Order #$orderId';

    await _createTimelineMessage(
      chatId: chatId,
      content: content,
      metadata: {
        'type': 'work_delivered',
        'order_id': orderId,
        'delivery_notes': deliveryNotes,
        'file_urls': fileUrls,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Create timeline message for delivery confirmation
  static Future<void> createDeliveryConfirmationTimeline({
    required String chatId,
    required String orderId,
    required String deliveryNotes,
    List<String>? fileUrls,
  }) async {
    final content = 'Freelancer confirmed delivery for Order #$orderId';

    await _createTimelineMessage(
      chatId: chatId,
      content: content,
      metadata: {
        'type': 'delivery_confirmation',
        'order_id': orderId,
        'delivery_notes': deliveryNotes,
        'file_urls': fileUrls,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Create timeline message for receipt confirmation
  static Future<void> createReceiptConfirmationTimeline({
    required String chatId,
    required String orderId,
    double? rating,
    String? review,
  }) async {
    final content = 'Client confirmed receipt for Order #$orderId';

    await _createTimelineMessage(
      chatId: chatId,
      content: content,
      metadata: {
        'type': 'receipt_confirmation',
        'order_id': orderId,
        'rating': rating,
        'review': review,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Create timeline message for delivery reminder
  static Future<void> createDeliveryReminderTimeline({
    required String chatId,
    required String orderId,
    required String reminderType,
  }) async {
    final content =
        reminderType == 'first'
            ? 'First reminder sent to client for Order #$orderId'
            : 'Urgent reminder sent to client for Order #$orderId';

    await _createTimelineMessage(
      chatId: chatId,
      content: content,
      metadata: {
        'type': 'delivery_reminder',
        'order_id': orderId,
        'reminder_type': reminderType,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Create timeline message for auto-confirmation
  static Future<void> createAutoConfirmationTimeline({required String chatId, required String orderId}) async {
    final content = 'Order #$orderId auto-confirmed after deadline';

    await _createTimelineMessage(
      chatId: chatId,
      content: content,
      metadata: {'type': 'auto_confirmation', 'order_id': orderId, 'timestamp': DateTime.now().toIso8601String()},
    );
  }

  /// Create timeline message for manual reminder
  static Future<void> createManualReminderTimeline({required String chatId, required String orderId}) async {
    final content = 'Freelancer sent reminder for Order #$orderId';

    await _createTimelineMessage(
      chatId: chatId,
      content: content,
      metadata: {'type': 'manual_reminder', 'order_id': orderId, 'timestamp': DateTime.now().toIso8601String()},
    );
  }

  /// Create timeline message for order completion
  static Future<void> createOrderCompletedTimeline({
    required String chatId,
    required String orderId,
    double? rating,
    String? review,
  }) async {
    final content = 'Order #$orderId completed successfully';

    await _createTimelineMessage(
      chatId: chatId,
      content: content,
      metadata: {
        'type': 'order_completed',
        'order_id': orderId,
        'rating': rating,
        'review': review,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Create timeline message for order cancellation
  static Future<void> createOrderCancelledTimeline({
    required String chatId,
    required String orderId,
    required String reason,
  }) async {
    final content = 'Order #$orderId cancelled - $reason';

    await _createTimelineMessage(
      chatId: chatId,
      content: content,
      metadata: {
        'type': 'order_cancelled',
        'order_id': orderId,
        'reason': reason,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Create timeline message for revision request
  static Future<void> createRevisionRequestedTimeline({
    required String chatId,
    required String orderId,
    required String revisionNotes,
  }) async {
    final content = 'Revision requested for Order #$orderId';

    await _createTimelineMessage(
      chatId: chatId,
      content: content,
      metadata: {
        'type': 'revision_requested',
        'order_id': orderId,
        'revision_notes': revisionNotes,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Create timeline message for deadline extension
  static Future<void> createDeadlineExtendedTimeline({
    required String chatId,
    required String orderId,
    required DateTime newDeadline,
    required String reason,
  }) async {
    final content = 'Deadline extended for Order #$orderId';

    await _createTimelineMessage(
      chatId: chatId,
      content: content,
      metadata: {
        'type': 'deadline_extended',
        'order_id': orderId,
        'new_deadline': newDeadline.toIso8601String(),
        'reason': reason,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Create timeline message for milestone reached
  static Future<void> createMilestoneReachedTimeline({
    required String chatId,
    required String orderId,
    required String milestoneName,
    required String description,
  }) async {
    final content = 'Milestone reached: $milestoneName';

    await _createTimelineMessage(
      chatId: chatId,
      content: content,
      metadata: {
        'type': 'milestone_reached',
        'order_id': orderId,
        'milestone_name': milestoneName,
        'description': description,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Create a generic timeline message
  static Future<void> _createTimelineMessage({
    required String chatId,
    required String content,
    required Map<String, dynamic> metadata,
  }) async {
    try {
      final message = MessageModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        chatId: chatId,
        senderId: 'system',
        content: content,
        type: MessageType.timeline,
        createdAt: DateTime.now(),
        metadata: metadata,
      );

      await ChatService.sendMessage(message);
    } catch (e) {
      print('Failed to create timeline message: $e');
    }
  }

  /// Get localized timeline message content
  static String getLocalizedTimelineContent(MessageModel message, String languageCode) {
    if (message.metadata == null) return message.content;

    final type = message.metadata!['type'] as String?;
    final orderId = message.metadata!['order_id'] as String?;

    if (languageCode == 'ar') {
      switch (type) {
        case 'order_created':
          final amount = message.metadata!['amount'] as double?;
          final serviceTitle = message.metadata!['service_title'] as String?;
          return 'تم إنشاء الطلب #$orderId لـ "$serviceTitle" - \$${amount?.toStringAsFixed(2)}';
        case 'order_accepted':
          final freelancerName = message.metadata!['freelancer_name'] as String?;
          return 'تم قبول الطلب #$orderId من قبل $freelancerName';
        case 'payment_confirmed':
          final amount = message.metadata!['amount'] as double?;
          return 'تم تأكيد الدفع للطلب #$orderId - \$${amount?.toStringAsFixed(2)}';
        case 'work_delivered':
          return 'تم تسليم العمل للطلب #$orderId';
        case 'order_completed':
          return 'تم إكمال الطلب #$orderId بنجاح';
        case 'order_cancelled':
          final reason = message.metadata!['reason'] as String?;
          return 'تم إلغاء الطلب #$orderId - $reason';
        case 'revision_requested':
          return 'تم طلب مراجعة للطلب #$orderId';
        case 'deadline_extended':
          return 'تم تمديد الموعد النهائي للطلب #$orderId';
        case 'milestone_reached':
          final milestoneName = message.metadata!['milestone_name'] as String?;
          return 'تم الوصول إلى المعلم: $milestoneName';
        default:
          return message.content;
      }
    } else {
      // English content is already in the original message
      return message.content;
    }
  }

  /// Get timeline message icon based on type
  static String getTimelineIcon(String? type) {
    switch (type) {
      case 'order_created':
        return '📋';
      case 'order_accepted':
        return '✅';
      case 'payment_confirmed':
        return '💳';
      case 'work_delivered':
        return '📦';
      case 'order_completed':
        return '🎉';
      case 'order_cancelled':
        return '❌';
      case 'revision_requested':
        return '🔄';
      case 'deadline_extended':
        return '⏰';
      case 'milestone_reached':
        return '🎯';
      default:
        return '📌';
    }
  }

  /// Get timeline message color based on type
  static String getTimelineColor(String? type) {
    switch (type) {
      case 'order_created':
        return 'blue';
      case 'order_accepted':
        return 'green';
      case 'payment_confirmed':
        return 'green';
      case 'work_delivered':
        return 'orange';
      case 'order_completed':
        return 'green';
      case 'order_cancelled':
        return 'red';
      case 'revision_requested':
        return 'orange';
      case 'deadline_extended':
        return 'blue';
      case 'milestone_reached':
        return 'purple';
      default:
        return 'grey';
    }
  }
}

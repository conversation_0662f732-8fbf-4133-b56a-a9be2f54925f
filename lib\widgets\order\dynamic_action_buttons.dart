import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:file_picker/file_picker.dart';
import '../../models/order_model.dart';
import '../../providers/theme_provider.dart';
import '../../widgets/common/enhanced_widgets.dart';
import '../../utils/app_localizations.dart';
import '../../services/order_service.dart';

class DynamicActionButtons extends StatelessWidget {
  final OrderModel order;
  final bool isArabic;
  final bool isDark;
  final AppLocalizations l10n;
  final VoidCallback? onPaymentUpload;
  final VoidCallback? onAcceptWork;
  final VoidCallback? onRequestRevision;
  final VoidCallback? onRateFreelancer;
  final VoidCallback? onCancelOrder;
  final VoidCallback? onChat;
  final VoidCallback? onViewDetails;

  const DynamicActionButtons({
    super.key,
    required this.order,
    required this.isArabic,
    required this.isDark,
    required this.l10n,
    this.onPaymentUpload,
    this.onAcceptWork,
    this.onRequestRevision,
    this.onRateFreelancer,
    this.onCancelOrder,
    this.onChat,
    this.onViewDetails,
  });

  @override
  Widget build(BuildContext context) {
    final actions = _getActionsForStatus();

    if (actions.isEmpty) {
      return const SizedBox.shrink();
    }

    return EnhancedCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'الإجراءات المتاحة' : 'Available Actions',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          ...actions.map((action) => _buildActionButton(context, action)),
        ],
      ),
    );
  }

  Widget _buildActionButton(BuildContext context, ActionButtonConfig action) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      width: double.infinity,
      child: EnhancedButton(
        text: action.text,
        icon: action.icon,
        backgroundColor: action.color,
        onPressed: action.isEnabled ? () => _handleAction(context, action.type) : null,
        isLoading: false,
      ),
    );
  }

  List<ActionButtonConfig> _getActionsForStatus() {
    List<ActionButtonConfig> actions = [];

    switch (order.status) {
      case OrderStatus.created:
      case OrderStatus.paymentPending:
        actions.addAll([
          ActionButtonConfig(
            type: ActionType.uploadPayment,
            text: isArabic ? 'رفع إثبات الدفع' : 'Upload Payment Proof',
            icon: Icons.upload_file,
            color: ThemeProvider.warningOrange,
            isEnabled: true,
          ),
          ActionButtonConfig(
            type: ActionType.cancelOrder,
            text: isArabic ? 'إلغاء الطلب' : 'Cancel Order',
            icon: Icons.cancel,
            color: Colors.red,
            isEnabled: true,
          ),
        ]);
        break;

      case OrderStatus.paymentConfirmed:
      case OrderStatus.inProgress:
        actions.addAll([
          ActionButtonConfig(
            type: ActionType.chat,
            text: isArabic ? 'محادثة المستقل' : 'Chat with Freelancer',
            icon: Icons.chat,
            color: ThemeProvider.primaryBlue,
            isEnabled: true,
          ),
          ActionButtonConfig(
            type: ActionType.viewDetails,
            text: isArabic ? 'تفاصيل الطلب' : 'View Order Details',
            icon: Icons.visibility,
            color: Colors.grey[600]!,
            isEnabled: true,
          ),
        ]);
        break;

      case OrderStatus.submitted:
        actions.addAll([
          ActionButtonConfig(
            type: ActionType.chat,
            text: isArabic ? 'محادثة المستقل' : 'Chat with Freelancer',
            icon: Icons.chat,
            color: ThemeProvider.primaryBlue,
            isEnabled: true,
          ),
          ActionButtonConfig(
            type: ActionType.viewDetails,
            text: isArabic ? 'تفاصيل الطلب' : 'View Order Details',
            icon: Icons.visibility,
            color: Colors.grey[600]!,
            isEnabled: true,
          ),
        ]);
        break;

      case OrderStatus.delivered:
        actions.addAll([
          ActionButtonConfig(
            type: ActionType.acceptWork,
            text: isArabic ? 'قبول العمل' : 'Accept Work',
            icon: Icons.check_circle,
            color: ThemeProvider.successGreen,
            isEnabled: true,
          ),
          ActionButtonConfig(
            type: ActionType.requestRevision,
            text: isArabic ? 'طلب مراجعة' : 'Request Revision',
            icon: Icons.edit,
            color: ThemeProvider.warningOrange,
            isEnabled: order.revisionCount < 3, // Limit revisions
          ),
          ActionButtonConfig(
            type: ActionType.chat,
            text: isArabic ? 'محادثة المستقل' : 'Chat with Freelancer',
            icon: Icons.chat,
            color: ThemeProvider.primaryBlue,
            isEnabled: true,
          ),
        ]);
        break;

      case OrderStatus.editing:
        actions.addAll([
          ActionButtonConfig(
            type: ActionType.chat,
            text: isArabic ? 'محادثة المستقل' : 'Chat with Freelancer',
            icon: Icons.chat,
            color: ThemeProvider.primaryBlue,
            isEnabled: true,
          ),
          ActionButtonConfig(
            type: ActionType.viewDetails,
            text: isArabic ? 'تفاصيل المراجعة' : 'View Revision Details',
            icon: Icons.visibility,
            color: Colors.purple,
            isEnabled: true,
          ),
        ]);
        break;

      case OrderStatus.completed:
        if (order.clientRating == null) {
          actions.add(
            ActionButtonConfig(
              type: ActionType.rateFreelancer,
              text: isArabic ? 'تقييم المستقل' : 'Rate Freelancer',
              icon: Icons.star,
              color: Colors.amber,
              isEnabled: true,
            ),
          );
        }
        actions.add(
          ActionButtonConfig(
            type: ActionType.viewDetails,
            text: isArabic ? 'تفاصيل الطلب' : 'View Order Details',
            icon: Icons.visibility,
            color: Colors.grey[600]!,
            isEnabled: true,
          ),
        );
        break;

      case OrderStatus.cancelled:
        actions.add(
          ActionButtonConfig(
            type: ActionType.viewDetails,
            text: isArabic ? 'تفاصيل الطلب' : 'View Order Details',
            icon: Icons.visibility,
            color: Colors.grey[600]!,
            isEnabled: true,
          ),
        );
        break;
    }

    return actions;
  }

  void _handleAction(BuildContext context, ActionType type) {
    // Add haptic feedback
    HapticFeedback.lightImpact();

    switch (type) {
      case ActionType.uploadPayment:
        if (onPaymentUpload != null) {
          onPaymentUpload!();
        } else {
          _showPaymentUploadDialog(context);
        }
        break;
      case ActionType.acceptWork:
        if (onAcceptWork != null) {
          onAcceptWork!();
        } else {
          _showAcceptWorkDialog(context);
        }
        break;
      case ActionType.requestRevision:
        if (onRequestRevision != null) {
          onRequestRevision!();
        } else {
          _showRevisionDialog(context);
        }
        break;
      case ActionType.rateFreelancer:
        if (onRateFreelancer != null) {
          onRateFreelancer!();
        } else {
          _showRatingDialog(context);
        }
        break;
      case ActionType.cancelOrder:
        if (onCancelOrder != null) {
          onCancelOrder!();
        } else {
          _showCancelDialog(context);
        }
        break;
      case ActionType.chat:
        if (onChat != null) {
          onChat!();
        }
        break;
      case ActionType.viewDetails:
        if (onViewDetails != null) {
          onViewDetails!();
        }
        break;
    }
  }

  void _showPaymentUploadDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(isArabic ? 'رفع إثبات الدفع' : 'Upload Payment Proof'),
            content: Text(
              isArabic
                  ? 'يرجى رفع صورة أو ملف يثبت عملية الدفع'
                  : 'Please upload an image or file that proves your payment',
            ),
            actions: [
              TextButton(onPressed: () => Navigator.pop(context), child: Text(isArabic ? 'إلغاء' : 'Cancel')),
              ElevatedButton(
                onPressed: () async {
                  Navigator.pop(context);
                  await _pickPaymentProofFile(context);
                },
                child: Text(isArabic ? 'اختيار ملف' : 'Choose File'),
              ),
            ],
          ),
    );
  }

  Future<void> _pickPaymentProofFile(BuildContext context) async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        PlatformFile file = result.files.first;

        // Show success message
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(isArabic ? 'تم اختيار الملف: ${file.name}' : 'File selected: ${file.name}'),
              backgroundColor: ThemeProvider.successGreen,
            ),
          );

          // Call the callback if provided
          if (onPaymentUpload != null) {
            onPaymentUpload!();
          }
        }
      } else {
        // User canceled the picker
        if (context.mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(isArabic ? 'لم يتم اختيار أي ملف' : 'No file selected')));
        }
      }
    } catch (e) {
      // Handle error
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isArabic ? 'حدث خطأ أثناء اختيار الملف' : 'Error occurred while selecting file'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showAcceptWorkDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(isArabic ? 'قبول العمل' : 'Accept Work'),
            content: Text(
              isArabic
                  ? 'هل أنت متأكد من قبول العمل المسلم؟ لن تتمكن من التراجع بعد ذلك.'
                  : 'Are you sure you want to accept the delivered work? You won\'t be able to undo this action.',
            ),
            actions: [
              TextButton(onPressed: () => Navigator.pop(context), child: Text(isArabic ? 'إلغاء' : 'Cancel')),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _acceptWork(context);
                },
                style: ElevatedButton.styleFrom(backgroundColor: ThemeProvider.successGreen),
                child: Text(isArabic ? 'قبول' : 'Accept'),
              ),
            ],
          ),
    );
  }

  void _showRevisionDialog(BuildContext context) {
    final controller = TextEditingController();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(isArabic ? 'طلب مراجعة' : 'Request Revision'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(isArabic ? 'يرجى توضيح التعديلات المطلوبة' : 'Please specify the required changes'),
                const SizedBox(height: 16),
                TextField(
                  controller: controller,
                  maxLines: 4,
                  decoration: InputDecoration(
                    hintText: isArabic ? 'اكتب ملاحظاتك هنا...' : 'Write your notes here...',
                    border: const OutlineInputBorder(),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(onPressed: () => Navigator.pop(context), child: Text(isArabic ? 'إلغاء' : 'Cancel')),
              ElevatedButton(
                onPressed: () async {
                  final revisionNotes = controller.text.trim();
                  if (revisionNotes.isEmpty) {
                    // Show error if no notes provided
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(isArabic ? 'يرجى كتابة ملاحظات المراجعة' : 'Please provide revision notes'),
                        backgroundColor: Colors.red,
                      ),
                    );
                    return;
                  }

                  Navigator.pop(context);

                  try {
                    // Show loading indicator
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Row(
                          children: [
                            const SizedBox(width: 20, height: 20, child: CircularProgressIndicator(strokeWidth: 2)),
                            const SizedBox(width: 16),
                            Text(isArabic ? 'جاري إرسال طلب المراجعة...' : 'Sending revision request...'),
                          ],
                        ),
                        duration: const Duration(seconds: 2),
                      ),
                    );

                    // Call the order service to request revision
                    await OrderService.requestRevision(order.id, revisionNotes);

                    // Show success message
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            isArabic ? 'تم إرسال طلب المراجعة بنجاح' : 'Revision request sent successfully',
                          ),
                          backgroundColor: ThemeProvider.successGreen,
                        ),
                      );
                    }
                  } catch (e) {
                    // Show error message
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(isArabic ? 'فشل في إرسال طلب المراجعة' : 'Failed to send revision request'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                },
                style: ElevatedButton.styleFrom(backgroundColor: ThemeProvider.warningOrange),
                child: Text(isArabic ? 'إرسال' : 'Send'),
              ),
            ],
          ),
    );
  }

  void _showRatingDialog(BuildContext context) {
    int selectedRating = 0;
    final reviewController = TextEditingController();
    bool isSubmitting = false;

    showDialog(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setState) => AlertDialog(
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                  title: Row(
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(color: Colors.amber.withValues(alpha: 0.1), shape: BoxShape.circle),
                        child: const Icon(Icons.star, color: Colors.amber, size: 20),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          isArabic ? 'تقييم المستقل' : 'Rate Freelancer',
                          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ],
                  ),
                  content: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          isArabic
                              ? 'كيف كانت تجربتك مع هذا المستقل؟'
                              : 'How was your experience with this freelancer?',
                          style: TextStyle(color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
                        ),
                        const SizedBox(height: 20),

                        // Rating Stars
                        Text(
                          isArabic ? 'التقييم:' : 'Rating:',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          children: List.generate(5, (index) {
                            return GestureDetector(
                              onTap: () {
                                setState(() {
                                  selectedRating = index + 1;
                                });
                                HapticFeedback.lightImpact();
                              },
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                child: Icon(
                                  index < selectedRating ? Icons.star : Icons.star_border,
                                  color: Colors.amber,
                                  size: 32,
                                ),
                              ),
                            );
                          }),
                        ),

                        if (selectedRating > 0) ...[
                          const SizedBox(height: 8),
                          Text(
                            _getRatingText(selectedRating),
                            style: TextStyle(fontWeight: FontWeight.w600, color: _getRatingColor(selectedRating)),
                          ),
                        ],

                        const SizedBox(height: 20),

                        // Review Text Field
                        Text(
                          isArabic ? 'التعليق (اختياري):' : 'Review (Optional):',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 12),
                        TextField(
                          controller: reviewController,
                          maxLines: 4,
                          textAlign: isArabic ? TextAlign.right : TextAlign.left,
                          decoration: InputDecoration(
                            hintText:
                                isArabic ? 'شاركنا رأيك في العمل المنجز...' : 'Share your thoughts about the work...',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(color: Colors.grey[300]!),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: const BorderSide(color: ThemeProvider.primaryBlue),
                            ),
                            filled: true,
                            fillColor: isDark ? ThemeProvider.darkCardBackground : Colors.grey[50],
                          ),
                        ),
                      ],
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: isSubmitting ? null : () => Navigator.of(context).pop(),
                      child: Text(
                        isArabic ? 'إلغاء' : 'Cancel',
                        style: TextStyle(color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
                      ),
                    ),
                    ElevatedButton(
                      onPressed:
                          (selectedRating > 0 && !isSubmitting)
                              ? () async {
                                setState(() {
                                  isSubmitting = true;
                                });

                                try {
                                  await OrderService.completeOrder(
                                    order.id,
                                    selectedRating.toDouble(),
                                    reviewController.text.trim().isEmpty ? null : reviewController.text.trim(),
                                  );

                                  if (context.mounted) {
                                    Navigator.of(context).pop();
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          isArabic ? 'تم إرسال التقييم بنجاح' : 'Rating submitted successfully',
                                        ),
                                        backgroundColor: Colors.green,
                                      ),
                                    );
                                  }
                                } catch (e) {
                                  if (context.mounted) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          isArabic ? 'حدث خطأ في إرسال التقييم' : 'Failed to submit rating',
                                        ),
                                        backgroundColor: Colors.red,
                                      ),
                                    );
                                  }
                                } finally {
                                  setState(() {
                                    isSubmitting = false;
                                  });
                                }
                              }
                              : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ThemeProvider.primaryBlue,
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                      ),
                      child:
                          isSubmitting
                              ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )
                              : Text(
                                isArabic ? 'إرسال التقييم' : 'Submit Rating',
                                style: const TextStyle(color: Colors.white),
                              ),
                    ),
                  ],
                ),
          ),
    );
  }

  void _showCancelDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(isArabic ? 'إلغاء الطلب' : 'Cancel Order'),
            content: Text(
              isArabic ? 'هل أنت متأكد من إلغاء هذا الطلب؟' : 'Are you sure you want to cancel this order?',
            ),
            actions: [
              TextButton(onPressed: () => Navigator.pop(context), child: Text(isArabic ? 'لا' : 'No')),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _cancelOrder(context);
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: Text(isArabic ? 'نعم، إلغاء' : 'Yes, Cancel'),
              ),
            ],
          ),
    );
  }

  Future<void> _acceptWork(BuildContext context) async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Row(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 20),
              Text(isArabic ? 'جاري قبول العمل...' : 'Accepting work...'),
            ],
          ),
        );
      },
    );

    try {
      // Accept the work by completing the order with a default rating
      // Note: In a real app, you might want to show a rating dialog first
      await OrderService.completeOrder(
        order.id,
        5.0, // Default rating - could be made configurable
        isArabic ? 'تم قبول العمل' : 'Work accepted',
      );

      if (context.mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isArabic
                  ? 'تم قبول العمل بنجاح! يمكنك الآن تقييم المستقل.'
                  : 'Work accepted successfully! You can now rate the freelancer.',
            ),
            backgroundColor: ThemeProvider.successGreen,
            duration: const Duration(seconds: 3),
          ),
        );

        // Call the callback if provided
        if (onAcceptWork != null) {
          onAcceptWork!();
        }
      }
    } catch (e) {
      if (context.mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isArabic
                  ? 'حدث خطأ أثناء قبول العمل. يرجى المحاولة مرة أخرى.'
                  : 'An error occurred while accepting the work. Please try again.',
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Future<void> _cancelOrder(BuildContext context) async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Row(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 20),
              Text(isArabic ? 'جاري إلغاء الطلب...' : 'Cancelling order...'),
            ],
          ),
        );
      },
    );

    try {
      // Cancel the order
      await OrderService.cancelOrder(order.id, isArabic ? 'تم إلغاء الطلب من قبل العميل' : 'Order cancelled by client');

      if (context.mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isArabic ? 'تم إلغاء الطلب بنجاح.' : 'Order cancelled successfully.'),
            backgroundColor: ThemeProvider.warningOrange,
            duration: const Duration(seconds: 3),
          ),
        );

        // Call the callback if provided
        if (onCancelOrder != null) {
          onCancelOrder!();
        }
      }
    } catch (e) {
      if (context.mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isArabic
                  ? 'حدث خطأ أثناء إلغاء الطلب. يرجى المحاولة مرة أخرى.'
                  : 'An error occurred while cancelling the order. Please try again.',
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  String _getRatingText(int rating) {
    switch (rating) {
      case 1:
        return isArabic ? 'ضعيف' : 'Poor';
      case 2:
        return isArabic ? 'مقبول' : 'Fair';
      case 3:
        return isArabic ? 'جيد' : 'Good';
      case 4:
        return isArabic ? 'جيد جداً' : 'Very Good';
      case 5:
        return isArabic ? 'ممتاز' : 'Excellent';
      default:
        return '';
    }
  }

  Color _getRatingColor(int rating) {
    switch (rating) {
      case 1:
      case 2:
        return Colors.red;
      case 3:
        return Colors.orange;
      case 4:
      case 5:
        return Colors.green;
      default:
        return Colors.grey;
    }
  }
}

class ActionButtonConfig {
  final ActionType type;
  final String text;
  final IconData icon;
  final Color color;
  final bool isEnabled;

  ActionButtonConfig({
    required this.type,
    required this.text,
    required this.icon,
    required this.color,
    required this.isEnabled,
  });
}

enum ActionType { uploadPayment, acceptWork, requestRevision, rateFreelancer, cancelOrder, chat, viewDetails }

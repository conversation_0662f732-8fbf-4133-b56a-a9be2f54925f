import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:record/record.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:io';
import '../../providers/language_provider.dart';
import '../../providers/demo_auth_provider.dart';
import '../../models/user_model.dart';
import '../../services/storage_service.dart';
import '../../models/order_model.dart';
import '../../services/order_service.dart';
import '../../widgets/chat/delivery_confirmation_widget.dart';
import '../../widgets/chat/receipt_confirmation_widget.dart';
import '../../widgets/chat/task_submission_button.dart';
import '../chat/payment_chat_screen.dart';

class ChatScreen extends StatefulWidget {
  final String chatId;
  final String recipientName;
  final String requestTitle;
  final bool isAdminChat;
  final String? orderId; // Add order ID to track delivery status

  const ChatScreen({
    super.key,
    required this.chatId,
    required this.recipientName,
    required this.requestTitle,
    this.isAdminChat = false,
    this.orderId,
  });

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final AudioRecorder _audioRecorder = AudioRecorder();

  List<Map<String, dynamic>> _messages = [
    {
      'content': 'Hi! I\'m interested in your project',
      'isMe': false,
      'timestamp': DateTime.now().subtract(const Duration(minutes: 10)),
      'senderName': 'جون العميل',
      'type': 'text',
      'isRead': true,
    },
    {
      'content': 'Great! Can you tell me more about your experience?',
      'isMe': true,
      'timestamp': DateTime.now().subtract(const Duration(minutes: 8)),
      'senderName': 'أنت',
      'type': 'text',
      'isRead': true,
    },
    {
      'content':
          'I have 5+ years of experience in Flutter development. I can complete this project in 2 weeks for \$2,500',
      'isMe': false,
      'timestamp': DateTime.now().subtract(const Duration(minutes: 5)),
      'senderName': 'جون العميل',
      'type': 'text',
      'isRead': true,
    },
    {
      'content': 'That sounds perfect! Let\'s proceed with the payment',
      'isMe': true,
      'timestamp': DateTime.now().subtract(const Duration(minutes: 2)),
      'senderName': 'أنت',
      'type': 'text',
      'isRead': true,
    },
  ];
  bool _isTyping = false;
  bool _isRecording = false;
  bool _otherUserTyping = false;
  OrderModel? _currentOrder;
  bool _isLoadingOrder = false;
  bool _isReceiptConfirmed = false;
  bool _isPaymentConfirmed = false;

  @override
  void initState() {
    super.initState();
    _loadDemoMessages();
    _messageController.addListener(_onTypingChanged);
    _simulateOtherUserTyping();
    _loadOrderIfExists();
  }

  void _onTypingChanged() {
    final isCurrentlyTyping = _messageController.text.isNotEmpty;
    if (isCurrentlyTyping != _isTyping) {
      setState(() {
        _isTyping = isCurrentlyTyping;
      });
    }
  }

  void _simulateOtherUserTyping() {
    // Simulate other user typing occasionally
    Future.delayed(const Duration(seconds: 10), () {
      if (mounted) {
        setState(() {
          _otherUserTyping = true;
        });
        Future.delayed(const Duration(seconds: 3), () {
          if (mounted) {
            setState(() {
              _otherUserTyping = false;
            });
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _audioRecorder.dispose();
    super.dispose();
  }

  Future<void> _loadOrderIfExists() async {
    if (widget.orderId == null) {
      print('=== DEBUG: No orderId provided to ChatScreen ===');
      return;
    }

    print('=== DEBUG: Loading order with ID: ${widget.orderId} ===');
    setState(() {
      _isLoadingOrder = true;
    });

    try {
      final order = await OrderService.getOrderById(widget.orderId!);
      print(
        '=== DEBUG: Loaded order: ${order?.id}, Status: ${order?.status}, PaymentStatus: ${order?.paymentStatus} ===',
      );
      if (mounted) {
        setState(() {
          _currentOrder = order;
          _isLoadingOrder = false;
        });
      }
    } catch (e) {
      print('=== DEBUG: Error loading order: $e ===');
      if (mounted) {
        setState(() {
          _isLoadingOrder = false;
        });
      }
    }
  }

  void _onDeliveryConfirmed() {
    // Refresh order status after delivery confirmation
    print('=== DEBUG: Delivery confirmed, refreshing order ===');
    _loadOrderIfExists();
  }

  void _onReceiptConfirmed() {
    // Refresh order status after receipt confirmation
    _loadOrderIfExists();
  }

  void _handlePayNow() {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    final isArabic = languageProvider.isArabic;

    // Navigate to payment chat screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => PaymentChatScreen(
              requestId: widget.chatId,
              requestTitle: isArabic ? 'دفع للمشروع: ${widget.requestTitle}' : 'Payment for: ${widget.requestTitle}',
              amount: 2500.0, // Demo amount - in real app this would come from order
            ),
      ),
    );
  }

  // This method would be called when admin confirms payment (in real app)
  // For demo purposes, you can call this method to simulate admin confirmation
  void _onAdminPaymentConfirmation() {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    final isArabic = languageProvider.isArabic;

    // Add payment confirmation message to chat
    final confirmationMessage = {
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
      'senderId': 'admin',
      'senderName': isArabic ? 'الإدارة' : 'Admin',
      'content':
          isArabic
              ? '✅ تم تأكيد الدفع من قبل الإدارة! يمكن للمستقل الآن البدء في العمل على مشروعك.'
              : '✅ Payment confirmed by admin! The freelancer can now start working on your project.',
      'timestamp': DateTime.now(),
      'isMe': false,
      'type': 'admin_confirmation',
    };

    setState(() {
      _messages.add(confirmationMessage);
      _isPaymentConfirmed = true; // Disable the pay now button only after admin confirmation
    });

    _scrollToBottom();

    // Show success snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(isArabic ? 'تم تأكيد الدفع من قبل الإدارة' : 'Payment confirmed by admin'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _handleConfirmReceipt() {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    final isArabic = languageProvider.isArabic;

    // Show confirmation dialog
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(isArabic ? 'تأكيد استلام المهمة' : 'Confirm Task Receipt'),
          content: Text(
            isArabic
                ? 'هل أنت متأكد من أنك استلمت المهمة المكتملة وترغب في تأكيد الاستلام؟'
                : 'Are you sure you have received the completed task and want to confirm receipt?',
          ),
          actions: [
            TextButton(onPressed: () => Navigator.of(context).pop(), child: Text(isArabic ? 'إلغاء' : 'Cancel')),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _confirmTaskReceipt();
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
              child: Text(isArabic ? 'تأكيد الاستلام' : 'Confirm Receipt', style: const TextStyle(color: Colors.white)),
            ),
          ],
        );
      },
    );
  }

  void _confirmTaskReceipt() {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    final isArabic = languageProvider.isArabic;

    // Add confirmation message to chat
    final confirmationMessage = {
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
      'senderId': 'system',
      'senderName': isArabic ? 'النظام' : 'System',
      'content':
          isArabic
              ? '✅ تم تأكيد استلام المهمة بنجاح! شكراً لك على استخدام خدماتنا.'
              : '✅ Task receipt confirmed successfully! Thank you for using our services.',
      'timestamp': DateTime.now(),
      'isMe': false,
      'type': 'system',
    };

    setState(() {
      _messages.add(confirmationMessage);
      _isReceiptConfirmed = true; // Disable the button after confirmation
    });

    _scrollToBottom();

    // Show success snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(isArabic ? 'تم تأكيد استلام المهمة بنجاح' : 'Task receipt confirmed successfully'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _loadDemoMessages() {
    setState(() {
      // Check if this is customer service chat
      final isCustomerService =
          widget.chatId == 'customer_service_demo' ||
          widget.recipientName.contains('Customer Service') ||
          widget.recipientName.contains('خدمة العملاء');

      if (isCustomerService) {
        // Customer service welcome messages
        _messages = [
          {
            'id': 'cs_welcome_1',
            'senderId': 'customer_service',
            'senderName': widget.recipientName,
            'content':
                '👋 Welcome to Taskly Customer Service!\n\nI\'m here to help you with:\n• Order questions\n• Payment support\n• Technical issues\n• General inquiries\n\nHow can I assist you today?',
            'timestamp': DateTime.now().subtract(const Duration(minutes: 5)),
            'isMe': false,
          },
          {
            'id': 'cs_welcome_2',
            'senderId': 'customer_service',
            'senderName': widget.recipientName,
            'content':
                '🌟 مرحباً بك في خدمة عملاء تاسكلي!\n\nأنا هنا لمساعدتك في:\n• أسئلة الطلبات\n• دعم المدفوعات\n• المشاكل التقنية\n• الاستفسارات العامة\n\nكيف يمكنني مساعدتك اليوم؟',
            'timestamp': DateTime.now().subtract(const Duration(minutes: 4)),
            'isMe': false,
          },
        ];
      } else if (widget.chatId == 'client_freelancer_demo') {
        // Client-Freelancer chat messages
        _messages = [
          {
            'id': '1',
            'senderId': 'other',
            'senderName': widget.recipientName,
            'content': 'مرحباً! شكراً لك على قبول عرضي لتطوير التطبيق. متى يمكننا البدء؟',
            'timestamp': DateTime.now().subtract(const Duration(hours: 3)),
            'isMe': false,
          },
          {
            'id': '2',
            'senderId': 'me',
            'senderName': 'أنت',
            'content': 'أهلاً وسهلاً! يمكننا البدء فوراً. هل يمكنك إرسال التفاصيل الكاملة للمشروع؟',
            'timestamp': DateTime.now().subtract(const Duration(hours: 2, minutes: 45)),
            'isMe': true,
          },
          {
            'id': '3',
            'senderId': 'other',
            'senderName': widget.recipientName,
            'content':
                'بالطبع! المطلوب تطبيق جوال للخدمات الأكاديمية مع الميزات التالية:\n• نظام تسجيل دخول\n• صفحة الخدمات\n• نظام المحادثة\n• نظام الدفع\n• لوحة تحكم المستخدم',
            'timestamp': DateTime.now().subtract(const Duration(hours: 2, minutes: 30)),
            'isMe': false,
          },
          {
            'id': '4',
            'senderId': 'me',
            'senderName': 'أنت',
            'content': 'ممتاز! سأحتاج إلى أسبوعين لإكمال المشروع. سأرسل لك تحديثات يومية عن التقدم.',
            'timestamp': DateTime.now().subtract(const Duration(hours: 2)),
            'isMe': true,
          },
          {
            'id': '5',
            'senderId': 'other',
            'senderName': widget.recipientName,
            'content': 'رائع! هل تحتاج إلى أي ملفات تصميم أو شعارات؟',
            'timestamp': DateTime.now().subtract(const Duration(hours: 1, minutes: 30)),
            'isMe': false,
          },
          {
            'id': '6',
            'senderId': 'me',
            'senderName': 'أنت',
            'content': 'نعم، سيكون من المفيد الحصول على الشعار وألوان العلامة التجارية.',
            'timestamp': DateTime.now().subtract(const Duration(hours: 1)),
            'isMe': true,
          },
          {
            'id': '7',
            'senderId': 'other',
            'senderName': widget.recipientName,
            'content': 'سأبدأ العمل على المشروع غداً صباحاً',
            'timestamp': DateTime.now().subtract(const Duration(minutes: 5)),
            'isMe': false,
          },
        ];
      } else {
        // Regular chat messages for other chats
        _messages = [
          {
            'id': '1',
            'senderId': widget.isAdminChat ? 'admin' : 'other',
            'senderName': widget.recipientName,
            'content':
                widget.isAdminChat
                    ? 'Hello! I can help you with payment verification.'
                    : 'Hi! I\'m interested in your project.',
            'timestamp': DateTime.now().subtract(const Duration(hours: 2)),
            'isMe': false,
          },
          {
            'id': '2',
            'senderId': 'me',
            'senderName': 'You',
            'content':
                widget.isAdminChat
                    ? 'I need to make a payment for my project.'
                    : 'Great! Can you tell me more about your experience?',
            'timestamp': DateTime.now().subtract(const Duration(hours: 1, minutes: 30)),
            'isMe': true,
          },
          if (!widget.isAdminChat) ...[
            {
              'id': '3',
              'senderId': 'other',
              'senderName': widget.recipientName,
              'content':
                  'I have 5+ years of experience in Flutter development. I can complete this project in 2 weeks for \$2,500.',
              'timestamp': DateTime.now().subtract(const Duration(hours: 1)),
              'isMe': false,
            },
            {
              'id': '4',
              'senderId': 'me',
              'senderName': 'You',
              'content': 'That sounds perfect! Let\'s proceed with the payment.',
              'timestamp': DateTime.now().subtract(const Duration(minutes: 30)),
              'isMe': true,
            },
          ],
        ];
      }

      // Pay Now functionality is now handled by ChatTopActionBar
    });
  }

  void _sendMessage() {
    if (_messageController.text.trim().isEmpty) return;

    final newMessage = {
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
      'senderId': 'me',
      'senderName': 'You',
      'content': _messageController.text.trim(),
      'timestamp': DateTime.now(),
      'isMe': true,
      'isRead': false,
      'type': 'text',
    };

    setState(() {
      _messages.add(newMessage);
      _isTyping = false;
    });

    _messageController.clear();
    _scrollToBottom();

    // Simulate message being read after 2 seconds
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          final index = _messages.indexWhere((m) => m['id'] == newMessage['id']);
          if (index != -1) {
            _messages[index]['isRead'] = true;
          }
        });
      }
    });
  }

  void _startVoiceRecording() async {
    try {
      // Check and request microphone permission
      final permission = await Permission.microphone.request();
      if (permission != PermissionStatus.granted) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('يجب السماح بالوصول للميكروفون لتسجيل الرسائل الصوتية'),
              duration: Duration(seconds: 3),
            ),
          );
        }
        return;
      }

      // Check if recorder is available
      if (await _audioRecorder.hasPermission()) {
        setState(() {
          _isRecording = true;
        });

        // Start recording
        await _audioRecorder.start(
          const RecordConfig(encoder: AudioEncoder.aacLc, bitRate: 128000, sampleRate: 44100),
          path: '${Directory.systemTemp.path}/voice_message_${DateTime.now().millisecondsSinceEpoch}.m4a',
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('بدأ التسجيل... اضغط مرة أخرى للإيقاف'), duration: Duration(seconds: 2)),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isRecording = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في بدء التسجيل: $e'), duration: const Duration(seconds: 3)));
      }
    }
  }

  void _stopVoiceRecording() async {
    try {
      final path = await _audioRecorder.stop();

      if (path != null && mounted) {
        setState(() {
          _isRecording = false;
        });

        // Upload the voice message to storage
        final voiceFile = File(path);
        final voiceUrl = await StorageService.uploadFile(voiceFile, 'voice_messages');

        if (voiceUrl != null) {
          // Add voice message to chat
          final voiceMessage = {
            'id': DateTime.now().millisecondsSinceEpoch.toString(),
            'senderId': 'me',
            'senderName': 'You',
            'content': 'Voice message',
            'timestamp': DateTime.now(),
            'isMe': true,
            'isRead': false,
            'type': 'voice',
            'voiceUrl': voiceUrl,
            'duration': '0:00', // You can calculate actual duration if needed
          };

          setState(() {
            _messages.add(voiceMessage);
          });

          _scrollToBottom();

          // Clean up temporary file
          try {
            await voiceFile.delete();
          } catch (e) {
            // Ignore cleanup errors
          }

          // Simulate message being read after 2 seconds
          Future.delayed(const Duration(seconds: 2), () {
            if (mounted) {
              setState(() {
                final index = _messages.indexWhere((m) => m['id'] == voiceMessage['id']);
                if (index != -1) {
                  _messages[index]['isRead'] = true;
                }
              });
            }
          });

          if (mounted) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(const SnackBar(content: Text('تم إرسال الرسالة الصوتية'), duration: Duration(seconds: 2)));
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(const SnackBar(content: Text('فشل في رفع الرسالة الصوتية'), duration: Duration(seconds: 3)));
          }
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isRecording = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في إيقاف التسجيل: $e'), duration: const Duration(seconds: 3)));
      }
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        appBar: AppBar(
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(widget.recipientName, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
              Text(widget.requestTitle, style: const TextStyle(fontSize: 12, fontWeight: FontWeight.normal)),
            ],
          ),
          actions: [
            PopupMenuButton(
              itemBuilder:
                  (context) => [
                    const PopupMenuItem(
                      value: 'info',
                      child: Row(children: [Icon(Icons.info), SizedBox(width: 8), Text('Project Info')]),
                    ),
                    const PopupMenuItem(
                      value: 'block',
                      child: Row(children: [Icon(Icons.block), SizedBox(width: 8), Text('Block User')]),
                    ),
                  ],
            ),
          ],
        ),
        body: Column(
          children: [
            // Show different interfaces based on user role
            // For demo purposes, you can switch between client and freelancer modes
            // Client Action Bar (uncomment for client interface)
            // ChatTopActionBar(
            //   order: _currentOrder,
            //   isClient: true,
            //   onPayNow: _handlePayNow,
            //   onConfirmSubmission: _triggerReceiptConfirmation,
            // ),

            // Order Loading Indicator
            if (_isLoadingOrder)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  border: Border(
                    bottom: BorderSide(color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2), width: 1),
                  ),
                ),
                child: Row(
                  children: [
                    SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.primary),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      languageProvider.isArabic ? 'جاري تحميل بيانات الطلب...' : 'Loading order details...',
                      style: TextStyle(color: Theme.of(context).colorScheme.onSurfaceVariant, fontSize: 14),
                    ),
                  ],
                ),
              ),

            // Delivery Widgets
            if (_currentOrder != null) ...[
              if (_currentOrder!.status == OrderStatus.paymentConfirmed &&
                  _currentOrder!.paymentStatus == PaymentStatus.confirmed)
                DeliveryConfirmationWidget(order: _currentOrder!, onDeliveryConfirmed: _onDeliveryConfirmed),
              if (_currentOrder!.status == OrderStatus.delivered)
                ReceiptConfirmationWidget(order: _currentOrder!, onReceiptConfirmed: _onReceiptConfirmed),
            ],

            // Client Action Buttons (Pay Now & Confirm Receipt - only for clients)
            Consumer<DemoAuthProvider>(
              builder: (context, authProvider, child) {
                final isClient = authProvider.userProfile?.role == UserRole.client;
                final languageProvider = Provider.of<LanguageProvider>(context);

                if (!isClient || widget.isAdminChat) return const SizedBox.shrink();

                return Container(
                  margin: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
                  child: Row(
                    children: [
                      // Pay Now Button
                      ElevatedButton.icon(
                        onPressed: _isPaymentConfirmed ? null : _handlePayNow,
                        icon: Icon(
                          _isPaymentConfirmed ? Icons.check_circle : Icons.payment,
                          color: _isPaymentConfirmed ? Colors.grey : Colors.white,
                          size: 18,
                        ),
                        label: Text(
                          _isPaymentConfirmed
                              ? (languageProvider.isArabic ? 'تم الدفع' : 'Paid')
                              : (languageProvider.isArabic ? 'ادفع الآن' : 'Pay Now'),
                          style: TextStyle(
                            color: _isPaymentConfirmed ? Colors.grey : Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: _isPaymentConfirmed ? Colors.grey[300] : Colors.green,
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                          elevation: _isPaymentConfirmed ? 0 : 2,
                          minimumSize: const Size(0, 36),
                        ),
                      ),

                      const SizedBox(width: 12),

                      // Confirm Receipt Button
                      ElevatedButton.icon(
                        onPressed: _isReceiptConfirmed ? null : _handleConfirmReceipt,
                        icon: Icon(
                          _isReceiptConfirmed ? Icons.check_circle : Icons.check_circle,
                          color: _isReceiptConfirmed ? Colors.grey : Colors.white,
                          size: 18,
                        ),
                        label: Text(
                          _isReceiptConfirmed
                              ? (languageProvider.isArabic ? 'تم التأكيد' : 'Confirmed')
                              : (languageProvider.isArabic ? 'تأكيد الاستلام' : 'Confirm Receipt'),
                          style: TextStyle(
                            color: _isReceiptConfirmed ? Colors.grey : Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: _isReceiptConfirmed ? Colors.grey[300] : Colors.blue,
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                          elevation: _isReceiptConfirmed ? 0 : 2,
                          minimumSize: const Size(0, 36),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),

            // Messages List with Task Submission Button
            Expanded(
              child: Stack(
                children: [
                  ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(16),
                    itemCount: _messages.length + (_otherUserTyping ? 1 : 0),
                    itemBuilder: (context, index) {
                      if (index == _messages.length && _otherUserTyping) {
                        return _buildTypingIndicator(languageProvider);
                      }
                      final message = _messages[index];
                      return _buildMessageBubble(message, languageProvider);
                    },
                  ),
                  // Task Submission Button (only for freelancers)
                  if (_currentOrder != null)
                    TaskSubmissionButton(
                      order: _currentOrder!,
                      onTaskSubmitted: () {
                        // Refresh order status after task submission
                        _loadOrderIfExists();
                      },
                    ),
                ],
              ),
            ),

            // Message Input
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                border: Border(top: BorderSide(color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2))),
              ),
              child: Row(
                children: [
                  // Message Input Field
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(25),
                        border: Border.all(color: Colors.grey[300]!, width: 1),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: _messageController,
                              decoration: InputDecoration(
                                hintText: languageProvider.isArabic ? 'رسالة' : 'Message',
                                hintStyle: const TextStyle(color: Colors.grey, fontSize: 16),
                                border: InputBorder.none,
                                contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                              ),
                              maxLines: null,
                              textInputAction: TextInputAction.send,
                              onSubmitted: (_) => _sendMessage(),
                              onChanged: (text) {
                                setState(() {
                                  // Trigger rebuild to show/hide send button
                                });
                              },
                            ),
                          ),
                          // Attachment Icon
                          IconButton(
                            icon: Icon(Icons.attach_file, color: Colors.grey[600], size: 24),
                            onPressed: () {
                              // Implement file attachment
                            },
                          ),
                          // Camera Icon
                          IconButton(
                            icon: Icon(Icons.camera_alt_outlined, color: Colors.grey[600], size: 24),
                            onPressed: () {
                              // Implement camera
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  // Voice/Send Button
                  GestureDetector(
                    onTap:
                        _messageController.text.isNotEmpty ? _sendMessage : (_isRecording ? _stopVoiceRecording : null),
                    onLongPress: _messageController.text.isEmpty && !_isRecording ? _startVoiceRecording : null,
                    child: Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color:
                            _isRecording
                                ? Colors.red
                                : (_messageController.text.isNotEmpty
                                    ? Theme.of(context).colorScheme.primary
                                    : Colors.black87),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        _isRecording ? Icons.stop : (_messageController.text.isNotEmpty ? Icons.send : Icons.mic),
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTypingIndicator(LanguageProvider languageProvider) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          CircleAvatar(radius: 16, child: Text(widget.recipientName[0], style: const TextStyle(fontSize: 12))),
          const SizedBox(width: 8),
          Container(
            constraints: const BoxConstraints(maxWidth: 200),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
                bottomRight: Radius.circular(20),
                bottomLeft: Radius.circular(4),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  languageProvider.isArabic ? 'يكتب...' : 'Typing...',
                  style: TextStyle(color: Theme.of(context).colorScheme.onSurfaceVariant, fontStyle: FontStyle.italic),
                ),
                const SizedBox(width: 8),
                SizedBox(
                  width: 20,
                  height: 10,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: List.generate(3, (index) {
                      return AnimatedContainer(
                        duration: Duration(milliseconds: 300 + (index * 100)),
                        width: 4,
                        height: 4,
                        decoration: BoxDecoration(color: Theme.of(context).colorScheme.primary, shape: BoxShape.circle),
                      );
                    }),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(Map<String, dynamic> message, LanguageProvider languageProvider) {
    final isMe = message['isMe'] as bool;
    final timestamp = message['timestamp'] as DateTime;
    final messageType = message['type'] as String? ?? 'text';
    final isRead = message['isRead'] as bool? ?? false;

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isMe) ...[
            CircleAvatar(radius: 16, child: Text(message['senderName'][0], style: const TextStyle(fontSize: 12))),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color:
                    isMe
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(20),
                  topRight: const Radius.circular(20),
                  bottomLeft: Radius.circular(isMe ? 20 : 4),
                  bottomRight: Radius.circular(isMe ? 4 : 20),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Message content
                  if (messageType == 'voice')
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.play_arrow,
                          color:
                              isMe
                                  ? Theme.of(context).colorScheme.onPrimary
                                  : Theme.of(context).colorScheme.onSurfaceVariant,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Container(
                          width: 100,
                          height: 20,
                          decoration: BoxDecoration(
                            color: (isMe
                                    ? Theme.of(context).colorScheme.onPrimary
                                    : Theme.of(context).colorScheme.onSurfaceVariant)
                                .withValues(alpha: 0.3),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Align(
                            alignment: Alignment.centerLeft,
                            child: Container(
                              width: 30,
                              height: 20,
                              decoration: BoxDecoration(
                                color:
                                    isMe
                                        ? Theme.of(context).colorScheme.onPrimary
                                        : Theme.of(context).colorScheme.onSurfaceVariant,
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          message['duration'] ?? '0:02',
                          style: TextStyle(
                            fontSize: 12,
                            color:
                                isMe
                                    ? Theme.of(context).colorScheme.onPrimary
                                    : Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    )
                  else
                    Text(
                      message['content'],
                      style: TextStyle(
                        color:
                            isMe
                                ? Theme.of(context).colorScheme.onPrimary
                                : Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  const SizedBox(height: 4),
                  // Timestamp and read status
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        languageProvider.getTimeAgo(timestamp),
                        style: TextStyle(
                          fontSize: 10,
                          color:
                              isMe
                                  ? Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.7)
                                  : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                      if (isMe) ...[
                        const SizedBox(width: 4),
                        Icon(
                          isRead ? Icons.done_all : Icons.done,
                          size: 12,
                          color: isRead ? Colors.blue : Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.7),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ),
          if (isMe) ...[
            const SizedBox(width: 8),
            CircleAvatar(radius: 16, child: Text(message['senderName'][0], style: const TextStyle(fontSize: 12))),
          ],
        ],
      ),
    );
  }
}

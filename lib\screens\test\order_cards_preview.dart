import 'package:flutter/material.dart';
import '../../models/order_model.dart';
import '../../providers/language_provider.dart';
import '../../providers/theme_provider.dart';
import '../../widgets/common/enhanced_widgets.dart';
import 'package:provider/provider.dart';

class OrderCardsPreviewScreen extends StatelessWidget {
  const OrderCardsPreviewScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer2<LanguageProvider, ThemeProvider>(
      builder: (context, languageProvider, themeProvider, child) {
        final isArabic = languageProvider.isArabic;
        final isDark = themeProvider.isDarkMode;

        return Directionality(
          textDirection: languageProvider.textDirection,
          child: Scaffold(
            backgroundColor: isDark ? ThemeProvider.darkBackground : ThemeProvider.lightBackground,
            appBar: AppBar(
              title: Text(isArabic ? 'عرض بطاقات الطلبات' : 'Order Cards Preview'),
              backgroundColor: isDark ? ThemeProvider.darkCardBackground : Colors.white,
            ),
            body: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                Text(
                  isArabic ? 'أمثلة على بطاقات الطلبات' : 'Order Cards Examples',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                  ),
                ),
                const SizedBox(height: 20),

                // Payment Pending Order
                _buildDemoOrderCard(
                  context,
                  _createDemoOrder('ORD001', 150.0, OrderStatus.paymentPending),
                  isArabic,
                  isDark,
                ),

                // In Progress Order
                _buildDemoOrderCard(
                  context,
                  _createDemoOrder('ORD002', 300.0, OrderStatus.inProgress),
                  isArabic,
                  isDark,
                ),

                // Submitted Order
                _buildDemoOrderCard(context, _createDemoOrder('ORD003', 75.0, OrderStatus.submitted), isArabic, isDark),

                // Delivered Order
                _buildDemoOrderCard(
                  context,
                  _createDemoOrder('ORD004', 200.0, OrderStatus.delivered),
                  isArabic,
                  isDark,
                ),

                // Under Revision Order
                _buildDemoOrderCard(context, _createDemoOrder('ORD005', 120.0, OrderStatus.editing), isArabic, isDark),

                // Completed Order
                _buildDemoOrderCard(
                  context,
                  _createDemoOrder('ORD006', 250.0, OrderStatus.completed),
                  isArabic,
                  isDark,
                ),

                // Cancelled Order
                _buildDemoOrderCard(
                  context,
                  _createDemoOrder('ORD007', 100.0, OrderStatus.cancelled),
                  isArabic,
                  isDark,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  OrderModel _createDemoOrder(String id, double amount, OrderStatus status) {
    return OrderModel(
      id: id,
      clientId: 'client123',
      freelancerId: 'freelancer456',
      requestId: 'req$id',
      offerId: 'offer$id',
      amount: amount,
      status: status,
      createdAt: DateTime.now().subtract(Duration(days: (id.hashCode % 10) + 1)),
      updatedAt: DateTime.now().subtract(Duration(hours: (id.hashCode % 24) + 1)),
    );
  }

  Widget _buildDemoOrderCard(BuildContext context, OrderModel order, bool isArabic, bool isDark) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: EnhancedCard(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isArabic ? 'طلب #${order.id}' : 'Order #${order.id}',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '\$${order.amount.toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: ThemeProvider.primaryBlue,
                        ),
                      ),
                    ],
                  ),
                ),
                _buildStatusBadge(order.status, isArabic),
              ],
            ),

            const SizedBox(height: 16),

            // Service Title
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isDark ? ThemeProvider.darkCardBackground.withValues(alpha: 0.5) : Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.work_outline,
                    size: 20,
                    color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _getServiceTitle(order.id, isArabic),
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Progress Timeline
            _buildMiniTimeline(order, isArabic, isDark),

            const SizedBox(height: 16),

            // Last Updated
            Row(
              children: [
                Icon(Icons.access_time, size: 16, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
                const SizedBox(width: 8),
                Text(
                  isArabic
                      ? 'آخر تحديث: ${_formatDateTime(order.updatedAt ?? order.createdAt, isArabic)}'
                      : 'Last updated: ${_formatDateTime(order.updatedAt ?? order.createdAt, isArabic)}',
                  style: TextStyle(fontSize: 12, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Action Buttons
            _buildDemoActionButtons(context, order, isArabic, isDark),
          ],
        ),
      ),
    );
  }

  String _getServiceTitle(String orderId, bool isArabic) {
    final titles = {
      'ORD001': isArabic ? 'تصميم شعار للشركة' : 'Logo Design for Company',
      'ORD002': isArabic ? 'تطوير موقع إلكتروني' : 'Website Development',
      'ORD003': isArabic ? 'ترجمة مقال' : 'Article Translation',
      'ORD004': isArabic ? 'تحليل البيانات' : 'Data Analysis',
      'ORD005': isArabic ? 'كتابة محتوى' : 'Content Writing',
      'ORD006': isArabic ? 'تصميم تطبيق' : 'App Design',
      'ORD007': isArabic ? 'مراجعة نص' : 'Text Review',
    };
    return titles[orderId] ?? (isArabic ? 'خدمة عامة' : 'General Service');
  }

  Widget _buildStatusBadge(OrderStatus status, bool isArabic) {
    Color color;
    String text;
    IconData icon;

    switch (status) {
      case OrderStatus.created:
      case OrderStatus.paymentPending:
        color = ThemeProvider.warningOrange;
        text = isArabic ? 'في انتظار الدفع' : 'Payment Pending';
        icon = Icons.payment;
        break;
      case OrderStatus.paymentConfirmed:
      case OrderStatus.inProgress:
        color = ThemeProvider.primaryBlue;
        text = isArabic ? 'قيد التنفيذ' : 'In Progress';
        icon = Icons.work;
        break;
      case OrderStatus.submitted:
        color = ThemeProvider.primaryBlue;
        text = isArabic ? 'تم التسليم للمراجعة' : 'Submitted for Review';
        icon = Icons.upload;
        break;
      case OrderStatus.delivered:
        color = ThemeProvider.successGreen;
        text = isArabic ? 'مكتمل' : 'Completed';
        icon = Icons.check_circle;
        break;
      case OrderStatus.editing:
        color = Colors.purple;
        text = isArabic ? 'قيد التعديل' : 'Under Revision';
        icon = Icons.edit;
        break;
      case OrderStatus.completed:
        color = ThemeProvider.successGreen;
        text = isArabic ? 'مكتمل' : 'Completed';
        icon = Icons.check_circle;
        break;
      case OrderStatus.cancelled:
        color = Colors.red;
        text = isArabic ? 'ملغي' : 'Cancelled';
        icon = Icons.cancel;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 6),
          Text(text, style: TextStyle(color: color, fontWeight: FontWeight.bold, fontSize: 12)),
        ],
      ),
    );
  }

  Widget _buildMiniTimeline(OrderModel order, bool isArabic, bool isDark) {
    final steps = _getTimelineSteps(order, isArabic);
    final currentStep = _getCurrentStepIndex(order.status);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isDark ? ThemeProvider.darkCardBackground.withValues(alpha: 0.3) : Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children:
            steps.asMap().entries.map((entry) {
              final index = entry.key;
              final step = entry.value;
              final isActive = index <= currentStep;
              final isLast = index == steps.length - 1;

              return Expanded(
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        children: [
                          Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              color: isActive ? ThemeProvider.primaryBlue : Colors.grey[300],
                              shape: BoxShape.circle,
                            ),
                            child: Icon(isActive ? Icons.check : Icons.circle, size: 12, color: Colors.white),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            step,
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                              color: isActive ? ThemeProvider.primaryBlue : Colors.grey[600],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                    if (!isLast)
                      Container(height: 2, width: 20, color: isActive ? ThemeProvider.primaryBlue : Colors.grey[300]),
                  ],
                ),
              );
            }).toList(),
      ),
    );
  }

  Widget _buildDemoActionButtons(BuildContext context, OrderModel order, bool isArabic, bool isDark) {
    return Row(
      children: [
        // Chat Button
        Expanded(
          flex: 1,
          child: EnhancedButton(
            text: isArabic ? 'محادثة' : 'Chat',
            icon: Icons.chat,
            backgroundColor: ThemeProvider.primaryBlue,
            onPressed: () {
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(SnackBar(content: Text(isArabic ? 'فتح المحادثة...' : 'Opening chat...')));
            },
          ),
        ),
        const SizedBox(width: 8),
        // View Details Button
        Expanded(
          flex: 1,
          child: EnhancedButton(
            text: isArabic ? 'تفاصيل' : 'Details',
            icon: Icons.visibility,
            backgroundColor: Colors.grey[600]!,
            onPressed: () {
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(SnackBar(content: Text(isArabic ? 'عرض التفاصيل...' : 'Viewing details...')));
            },
          ),
        ),
        const SizedBox(width: 8),
        // Dynamic Action Button
        Expanded(flex: 1, child: _buildDynamicActionButton(context, order, isArabic, isDark)),
      ],
    );
  }

  Widget _buildDynamicActionButton(BuildContext context, OrderModel order, bool isArabic, bool isDark) {
    switch (order.status) {
      case OrderStatus.paymentPending:
        return EnhancedButton(
          text: isArabic ? 'دفع' : 'Pay',
          icon: Icons.payment,
          backgroundColor: ThemeProvider.successGreen,
          onPressed: () {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text(isArabic ? 'معالجة الدفع...' : 'Processing payment...')));
          },
        );
      case OrderStatus.delivered:
        return EnhancedButton(
          text: isArabic ? 'قبول' : 'Accept',
          icon: Icons.check,
          backgroundColor: ThemeProvider.successGreen,
          onPressed: () {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text(isArabic ? 'قبول العمل...' : 'Accepting work...')));
          },
        );
      case OrderStatus.completed:
        return EnhancedButton(
          text: isArabic ? 'تقييم' : 'Rate',
          icon: Icons.star,
          backgroundColor: ThemeProvider.warningOrange,
          onPressed: () {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text(isArabic ? 'تقييم المستقل...' : 'Rating freelancer...')));
          },
        );
      default:
        return EnhancedButton(
          text: isArabic ? 'إجراء' : 'Action',
          icon: Icons.more_horiz,
          backgroundColor: Colors.grey[600]!,
          onPressed: () {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text(isArabic ? 'إجراء عام...' : 'General action...')));
          },
        );
    }
  }

  List<String> _getTimelineSteps(OrderModel order, bool isArabic) {
    if (isArabic) {
      return ['إنشاء', 'دفع', 'تنفيذ', 'تسليم', 'إكمال'];
    } else {
      return ['Created', 'Payment', 'Progress', 'Delivery', 'Complete'];
    }
  }

  int _getCurrentStepIndex(OrderStatus status) {
    switch (status) {
      case OrderStatus.created:
        return 0;
      case OrderStatus.paymentPending:
        return 0;
      case OrderStatus.paymentConfirmed:
      case OrderStatus.inProgress:
        return 2;
      case OrderStatus.submitted:
        return 3;
      case OrderStatus.delivered:
      case OrderStatus.editing:
        return 3;
      case OrderStatus.completed:
        return 4;
      case OrderStatus.cancelled:
        return 0;
    }
  }

  String _formatDateTime(DateTime dateTime, bool isArabic) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return isArabic ? 'منذ ${difference.inDays} يوم' : '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return isArabic ? 'منذ ${difference.inHours} ساعة' : '${difference.inHours}h ago';
    } else {
      return isArabic ? 'منذ ${difference.inMinutes} دقيقة' : '${difference.inMinutes}m ago';
    }
  }
}

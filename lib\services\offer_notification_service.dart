import '../models/user_model.dart';
import '../models/notification_model.dart';
import '../services/smart_notification_service.dart';
import '../config/supabase_config.dart';

/// Service to handle offer-related notifications
class OfferNotificationService {
  /// Get user profile by ID
  static Future<UserModel?> _getUserProfile(String userId) async {
    try {
      final response = await SupabaseConfig.client.from('profiles').select().eq('id', userId).single();
      return UserModel.fromJson(response);
    } catch (e) {
      print('Failed to get user profile: $e');
      return null;
    }
  }

  /// Notify when a new offer is received
  static Future<void> notifyOfferReceived({
    required String clientId,
    required String offerId,
    required String freelancerId,
    required double offerAmount,
    required String serviceTitle,
    required int deliveryDays,
  }) async {
    try {
      // Get freelancer profile for name
      final freelancerProfile = await _getUserProfile(freelancerId);
      final freelancerName = freelancerProfile?.fullName ?? 'Freelancer';

      await SmartNotificationService.notifyOfferReceived(
        clientId: clientId,
        offerId: offerId,
        freelancerName: freelancerName,
        offerAmount: offerAmount,
        serviceTitle: serviceTitle,
      );
    } catch (e) {
      print('Failed to notify offer received: $e');
    }
  }

  /// Notify when an offer is accepted
  static Future<void> notifyOfferAccepted({
    required String freelancerId,
    required String offerId,
    required String clientId,
    required double offerAmount,
    required String serviceTitle,
  }) async {
    try {
      // Get client profile for name
      final clientProfile = await _getUserProfile(clientId);
      final clientName = clientProfile?.fullName ?? 'Client';

      await SmartNotificationService.createSmartNotification(
        userId: freelancerId,
        titleEn: 'Offer Accepted!',
        titleAr: 'تم قبول العرض!',
        descriptionEn: '$clientName accepted your offer of \$${offerAmount.toStringAsFixed(2)} for "$serviceTitle"',
        descriptionAr: 'قبل $clientName عرضك بقيمة \$${offerAmount.toStringAsFixed(2)} لـ "$serviceTitle"',
        type: NotificationType.offerAccepted,
        relatedId: offerId,
        priority: NotificationPriority.high,
        metadata: {'client_name': clientName, 'offer_amount': offerAmount, 'service_title': serviceTitle},
        actionUrl: '/offers/$offerId',
      );
    } catch (e) {
      print('Failed to notify offer accepted: $e');
    }
  }

  /// Notify when an offer is rejected
  static Future<void> notifyOfferRejected({
    required String freelancerId,
    required String offerId,
    required String clientId,
    required double offerAmount,
    required String serviceTitle,
    String? rejectionReason,
  }) async {
    try {
      // Get client profile for name
      final clientProfile = await _getUserProfile(clientId);
      final clientName = clientProfile?.fullName ?? 'Client';

      final reasonText = rejectionReason != null ? ' Reason: $rejectionReason' : '';
      final reasonTextAr = rejectionReason != null ? ' السبب: $rejectionReason' : '';

      await SmartNotificationService.createSmartNotification(
        userId: freelancerId,
        titleEn: 'Offer Declined',
        titleAr: 'تم رفض العرض',
        descriptionEn: '$clientName declined your offer for "$serviceTitle".$reasonText',
        descriptionAr: 'رفض $clientName عرضك لـ "$serviceTitle".$reasonTextAr',
        type: NotificationType.offerRejected,
        relatedId: offerId,
        priority: NotificationPriority.normal,
        metadata: {
          'client_name': clientName,
          'offer_amount': offerAmount,
          'service_title': serviceTitle,
          'rejection_reason': rejectionReason,
        },
        actionUrl: '/offers/$offerId',
      );
    } catch (e) {
      print('Failed to notify offer rejected: $e');
    }
  }

  /// Notify freelancer when they should submit an offer
  static Future<void> notifyOfferOpportunity({
    required String freelancerId,
    required String requestId,
    required String serviceTitle,
    required double budget,
    required String clientLocation,
  }) async {
    try {
      await SmartNotificationService.createSmartNotification(
        userId: freelancerId,
        titleEn: 'New Project Opportunity',
        titleAr: 'فرصة مشروع جديد',
        descriptionEn:
            'A new project "$serviceTitle" with budget \$${budget.toStringAsFixed(2)} is available in $clientLocation',
        descriptionAr: 'مشروع جديد "$serviceTitle" بميزانية \$${budget.toStringAsFixed(2)} متاح في $clientLocation',
        type: NotificationType.freelancerApplication,
        relatedId: requestId,
        priority: NotificationPriority.normal,
        metadata: {'service_title': serviceTitle, 'budget': budget, 'client_location': clientLocation},
        actionUrl: '/requests/$requestId',
      );
    } catch (e) {
      print('Failed to notify offer opportunity: $e');
    }
  }

  /// Notify client about offer expiration reminder
  static Future<void> notifyOfferExpirationReminder({
    required String clientId,
    required String offerId,
    required String freelancerName,
    required double offerAmount,
    required int hoursRemaining,
  }) async {
    try {
      await SmartNotificationService.createSmartNotification(
        userId: clientId,
        titleEn: 'Offer Expiring Soon',
        titleAr: 'العرض ينتهي قريباً',
        descriptionEn:
            'Offer from $freelancerName (\$${offerAmount.toStringAsFixed(2)}) expires in $hoursRemaining hours',
        descriptionAr: 'عرض $freelancerName (\$${offerAmount.toStringAsFixed(2)}) ينتهي خلال $hoursRemaining ساعة',
        type: NotificationType.reminderGeneral,
        relatedId: offerId,
        priority: NotificationPriority.high,
        metadata: {'freelancer_name': freelancerName, 'offer_amount': offerAmount, 'hours_remaining': hoursRemaining},
        actionUrl: '/offers/$offerId',
      );
    } catch (e) {
      print('Failed to notify offer expiration reminder: $e');
    }
  }

  /// Notify freelancer when their offer is about to expire
  static Future<void> notifyFreelancerOfferExpiring({
    required String freelancerId,
    required String offerId,
    required String serviceTitle,
    required int hoursRemaining,
  }) async {
    try {
      await SmartNotificationService.createSmartNotification(
        userId: freelancerId,
        titleEn: 'Your Offer Expires Soon',
        titleAr: 'عرضك ينتهي قريباً',
        descriptionEn: 'Your offer for "$serviceTitle" expires in $hoursRemaining hours',
        descriptionAr: 'عرضك لـ "$serviceTitle" ينتهي خلال $hoursRemaining ساعة',
        type: NotificationType.reminderGeneral,
        relatedId: offerId,
        priority: NotificationPriority.normal,
        metadata: {'service_title': serviceTitle, 'hours_remaining': hoursRemaining},
        actionUrl: '/offers/$offerId',
      );
    } catch (e) {
      print('Failed to notify freelancer offer expiring: $e');
    }
  }

  /// Batch notify multiple freelancers about a new project
  static Future<void> batchNotifyProjectAvailable({
    required List<String> freelancerIds,
    required String requestId,
    required String serviceTitle,
    required double budget,
    required String clientLocation,
    required List<String> requiredSkills,
  }) async {
    try {
      final skillsText = requiredSkills.join(', ');

      for (final freelancerId in freelancerIds) {
        await SmartNotificationService.createSmartNotification(
          userId: freelancerId,
          titleEn: 'New Project Match',
          titleAr: 'مشروع جديد مناسب',
          descriptionEn:
              'New project "$serviceTitle" matches your skills ($skillsText). Budget: \$${budget.toStringAsFixed(2)}',
          descriptionAr:
              'مشروع جديد "$serviceTitle" يناسب مهاراتك ($skillsText). الميزانية: \$${budget.toStringAsFixed(2)}',
          type: NotificationType.freelancerApplication,
          relatedId: requestId,
          priority: NotificationPriority.normal,
          metadata: {
            'service_title': serviceTitle,
            'budget': budget,
            'client_location': clientLocation,
            'required_skills': requiredSkills,
          },
          actionUrl: '/requests/$requestId',
        );

        // Add small delay to prevent overwhelming the system
        await Future.delayed(const Duration(milliseconds: 100));
      }
    } catch (e) {
      print('Failed to batch notify project available: $e');
    }
  }
}

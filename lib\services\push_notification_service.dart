import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../models/notification_model.dart';
import '../services/smart_notification_service.dart';

/// Push notification service for handling Firebase Cloud Messaging
/// This is a foundation implementation that can be extended with actual FCM
class PushNotificationService {
  static const String _fcmServerKey = 'YOUR_FCM_SERVER_KEY'; // Replace with actual key
  static const String _fcmSenderId = 'YOUR_FCM_SENDER_ID'; // Replace with actual sender ID

  static String? _fcmToken;
  static StreamSubscription? _tokenRefreshSubscription;
  static StreamSubscription? _messageSubscription;
  static StreamSubscription? _backgroundMessageSubscription;

  /// Initialize push notification service
  static Future<void> initialize() async {
    try {
      if (kDebugMode) {
        print('🔔 Initializing Push Notification Service...');
      }

      // In a real implementation, this would initialize Firebase Messaging
      // await FirebaseMessaging.instance.requestPermission();
      // _fcmToken = await FirebaseMessaging.instance.getToken();

      // For demo purposes, simulate FCM token
      _fcmToken = 'demo_fcm_token_${DateTime.now().millisecondsSinceEpoch}';

      if (kDebugMode) {
        print('🔧 FCM Configuration:');
        print('  - Sender ID: $_fcmSenderId');
        print('  - Server Key: ${_fcmServerKey.substring(0, 10)}...');
      }

      await _setupMessageHandlers();
      await _setupTokenRefreshHandler();

      if (kDebugMode) {
        print('✅ Push Notification Service initialized');
        print('📱 FCM Token: $_fcmToken');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize Push Notification Service: $e');
      }
    }
  }

  /// Setup message handlers for different app states
  static Future<void> _setupMessageHandlers() async {
    // Handle foreground messages
    // FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle background messages
    // FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);

    // Handle notification taps when app is terminated
    // FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

    // Check for initial message if app was opened from notification
    // final initialMessage = await FirebaseMessaging.instance.getInitialMessage();
    // if (initialMessage != null) {
    //   _handleNotificationTap(initialMessage);
    // }

    // For demo purposes, simulate message handler setup
    if (kDebugMode) {
      print('📨 Message handlers registered:');
      print('  - Foreground: ${_handleForegroundMessage.toString()}');
      print('  - Background: ${_handleBackgroundMessage.toString()}');
      print('  - Tap: ${_handleNotificationTap.toString()}');
      print('📨 Message handlers setup complete');
    }
  }

  /// Setup token refresh handler
  static Future<void> _setupTokenRefreshHandler() async {
    // FirebaseMessaging.instance.onTokenRefresh.listen((newToken) {
    //   _fcmToken = newToken;
    //   _updateTokenOnServer(newToken);
    // });

    // For demo purposes, simulate initial token update
    if (_fcmToken != null) {
      await _updateTokenOnServer(_fcmToken!);
    }

    if (kDebugMode) {
      print('🔄 Token refresh handler setup complete');
    }
  }

  /// Handle foreground messages (when app is open)
  static void _handleForegroundMessage(Map<String, dynamic> message) {
    try {
      if (kDebugMode) {
        print('📨 Foreground message received: $message');
      }

      final notification = _parseNotificationFromMessage(message);
      if (notification != null) {
        // Show in-app notification
        _showInAppNotification(notification);

        // Add to notification provider
        SmartNotificationService.createSmartNotification(
          userId: notification.userId,
          titleEn: notification.titleEn,
          titleAr: notification.titleAr,
          descriptionEn: notification.descriptionEn,
          descriptionAr: notification.descriptionAr,
          type: notification.type,
          relatedId: notification.relatedId,
          priority: notification.priority,
          metadata: notification.metadata,
          actionUrl: notification.actionUrl,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error handling foreground message: $e');
      }
    }
  }

  /// Handle background messages (when app is in background)
  static Future<void> _handleBackgroundMessage(Map<String, dynamic> message) async {
    try {
      if (kDebugMode) {
        print('📨 Background message received: $message');
      }

      final notification = _parseNotificationFromMessage(message);
      if (notification != null) {
        // Store notification for when app becomes active
        await _storeBackgroundNotification(notification);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error handling background message: $e');
      }
    }
  }

  /// Handle notification tap (when user taps on notification)
  static void _handleNotificationTap(Map<String, dynamic> message) {
    try {
      if (kDebugMode) {
        print('👆 Notification tapped: $message');
      }

      final notification = _parseNotificationFromMessage(message);
      if (notification != null && notification.actionUrl != null) {
        // Navigate to specific screen
        _navigateToScreen(notification.actionUrl!);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error handling notification tap: $e');
      }
    }
  }

  /// Parse notification from FCM message
  static NotificationModel? _parseNotificationFromMessage(Map<String, dynamic> message) {
    try {
      final data = message['data'] ?? {};

      return NotificationModel(
        id: data['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
        userId: data['user_id'] ?? '',
        titleEn: data['title_en'] ?? message['notification']?['title'] ?? '',
        titleAr: data['title_ar'] ?? message['notification']?['title'] ?? '',
        descriptionEn: data['description_en'] ?? message['notification']?['body'] ?? '',
        descriptionAr: data['description_ar'] ?? message['notification']?['body'] ?? '',
        type: NotificationType.values.firstWhere(
          (e) => e.toString().split('.').last == data['type'],
          orElse: () => NotificationType.systemAnnouncement,
        ),
        relatedId: data['related_id'],
        priority: NotificationPriority.values.firstWhere(
          (e) => e.toString().split('.').last == data['priority'],
          orElse: () => NotificationPriority.normal,
        ),
        createdAt: DateTime.now(),
        metadata: data['metadata'] != null ? jsonDecode(data['metadata']) : null,
        actionUrl: data['action_url'],
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error parsing notification from message: $e');
      }
      return null;
    }
  }

  /// Show in-app notification
  static void _showInAppNotification(NotificationModel notification) {
    // This would show a toast or overlay notification
    if (kDebugMode) {
      print('🔔 Showing in-app notification: ${notification.titleEn}');
    }
  }

  /// Store background notification for later processing
  static Future<void> _storeBackgroundNotification(NotificationModel notification) async {
    // Store in local database or shared preferences
    if (kDebugMode) {
      print('💾 Storing background notification: ${notification.titleEn}');
    }
  }

  /// Navigate to specific screen based on action URL
  static void _navigateToScreen(String actionUrl) {
    // Implement navigation logic based on your routing system
    if (kDebugMode) {
      print('🧭 Navigating to: $actionUrl');
    }
  }

  /// Send push notification to specific user
  static Future<bool> sendPushNotification({
    required String userToken,
    required String titleEn,
    required String titleAr,
    required String bodyEn,
    required String bodyAr,
    required NotificationType type,
    String? relatedId,
    NotificationPriority priority = NotificationPriority.normal,
    Map<String, dynamic>? metadata,
    String? actionUrl,
    String? imageUrl,
  }) async {
    try {
      // In a real implementation, this would send via FCM API using _fcmServerKey
      final payload = {
        'to': userToken,
        'notification': {'title': titleEn, 'body': bodyEn, 'image': imageUrl},
        'data': {
          'id': DateTime.now().millisecondsSinceEpoch.toString(),
          'title_en': titleEn,
          'title_ar': titleAr,
          'description_en': bodyEn,
          'description_ar': bodyAr,
          'type': type.toString().split('.').last,
          'related_id': relatedId,
          'priority': priority.toString().split('.').last,
          'metadata': metadata != null ? jsonEncode(metadata) : null,
          'action_url': actionUrl,
        },
        'android': {
          'priority': 'high',
          'notification': {
            'channel_id': 'taskly_notifications',
            'sound': 'default',
            'click_action': 'FLUTTER_NOTIFICATION_CLICK',
          },
        },
        'apns': {
          'payload': {
            'aps': {'sound': 'default', 'badge': 1},
          },
        },
      };

      if (kDebugMode) {
        print('📤 Sending push notification: $payload');
        print('🔑 Using FCM Server Key: ${_fcmServerKey.substring(0, 10)}...');
      }

      // In real implementation, you would use _fcmServerKey in HTTP headers:
      // final headers = {
      //   'Content-Type': 'application/json',
      //   'Authorization': 'key=$_fcmServerKey',
      // };
      // final response = await http.post(
      //   Uri.parse('https://fcm.googleapis.com/fcm/send'),
      //   headers: headers,
      //   body: jsonEncode(payload),
      // );

      // Simulate successful send
      await Future.delayed(const Duration(milliseconds: 500));

      if (kDebugMode) {
        print('✅ Push notification sent successfully');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to send push notification: $e');
      }
      return false;
    }
  }

  /// Send push notification to multiple users
  static Future<Map<String, bool>> sendBulkPushNotifications({
    required List<String> userTokens,
    required String titleEn,
    required String titleAr,
    required String bodyEn,
    required String bodyAr,
    required NotificationType type,
    String? relatedId,
    NotificationPriority priority = NotificationPriority.normal,
    Map<String, dynamic>? metadata,
    String? actionUrl,
  }) async {
    final results = <String, bool>{};

    for (final token in userTokens) {
      final success = await sendPushNotification(
        userToken: token,
        titleEn: titleEn,
        titleAr: titleAr,
        bodyEn: bodyEn,
        bodyAr: bodyAr,
        type: type,
        relatedId: relatedId,
        priority: priority,
        metadata: metadata,
        actionUrl: actionUrl,
      );

      results[token] = success;

      // Add small delay to prevent overwhelming the FCM service
      await Future.delayed(const Duration(milliseconds: 100));
    }

    return results;
  }

  /// Get current FCM token
  static String? get fcmToken => _fcmToken;

  /// Update FCM token on server
  static Future<void> _updateTokenOnServer(String token) async {
    try {
      // Send token to your backend server
      if (kDebugMode) {
        print('🔄 Updating FCM token on server: $token');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to update FCM token on server: $e');
      }
    }
  }

  /// Subscribe to topic for broadcast notifications
  static Future<void> subscribeToTopic(String topic) async {
    try {
      // await FirebaseMessaging.instance.subscribeToTopic(topic);
      if (kDebugMode) {
        print('📢 Subscribed to topic: $topic');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to subscribe to topic: $e');
      }
    }
  }

  /// Unsubscribe from topic
  static Future<void> unsubscribeFromTopic(String topic) async {
    try {
      // await FirebaseMessaging.instance.unsubscribeFromTopic(topic);
      if (kDebugMode) {
        print('📢 Unsubscribed from topic: $topic');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to unsubscribe from topic: $e');
      }
    }
  }

  /// Dispose resources
  static void dispose() {
    _tokenRefreshSubscription?.cancel();
    _messageSubscription?.cancel();
    _backgroundMessageSubscription?.cancel();
  }
}

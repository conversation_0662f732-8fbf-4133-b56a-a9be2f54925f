import 'package:flutter/material.dart';
import '../../models/order_model.dart';
import '../../providers/theme_provider.dart';
import '../../widgets/common/enhanced_widgets.dart';

class ClientFeedbackSection extends StatefulWidget {
  final OrderModel order;
  final bool isArabic;
  final bool isDark;
  final Function(int rating, String review)? onSubmitReview;

  const ClientFeedbackSection({
    super.key,
    required this.order,
    required this.isArabic,
    required this.isDark,
    this.onSubmitReview,
  });

  @override
  State<ClientFeedbackSection> createState() => _ClientFeedbackSectionState();
}

class _ClientFeedbackSectionState extends State<ClientFeedbackSection> {
  int _selectedRating = 0;
  final TextEditingController _reviewController = TextEditingController();
  bool _isSubmitting = false;

  @override
  void dispose() {
    _reviewController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Show existing review if available
    if (widget.order.clientRating != null) {
      return _buildExistingReview();
    }

    // Show rating prompt for completed orders
    if (widget.order.status == OrderStatus.completed) {
      return _buildRatingPrompt();
    }

    // Don't show anything for other statuses
    return const SizedBox.shrink();
  }

  Widget _buildExistingReview() {
    return EnhancedCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.rate_review, color: ThemeProvider.primaryBlue, size: 24),
              const SizedBox(width: 12),
              Text(
                widget.isArabic ? 'تقييمك' : 'Your Review',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: widget.isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Rating Stars
          Row(
            children: [
              Text(
                widget.isArabic ? 'التقييم:' : 'Rating:',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: widget.isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                ),
              ),
              const SizedBox(width: 12),
              ...List.generate(5, (index) {
                return Icon(
                  index < (widget.order.clientRating ?? 0) ? Icons.star : Icons.star_border,
                  color: Colors.amber,
                  size: 24,
                );
              }),
              const SizedBox(width: 8),
              Text(
                '${widget.order.clientRating}/5',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: widget.isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                ),
              ),
            ],
          ),

          if (widget.order.clientReview != null && widget.order.clientReview!.isNotEmpty) ...[
            const SizedBox(height: 16),
            Text(
              widget.isArabic ? 'مراجعتك:' : 'Your Review:',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: widget.isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: (widget.isDark ? ThemeProvider.darkCardBackground : Colors.grey[50]),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: widget.isDark ? Colors.grey[700]! : Colors.grey[200]!),
              ),
              child: Text(
                widget.order.clientReview!,
                style: TextStyle(
                  color: widget.isDark ? ThemeProvider.darkTextSecondary : Colors.grey[700],
                  height: 1.5,
                ),
              ),
            ),
          ],

          const SizedBox(height: 16),

          // Review Date
          Row(
            children: [
              Icon(
                Icons.access_time,
                size: 16,
                color: widget.isDark ? ThemeProvider.darkTextSecondary : Colors.grey[500],
              ),
              const SizedBox(width: 8),
              Text(
                widget.isArabic
                    ? 'تم التقييم: ${_formatDate(widget.order.reviewDate ?? widget.order.updatedAt ?? widget.order.createdAt)}'
                    : 'Reviewed: ${_formatDate(widget.order.reviewDate ?? widget.order.updatedAt ?? widget.order.createdAt)}',
                style: TextStyle(
                  fontSize: 14,
                  color: widget.isDark ? ThemeProvider.darkTextSecondary : Colors.grey[500],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRatingPrompt() {
    return EnhancedCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(color: Colors.amber.withValues(alpha: 0.1), shape: BoxShape.circle),
                child: const Icon(Icons.star, color: Colors.amber, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.isArabic ? 'قيم المستقل' : 'Rate the Freelancer',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: widget.isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      widget.isArabic ? 'ساعد الآخرين من خلال تقييم تجربتك' : 'Help others by rating your experience',
                      style: TextStyle(
                        fontSize: 14,
                        color: widget.isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Rating Stars
          Text(
            widget.isArabic ? 'التقييم:' : 'Rating:',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: widget.isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: List.generate(5, (index) {
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedRating = index + 1;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.all(8),
                  child: Icon(index < _selectedRating ? Icons.star : Icons.star_border, color: Colors.amber, size: 32),
                ),
              );
            }),
          ),

          if (_selectedRating > 0) ...[
            const SizedBox(height: 8),
            Text(
              _getRatingText(_selectedRating),
              style: TextStyle(fontWeight: FontWeight.w600, color: _getRatingColor(_selectedRating)),
            ),
          ],

          const SizedBox(height: 20),

          // Review Text
          Text(
            widget.isArabic ? 'مراجعة (اختيارية):' : 'Review (Optional):',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: widget.isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          TextField(
            controller: _reviewController,
            maxLines: 4,
            decoration: InputDecoration(
              hintText:
                  widget.isArabic ? 'شارك تجربتك مع هذا المستقل...' : 'Share your experience with this freelancer...',
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: ThemeProvider.primaryBlue),
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Submit Button
          SizedBox(
            width: double.infinity,
            child: EnhancedButton(
              text: widget.isArabic ? 'إرسال التقييم' : 'Submit Review',
              icon: Icons.send,
              backgroundColor: ThemeProvider.primaryBlue,
              onPressed: _selectedRating > 0 ? _submitReview : null,
              isLoading: _isSubmitting,
            ),
          ),

          const SizedBox(height: 12),

          // Skip Button
          Center(
            child: TextButton(
              onPressed: _skipReview,
              child: Text(
                widget.isArabic ? 'تخطي الآن' : 'Skip for now',
                style: TextStyle(color: widget.isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getRatingText(int rating) {
    if (widget.isArabic) {
      switch (rating) {
        case 1:
          return 'ضعيف جداً';
        case 2:
          return 'ضعيف';
        case 3:
          return 'متوسط';
        case 4:
          return 'جيد';
        case 5:
          return 'ممتاز';
        default:
          return '';
      }
    } else {
      switch (rating) {
        case 1:
          return 'Very Poor';
        case 2:
          return 'Poor';
        case 3:
          return 'Average';
        case 4:
          return 'Good';
        case 5:
          return 'Excellent';
        default:
          return '';
      }
    }
  }

  Color _getRatingColor(int rating) {
    switch (rating) {
      case 1:
      case 2:
        return Colors.red;
      case 3:
        return Colors.orange;
      case 4:
      case 5:
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _submitReview() async {
    if (_selectedRating == 0) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      // Call the callback if provided
      if (widget.onSubmitReview != null) {
        await widget.onSubmitReview!(_selectedRating, _reviewController.text.trim());
      }

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.isArabic ? 'تم إرسال التقييم بنجاح!' : 'Review submitted successfully!'),
            backgroundColor: ThemeProvider.successGreen,
          ),
        );
      }
    } catch (e) {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.isArabic ? 'حدث خطأ أثناء إرسال التقييم' : 'Error submitting review'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  void _skipReview() {
    // Show confirmation message
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            widget.isArabic
                ? 'يمكنك تقييم المستقل لاحقاً من صفحة الطلبات'
                : 'You can rate the freelancer later from the orders page',
          ),
          backgroundColor: widget.isDark ? ThemeProvider.darkCardBackground : Colors.grey[600],
          duration: const Duration(seconds: 3),
        ),
      );
    }

    // Navigate back if this widget is in a separate screen
    // or simply dismiss the feedback section by navigating back
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    }
  }
}

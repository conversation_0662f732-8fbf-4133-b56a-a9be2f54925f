/// Enum for different types of notifications
enum NotificationType {
  // Core order lifecycle notifications
  orderStatus,
  orderNew,
  orderAccepted,
  orderDelivered,
  orderCompleted,
  orderCancelled,

  // Offer and payment notifications
  offerReceived,
  offerAccepted,
  offerRejected,
  paymentConfirmation,
  paymentConfirmed,
  paymentRequest,
  paymentPending,

  // Communication notifications
  newMessage,
  messageReceived,

  // System and admin notifications
  systemAnnouncement,
  adminMessage,
  systemUpdate,
  maintenanceNotice,

  // Reminder notifications
  reminderPaymentDue,
  reminderDeliveryPending,
  reminderReviewPending,
  reminderResponseNeeded,
  reminderGeneral,

  // Freelancer specific
  freelancerApplication,
  jobAssigned,

  // Client specific
  workDelivered,
  revisionRequested,

  // Account and profile
  profileUpdate,
  accountVerification,
}

/// Enum for notification priority levels
enum NotificationPriority { low, normal, high, urgent }

class NotificationModel {
  final String id;
  final String userId;
  final String titleEn;
  final String titleAr;
  final String descriptionEn;
  final String descriptionAr;
  final NotificationType type;
  final String? relatedId; // ID of related order, payment, etc.
  final bool isRead;
  final NotificationPriority priority;
  final DateTime createdAt;
  final DateTime? readAt;
  final Map<String, dynamic>? metadata;
  final String? actionUrl;
  final String? imageUrl;

  // Legacy fields for backward compatibility
  String get title => titleEn;
  String get message => descriptionEn;

  const NotificationModel({
    required this.id,
    required this.userId,
    required this.titleEn,
    required this.titleAr,
    required this.descriptionEn,
    required this.descriptionAr,
    required this.type,
    this.relatedId,
    this.isRead = false,
    this.priority = NotificationPriority.normal,
    required this.createdAt,
    this.readAt,
    this.metadata,
    this.actionUrl,
    this.imageUrl,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'],
      userId: json['user_id'],
      titleEn: json['title_en'] ?? json['title'] ?? '',
      titleAr: json['title_ar'] ?? json['title'] ?? '',
      descriptionEn: json['description_en'] ?? json['message'] ?? '',
      descriptionAr: json['description_ar'] ?? json['message'] ?? '',
      type: NotificationType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => NotificationType.orderStatus,
      ),
      relatedId: json['related_id'],
      isRead: json['is_read'] ?? false,
      priority: NotificationPriority.values.firstWhere(
        (e) => e.toString().split('.').last == json['priority'],
        orElse: () => NotificationPriority.normal,
      ),
      createdAt: DateTime.parse(json['created_at']),
      readAt: json['read_at'] != null ? DateTime.parse(json['read_at']) : null,
      metadata: json['metadata'],
      actionUrl: json['action_url'],
      imageUrl: json['image_url'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'title_en': titleEn,
      'title_ar': titleAr,
      'description_en': descriptionEn,
      'description_ar': descriptionAr,
      'type': type.toString().split('.').last,
      'related_id': relatedId,
      'is_read': isRead,
      'priority': priority.toString().split('.').last,
      'created_at': createdAt.toIso8601String(),
      'read_at': readAt?.toIso8601String(),
      'metadata': metadata,
      'action_url': actionUrl,
      'image_url': imageUrl,
      // Legacy fields for backward compatibility
      'title': titleEn,
      'message': descriptionEn,
    };
  }

  NotificationModel copyWith({
    String? id,
    String? userId,
    String? titleEn,
    String? titleAr,
    String? descriptionEn,
    String? descriptionAr,
    NotificationType? type,
    String? relatedId,
    bool? isRead,
    NotificationPriority? priority,
    DateTime? createdAt,
    DateTime? readAt,
    Map<String, dynamic>? metadata,
    String? actionUrl,
    String? imageUrl,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      titleEn: titleEn ?? this.titleEn,
      titleAr: titleAr ?? this.titleAr,
      descriptionEn: descriptionEn ?? this.descriptionEn,
      descriptionAr: descriptionAr ?? this.descriptionAr,
      type: type ?? this.type,
      relatedId: relatedId ?? this.relatedId,
      isRead: isRead ?? this.isRead,
      priority: priority ?? this.priority,
      createdAt: createdAt ?? this.createdAt,
      readAt: readAt ?? this.readAt,
      metadata: metadata ?? this.metadata,
      actionUrl: actionUrl ?? this.actionUrl,
      imageUrl: imageUrl ?? this.imageUrl,
    );
  }

  /// Mark notification as read
  NotificationModel markAsRead() {
    return copyWith(isRead: true, readAt: DateTime.now());
  }

  /// Get localized title based on language
  String getTitle(String languageCode) {
    return languageCode == 'ar' ? titleAr : titleEn;
  }

  /// Get localized description based on language
  String getDescription(String languageCode) {
    return languageCode == 'ar' ? descriptionAr : descriptionEn;
  }

  /// Get time ago string for display
  String getTimeAgo(String languageCode) {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (languageCode == 'ar') {
      if (difference.inMinutes < 1) {
        return 'الآن';
      } else if (difference.inMinutes < 60) {
        return 'منذ ${difference.inMinutes} دقيقة';
      } else if (difference.inHours < 24) {
        return 'منذ ${difference.inHours} ساعة';
      } else if (difference.inDays < 7) {
        return 'منذ ${difference.inDays} يوم';
      } else {
        return 'منذ ${(difference.inDays / 7).floor()} أسبوع';
      }
    } else {
      if (difference.inMinutes < 1) {
        return 'Now';
      } else if (difference.inMinutes < 60) {
        return '${difference.inMinutes}m ago';
      } else if (difference.inHours < 24) {
        return '${difference.inHours}h ago';
      } else if (difference.inDays < 7) {
        return '${difference.inDays}d ago';
      } else {
        return '${(difference.inDays / 7).floor()}w ago';
      }
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) || other is NotificationModel && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'NotificationModel{id: $id, type: $type, isRead: $isRead, createdAt: $createdAt}';
  }
}

/// Extension to get icon data and colors for notification types
extension NotificationTypeExtension on NotificationType {
  String get iconName {
    switch (this) {
      case NotificationType.orderStatus:
      case NotificationType.orderNew:
      case NotificationType.orderAccepted:
      case NotificationType.orderDelivered:
      case NotificationType.orderCompleted:
      case NotificationType.orderCancelled:
      case NotificationType.jobAssigned:
        return 'assignment';
      case NotificationType.newMessage:
      case NotificationType.messageReceived:
        return 'message';
      case NotificationType.paymentConfirmation:
      case NotificationType.paymentRequest:
      case NotificationType.paymentConfirmed:
      case NotificationType.paymentPending:
        return 'payment';
      case NotificationType.systemAnnouncement:
      case NotificationType.adminMessage:
      case NotificationType.systemUpdate:
      case NotificationType.maintenanceNotice:
        return 'announcement';
      case NotificationType.freelancerApplication:
        return 'person_add';
      case NotificationType.offerReceived:
      case NotificationType.offerAccepted:
      case NotificationType.offerRejected:
        return 'local_offer';
      case NotificationType.reminderPaymentDue:
      case NotificationType.reminderDeliveryPending:
      case NotificationType.reminderReviewPending:
      case NotificationType.reminderResponseNeeded:
      case NotificationType.reminderGeneral:
        return 'schedule';
      case NotificationType.workDelivered:
        return 'file_download';
      case NotificationType.revisionRequested:
        return 'edit';
      case NotificationType.profileUpdate:
      case NotificationType.accountVerification:
        return 'person';
    }
  }

  String get colorName {
    switch (this) {
      case NotificationType.orderNew:
        return 'blue';
      case NotificationType.orderAccepted:
        return 'green';
      case NotificationType.orderDelivered:
        return 'orange';
      case NotificationType.orderCompleted:
        return 'green';
      case NotificationType.orderCancelled:
        return 'red';
      case NotificationType.newMessage:
      case NotificationType.messageReceived:
        return 'blue';
      case NotificationType.paymentConfirmation:
      case NotificationType.paymentConfirmed:
        return 'green';
      case NotificationType.paymentRequest:
      case NotificationType.paymentPending:
        return 'orange';
      case NotificationType.systemAnnouncement:
      case NotificationType.adminMessage:
      case NotificationType.systemUpdate:
      case NotificationType.maintenanceNotice:
        return 'purple';
      case NotificationType.freelancerApplication:
        return 'blue';
      case NotificationType.orderStatus:
      case NotificationType.jobAssigned:
        return 'indigo';
      case NotificationType.offerReceived:
        return 'teal';
      case NotificationType.offerAccepted:
        return 'green';
      case NotificationType.offerRejected:
        return 'red';
      case NotificationType.reminderPaymentDue:
      case NotificationType.reminderDeliveryPending:
      case NotificationType.reminderReviewPending:
      case NotificationType.reminderResponseNeeded:
      case NotificationType.reminderGeneral:
        return 'amber';
      case NotificationType.workDelivered:
        return 'blue';
      case NotificationType.revisionRequested:
        return 'orange';
      case NotificationType.profileUpdate:
      case NotificationType.accountVerification:
        return 'indigo';
    }
  }
}

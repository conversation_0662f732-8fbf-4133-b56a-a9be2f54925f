-- FreelanceHub Database Schema for Supabase
-- Run these SQL commands in your Supabase SQL editor

-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';

-- <PERSON>reate custom types
CREATE TYPE user_role AS ENUM ('client', 'freelancer', 'admin');
CREATE TYPE request_status AS ENUM ('pending', 'inProgress', 'completed', 'cancelled');
CREATE TYPE priority AS ENUM ('normal', 'urgent', 'vip');
CREATE TYPE offer_status AS ENUM ('pending', 'accepted', 'rejected', 'withdrawn');
CREATE TYPE order_status AS ENUM ('created', 'paymentPending', 'paymentConfirmed', 'inProgress', 'delivered', 'completed', 'cancelled');
CREATE TYPE payment_status AS ENUM ('pending', 'confirmed', 'rejected');
CREATE TYPE message_type AS ENUM ('text', 'image', 'file', 'system', 'timeline', 'deliveryConfirmation', 'receiptConfirmation', 'autoReminder', 'ratingRequest');

-- Profiles table (extends auth.users)
CREATE TABLE profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    role user_role NOT NULL DEFAULT 'client',
    bio TEXT,
    skills TEXT[],
    rating DECIMAL(3,2) DEFAULT 0.00,
    completed_jobs INTEGER DEFAULT 0,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Service requests table
CREATE TABLE service_requests (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    client_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    budget DECIMAL(10,2),
    attachments TEXT[],
    status request_status DEFAULT 'pending',
    priority priority DEFAULT 'normal',
    assigned_freelancer_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
    deadline TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    tags TEXT[],
    category TEXT
);

-- Offers table
CREATE TABLE offers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    request_id UUID REFERENCES service_requests(id) ON DELETE CASCADE NOT NULL,
    freelancer_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    description TEXT NOT NULL,
    delivery_days INTEGER NOT NULL,
    status offer_status DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    rejection_reason TEXT
);

-- Orders table
CREATE TABLE orders (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    request_id UUID REFERENCES service_requests(id) ON DELETE CASCADE NOT NULL,
    client_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    freelancer_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    offer_id UUID REFERENCES offers(id) ON DELETE CASCADE NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    status order_status DEFAULT 'created',
    payment_status payment_status DEFAULT 'pending',
    payment_proof_url TEXT,
    delivery_url TEXT,
    delivery_notes TEXT,
    delivery_date TIMESTAMP WITH TIME ZONE,
    client_rating DECIMAL(3,2),
    client_review TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Chats table
CREATE TABLE chats (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    request_id UUID REFERENCES service_requests(id) ON DELETE CASCADE NOT NULL,
    client_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    freelancer_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    last_message TEXT,
    last_message_time TIMESTAMP WITH TIME ZONE,
    unread_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Messages table
CREATE TABLE messages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    chat_id UUID REFERENCES chats(id) ON DELETE CASCADE NOT NULL,
    sender_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    content TEXT NOT NULL,
    type message_type DEFAULT 'text',
    file_url TEXT,
    file_name TEXT,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_profiles_role ON profiles(role);
CREATE INDEX idx_profiles_email ON profiles(email);
CREATE INDEX idx_service_requests_client_id ON service_requests(client_id);
CREATE INDEX idx_service_requests_status ON service_requests(status);
CREATE INDEX idx_service_requests_created_at ON service_requests(created_at DESC);
CREATE INDEX idx_offers_request_id ON offers(request_id);
CREATE INDEX idx_offers_freelancer_id ON offers(freelancer_id);
CREATE INDEX idx_offers_status ON offers(status);
CREATE INDEX idx_orders_client_id ON orders(client_id);
CREATE INDEX idx_orders_freelancer_id ON orders(freelancer_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_payment_status ON orders(payment_status);
CREATE INDEX idx_chats_client_id ON chats(client_id);
CREATE INDEX idx_chats_freelancer_id ON chats(freelancer_id);
CREATE INDEX idx_messages_chat_id ON messages(chat_id);
CREATE INDEX idx_messages_created_at ON messages(created_at);

-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE service_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE offers ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE chats ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

-- RLS Policies for profiles
CREATE POLICY "Users can view all profiles" ON profiles FOR SELECT USING (true);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON profiles FOR INSERT WITH CHECK (auth.uid() = id);

-- RLS Policies for service_requests
CREATE POLICY "Anyone can view service requests" ON service_requests FOR SELECT USING (true);
CREATE POLICY "Clients can insert own requests" ON service_requests FOR INSERT WITH CHECK (auth.uid() = client_id);
CREATE POLICY "Clients can update own requests" ON service_requests FOR UPDATE USING (auth.uid() = client_id);
CREATE POLICY "Admins can update any request" ON service_requests FOR UPDATE USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin')
);

-- RLS Policies for offers
CREATE POLICY "Anyone can view offers" ON offers FOR SELECT USING (true);
CREATE POLICY "Freelancers can insert offers" ON offers FOR INSERT WITH CHECK (auth.uid() = freelancer_id);
CREATE POLICY "Freelancers can update own offers" ON offers FOR UPDATE USING (auth.uid() = freelancer_id);
CREATE POLICY "Clients can update offers on their requests" ON offers FOR UPDATE USING (
    EXISTS (SELECT 1 FROM service_requests WHERE id = request_id AND client_id = auth.uid())
);

-- RLS Policies for orders
CREATE POLICY "Users can view related orders" ON orders FOR SELECT USING (
    auth.uid() = client_id OR auth.uid() = freelancer_id OR 
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin')
);
CREATE POLICY "System can insert orders" ON orders FOR INSERT WITH CHECK (true);
CREATE POLICY "Related users can update orders" ON orders FOR UPDATE USING (
    auth.uid() = client_id OR auth.uid() = freelancer_id OR 
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin')
);

-- RLS Policies for chats
CREATE POLICY "Users can view own chats" ON chats FOR SELECT USING (
    auth.uid() = client_id OR auth.uid() = freelancer_id OR 
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin')
);
CREATE POLICY "System can insert chats" ON chats FOR INSERT WITH CHECK (true);
CREATE POLICY "Chat participants can update" ON chats FOR UPDATE USING (
    auth.uid() = client_id OR auth.uid() = freelancer_id
);

-- RLS Policies for messages
CREATE POLICY "Users can view chat messages" ON messages FOR SELECT USING (
    EXISTS (SELECT 1 FROM chats WHERE id = chat_id AND (client_id = auth.uid() OR freelancer_id = auth.uid())) OR
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin')
);
CREATE POLICY "Chat participants can insert messages" ON messages FOR INSERT WITH CHECK (
    EXISTS (SELECT 1 FROM chats WHERE id = chat_id AND (client_id = auth.uid() OR freelancer_id = auth.uid())) OR
    sender_id = 'system'
);
CREATE POLICY "Users can update own messages" ON messages FOR UPDATE USING (auth.uid() = sender_id);

-- Create functions for updating timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_service_requests_updated_at BEFORE UPDATE ON service_requests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_offers_updated_at BEFORE UPDATE ON offers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_chats_updated_at BEFORE UPDATE ON chats FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create storage bucket for file uploads
INSERT INTO storage.buckets (id, name, public) VALUES ('uploads', 'uploads', true);

-- Storage policies
CREATE POLICY "Anyone can view uploads" ON storage.objects FOR SELECT USING (bucket_id = 'uploads');
CREATE POLICY "Authenticated users can upload" ON storage.objects FOR INSERT WITH CHECK (bucket_id = 'uploads' AND auth.role() = 'authenticated');
CREATE POLICY "Users can update own uploads" ON storage.objects FOR UPDATE USING (bucket_id = 'uploads' AND auth.uid()::text = (storage.foldername(name))[1]);
CREATE POLICY "Users can delete own uploads" ON storage.objects FOR DELETE USING (bucket_id = 'uploads' AND auth.uid()::text = (storage.foldername(name))[1]);

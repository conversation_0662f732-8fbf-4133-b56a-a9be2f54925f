@echo off
echo Building Academic Services Platform APK...
echo.

echo Step 1: Cleaning previous builds...
flutter clean
echo.

echo Step 2: Getting dependencies...
flutter pub get --no-example
echo.

echo Step 3: Building APK (Debug)...
flutter build apk --debug --no-tree-shake-icons
echo.

echo Step 4: Building APK (Release)...
flutter build apk --release --no-tree-shake-icons
echo.

echo Build completed!
echo.
echo Debug APK location: build\app\outputs\flutter-apk\app-debug.apk
echo Release APK location: build\app\outputs\flutter-apk\app-release.apk
echo.
pause

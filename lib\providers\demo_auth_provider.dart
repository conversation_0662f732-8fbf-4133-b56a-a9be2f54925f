import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../services/app_initialization_service.dart';

class DemoUser {
  final String id;
  final String email;

  DemoUser({required this.id, required this.email});
}

class DemoAuthProvider with ChangeNotifier {
  DemoUser? _user;
  UserModel? _userProfile;
  bool _isLoading = false;
  String? _error;

  DemoUser? get user => _user;
  UserModel? get userProfile => _userProfile;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _user != null;

  Future<bool> signUp({
    required String email,
    required String password,
    required String fullName,
    required UserRole role,
  }) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      // Create demo user with consistent ID for demo data
      _user = DemoUser(id: 'demo_client_1', email: email);

      _userProfile = UserModel(
        id: _user!.id,
        email: email,
        fullName: fullName,
        role: role,
        createdAt: DateTime.now(),
        isVerified: true,
      );

      _isLoading = false;
      notifyListeners();

      // Initialize user session after successful signup
      // Note: In a real app, you'd pass the actual context here
      // For demo purposes, we'll handle this in the UI layer

      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<bool> signIn({required String email, required String password}) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      // Create demo user based on email with consistent ID for demo data
      String userId = email.contains('freelancer') ? 'demo_freelancer_1' : 'demo_client_1';
      _user = DemoUser(id: userId, email: email);

      // Determine role based on email for demo
      UserRole role;
      String fullName;

      if (email.contains('admin')) {
        role = UserRole.admin;
        fullName = 'Admin User';
      } else if (email.contains('freelancer')) {
        role = UserRole.freelancer;
        fullName = 'Freelancer User';
      } else {
        role = UserRole.client;
        fullName = 'Client User';
      }

      _userProfile = UserModel(
        id: _user!.id,
        email: email,
        fullName: fullName,
        role: role,
        createdAt: DateTime.now(),
        isVerified: true,
        rating: role == UserRole.freelancer ? 4.8 : null,
        completedJobs: role == UserRole.freelancer ? 25 : null,
      );

      _isLoading = false;
      notifyListeners();

      // Initialize user session after successful signin
      // Note: In a real app, you'd pass the actual context here
      // For demo purposes, we'll handle this in the UI layer

      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<void> signOut() async {
    _user = null;
    _userProfile = null;
    notifyListeners();
  }

  Future<bool> updateProfile({String? fullName, String? bio, List<String>? skills, String? avatarUrl}) async {
    try {
      _isLoading = true;
      notifyListeners();

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      if (_userProfile != null) {
        _userProfile = _userProfile!.copyWith(
          fullName: fullName ?? _userProfile!.fullName,
          bio: bio ?? _userProfile!.bio,
          skills: skills ?? _userProfile!.skills,
          avatarUrl: avatarUrl ?? _userProfile!.avatarUrl,
          updatedAt: DateTime.now(),
        );
      }

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Demo method to quickly login as different roles
  void quickLogin(UserRole role) {
    String email;
    String fullName;

    switch (role) {
      case UserRole.client:
        email = '<EMAIL>';
        fullName = 'John Client';
        break;
      case UserRole.freelancer:
        email = '<EMAIL>';
        fullName = 'Sarah Freelancer';
        break;
      case UserRole.admin:
        email = '<EMAIL>';
        fullName = 'Admin User';
        break;
    }

    // Use consistent demo IDs based on role
    String userId;
    switch (role) {
      case UserRole.client:
        userId = 'demo_client_1';
        break;
      case UserRole.freelancer:
        userId = 'demo_freelancer_1';
        break;
      case UserRole.admin:
        userId = 'demo_admin_1';
        break;
    }
    _user = DemoUser(id: userId, email: email);

    _userProfile = UserModel(
      id: _user!.id,
      email: email,
      fullName: fullName,
      role: role,
      createdAt: DateTime.now(),
      isVerified: true,
      rating: role == UserRole.freelancer ? 4.8 : null,
      completedJobs: role == UserRole.freelancer ? 25 : null,
      bio: role == UserRole.freelancer ? 'Experienced Flutter developer with 5+ years of experience' : null,
      skills: role == UserRole.freelancer ? ['Flutter', 'Dart', 'Firebase', 'UI/UX Design'] : null,
    );

    notifyListeners();
  }

  /// Initialize user session with providers
  Future<void> initializeUserSession(BuildContext context) async {
    if (_user != null) {
      await AppInitializationService.initializeUserSession(context, _user!.id);
    }
  }

  /// Cleanup user session
  Future<void> cleanupUserSession(BuildContext context) async {
    await AppInitializationService.cleanupUserSession(context);
  }
}

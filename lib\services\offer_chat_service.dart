import '../models/chat_model.dart';
import '../models/message_model.dart';
import '../models/offer_model.dart';
import '../models/service_request_model.dart';
import 'chat_service.dart';
import 'request_service.dart';

/// Service to handle automatic chat creation when offers are sent
class OfferChatService {
  /// Create or get chat when freelancer sends an offer to client
  static Future<ChatModel?> createChatForOffer(OfferModel offer, {String languageCode = 'en'}) async {
    try {
      print('🔄 Creating chat for offer: ${offer.id}');

      // Get the service request to find client ID
      final request = await RequestService.getRequestById(offer.requestId);
      if (request == null) {
        print('❌ Request not found for offer: ${offer.requestId}');
        // For demo mode, create a mock request
        return await _createDemoChatForOffer(offer);
      }

      // Check if chat already exists for this request
      final existingChat = await ChatService.getChatByRequestId(offer.requestId);
      if (existingChat != null) {
        print('✅ Chat already exists for request: ${offer.requestId}');
        await _sendOfferIntroductionMessage(existingChat, offer, request, languageCode: languageCode);
        return existingChat;
      }

      // Create new chat between freelancer and client
      final chat = ChatModel(
        id: 'chat_${offer.requestId}_${DateTime.now().millisecondsSinceEpoch}',
        requestId: offer.requestId,
        clientId: request.clientId,
        freelancerId: offer.freelancerId,
        createdAt: DateTime.now(),
      );

      // Save chat to database (in demo mode, this will be handled by ChatService)
      final createdChat = await ChatService.createChat(chat);

      // Send introduction messages
      await _sendOfferIntroductionMessage(createdChat, offer, request, languageCode: languageCode);

      print('✅ Chat created successfully for offer: ${offer.id}');
      return createdChat;
    } catch (e) {
      print('❌ Error creating chat for offer: $e');
      return null;
    }
  }

  /// Send introduction message when freelancer sends offer
  static Future<void> _sendOfferIntroductionMessage(
    ChatModel chat,
    OfferModel offer,
    ServiceRequestModel request, {
    String languageCode = 'en',
  }) async {
    try {
      // Create introduction message from freelancer
      final introMessage = MessageModel(
        id: 'offer_intro_${offer.id}',
        chatId: chat.id,
        senderId: offer.freelancerId,
        content:
            languageCode == 'ar'
                ? _buildOfferIntroductionMessageArabic(offer, request)
                : _buildOfferIntroductionMessage(offer, request),
        createdAt: DateTime.now(),
        type: MessageType.text,
      );

      // Send the message
      await ChatService.sendMessage(introMessage);

      // Create system message about the offer
      final systemMessage = MessageModel(
        id: 'offer_system_${offer.id}',
        chatId: chat.id,
        senderId: 'system',
        content:
            languageCode == 'ar'
                ? '📋 تم إرسال عرض جديد: ${offer.price} ريال سعودي لمدة ${offer.deliveryDays} أيام تسليم'
                : '📋 New offer submitted: ${offer.price} SAR for ${offer.deliveryDays} days delivery',
        createdAt: DateTime.now().add(const Duration(seconds: 1)),
        type: MessageType.system,
      );

      await ChatService.sendMessage(systemMessage);

      print('✅ Introduction messages sent for offer: ${offer.id}');
    } catch (e) {
      print('❌ Error sending introduction message: $e');
    }
  }

  /// Build the introduction message content
  static String _buildOfferIntroductionMessage(OfferModel offer, ServiceRequestModel request) {
    return '''👋 Hello! I'm interested in your project "${request.title}".

💰 **My Offer:** ${offer.price} SAR
⏰ **Delivery Time:** ${offer.deliveryDays} days
📝 **Details:** ${offer.description}

I'm excited to work with you on this project. Please let me know if you have any questions or would like to discuss the details further.

Looking forward to your response! 🚀''';
  }

  /// Build Arabic version of introduction message
  static String _buildOfferIntroductionMessageArabic(OfferModel offer, ServiceRequestModel request) {
    return '''👋 مرحباً! أنا مهتم بمشروعك "${request.title}".

💰 **عرضي:** ${offer.price} ريال سعودي
⏰ **وقت التسليم:** ${offer.deliveryDays} أيام
📝 **التفاصيل:** ${offer.description}

أنا متحمس للعمل معك في هذا المشروع. يرجى إعلامي إذا كان لديك أي أسئلة أو تريد مناقشة التفاصيل أكثر.

في انتظار ردك! 🚀''';
  }

  /// Send offer update message when offer status changes
  static Future<void> sendOfferStatusMessage(String chatId, OfferModel offer, OfferStatus previousStatus) async {
    try {
      String messageContent;

      switch (offer.status) {
        case OfferStatus.accepted:
          messageContent = '✅ Great news! Your offer has been accepted. The client will proceed with payment.';
          break;
        case OfferStatus.rejected:
          messageContent = '❌ Your offer has been declined. ${offer.rejectionReason ?? "No reason provided."}';
          break;
        case OfferStatus.withdrawn:
          messageContent = '🔄 The offer has been withdrawn by the freelancer.';
          break;
        case OfferStatus.completed:
          messageContent = '🎉 Project completed successfully! Thank you for your excellent work.';
          break;
        default:
          return; // No message for pending status
      }

      final statusMessage = MessageModel(
        id: 'offer_status_${offer.id}_${DateTime.now().millisecondsSinceEpoch}',
        chatId: chatId,
        senderId: 'system',
        content: messageContent,
        createdAt: DateTime.now(),
        type: MessageType.system,
      );

      await ChatService.sendMessage(statusMessage);
    } catch (e) {
      print('❌ Error sending offer status message: $e');
    }
  }

  /// Get chat ID for an offer (if exists)
  static Future<String?> getChatIdForOffer(String offerId) async {
    try {
      // This would query the database to find chat associated with the offer
      // For demo purposes, we'll return a constructed ID
      return 'chat_offer_$offerId';
    } catch (e) {
      return null;
    }
  }

  /// Check if chat exists for a request
  static Future<bool> chatExistsForRequest(String requestId) async {
    try {
      final chat = await ChatService.getChatByRequestId(requestId);
      return chat != null;
    } catch (e) {
      return false;
    }
  }

  /// Create demo chat when request is not found (demo mode fallback)
  static Future<ChatModel?> _createDemoChatForOffer(OfferModel offer) async {
    try {
      print('🔄 Creating demo chat for offer: ${offer.id}');

      // Create demo chat with mock client ID
      final chat = ChatModel(
        id: 'demo_chat_${offer.requestId}_${DateTime.now().millisecondsSinceEpoch}',
        requestId: offer.requestId,
        clientId: 'demo_client_001', // Mock client ID for demo
        freelancerId: offer.freelancerId,
        createdAt: DateTime.now(),
      );

      // In demo mode, we'll just return the chat without saving to database
      print('✅ Demo chat created for offer: ${offer.id}');
      return chat;
    } catch (e) {
      print('❌ Error creating demo chat for offer: $e');
      return null;
    }
  }
}

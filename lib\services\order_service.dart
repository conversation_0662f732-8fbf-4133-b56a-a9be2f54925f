import '../config/supabase_config.dart';
import '../models/order_model.dart';
import '../models/notification_model.dart';
import '../models/chat_model.dart';
import '../models/user_model.dart';
import 'smart_notification_service.dart';
import 'timeline_service.dart';
import 'chat_service.dart';
import 'wallet_service.dart';

class OrderService {
  // Static cache for demo orders to persist changes during the session
  static List<OrderModel>? _cachedDemoOrders;

  /// Get user profile by ID from Supabase profiles table
  static Future<UserModel?> _getUserProfile(String userId) async {
    try {
      final response = await SupabaseConfig.client.from('profiles').select().eq('id', userId).single();
      return UserModel.fromJson(response);
    } catch (e) {
      // Return null if user not found or error occurs
      return null;
    }
  }

  static Future<List<OrderModel>> getOrders({String? clientId, String? freelancerId, OrderStatus? status}) async {
    try {
      // For demo purposes, return demo data instead of making API calls
      print('=== DEBUG: OrderService.getOrders called ===');
      print('ClientId: $clientId');
      print('FreelancerId: $freelancerId');
      print('Status: $status');

      final orders = _getDemoOrders(clientId: clientId, freelancerId: freelancerId, status: status);
      print('Returning ${orders.length} orders');
      print('=== END DEBUG ===');

      return orders;
    } catch (e) {
      throw Exception('Failed to fetch orders: $e');
    }
  }

  /// Demo data for orders - in production this would be removed
  static List<OrderModel> _getDemoOrders({String? clientId, String? freelancerId, OrderStatus? status}) {
    // Initialize cache if not already done
    if (_cachedDemoOrders == null) {
      final now = DateTime.now();

      // For demo purposes, always return demo data regardless of clientId
      // In production, you would filter by actual clientId

      _cachedDemoOrders = [
        // Order in progress - payment already confirmed
        OrderModel(
          id: 'order_0',
          requestId: 'req_0',
          clientId: clientId ?? 'demo_client_1',
          freelancerId: 'demo_freelancer_0',
          offerId: 'offer_0',
          amount: 950.0,
          status: OrderStatus.inProgress,
          paymentStatus: PaymentStatus.confirmed,
          createdAt: now.subtract(const Duration(hours: 2)),
          updatedAt: now.subtract(const Duration(hours: 2)),
        ),
        OrderModel(
          id: 'order_1',
          requestId: 'req_1',
          clientId: clientId ?? 'demo_client_1', // Use the actual client ID or demo
          freelancerId: 'demo_freelancer_1',
          offerId: 'offer_1',
          amount: 1500.0,
          status: OrderStatus.delivered,
          paymentStatus: PaymentStatus.confirmed,
          deliveryUrl: 'https://example.com/delivery1.zip',
          deliveryNotes:
              'Mobile app completed with all requested features. Please review and let me know if any changes are needed.',
          deliveryDate: now.subtract(const Duration(hours: 6)),
          createdAt: now.subtract(const Duration(days: 5)),
          updatedAt: now.subtract(const Duration(hours: 6)),
        ),
        OrderModel(
          id: 'order_2',
          requestId: 'req_2',
          clientId: clientId ?? 'demo_client_1', // Use the actual client ID or demo
          freelancerId: 'demo_freelancer_2',
          offerId: 'offer_2',
          amount: 800.0,
          status: OrderStatus.inProgress, // Changed to show Task Submission button
          paymentStatus: PaymentStatus.confirmed, // Changed to confirmed to show button
          createdAt: now.subtract(const Duration(days: 3)),
          updatedAt: now.subtract(const Duration(hours: 12)),
        ),
        OrderModel(
          id: 'order_3',
          requestId: 'req_3',
          clientId: clientId ?? 'demo_client_1', // Use the actual client ID or demo
          freelancerId: 'demo_freelancer_3',
          offerId: 'offer_3',
          amount: 1200.0,
          status: OrderStatus.inProgress, // Changed to show task submission button
          paymentStatus: PaymentStatus.confirmed,
          createdAt: now.subtract(const Duration(days: 7)),
          updatedAt: now.subtract(const Duration(hours: 8)),
        ),
        OrderModel(
          id: 'order_4',
          requestId: 'req_4',
          clientId: clientId ?? 'demo_client_1', // Use the actual client ID or demo
          freelancerId: 'demo_freelancer_4',
          offerId: 'offer_4',
          amount: 2000.0,
          status: OrderStatus.completed,
          paymentStatus: PaymentStatus.confirmed,
          deliveryUrl: 'https://example.com/delivery4.zip',
          deliveryNotes: 'E-commerce website completed with payment integration.',
          deliveryDate: now.subtract(const Duration(days: 10)),
          clientRating: 5.0,
          clientReview: 'Excellent work! Delivered on time and exceeded expectations.',
          createdAt: now.subtract(const Duration(days: 15)),
          updatedAt: now.subtract(const Duration(days: 8)),
        ),
      ];
    }

    // Filter by clientId
    var filteredOrders = _cachedDemoOrders!;
    filteredOrders = filteredOrders.where((order) => order.clientId == clientId).toList();

    // Filter by freelancerId
    if (freelancerId != null) {
      filteredOrders = filteredOrders.where((order) => order.freelancerId == freelancerId).toList();
    }

    // Filter by status
    if (status != null) {
      filteredOrders = filteredOrders.where((order) => order.status == status).toList();
    }

    return filteredOrders;
  }

  static Future<OrderModel> createOrder(OrderModel order) async {
    try {
      final response = await SupabaseConfig.client.from('orders').insert(order.toJson()).select().single();
      final createdOrder = OrderModel.fromJson(response);

      // Create chat for the order
      await _createChatForOrder(createdOrder);

      // Create timeline message for order creation
      await _createOrderCreationTimeline(createdOrder);

      return createdOrder;
    } catch (e) {
      throw Exception('Failed to create order: $e');
    }
  }

  static Future<OrderModel> updateOrder(OrderModel order) async {
    try {
      // Get the previous order state for comparison
      final previousOrder = await getOrderById(order.id);

      final response =
          await SupabaseConfig.client.from('orders').update(order.toJson()).eq('id', order.id).select().single();

      final updatedOrder = OrderModel.fromJson(response);

      // Create notification and timeline message if status changed
      if (previousOrder != null && previousOrder.status != updatedOrder.status) {
        await _createOrderStatusNotification(updatedOrder);
        await _createOrderStatusTimeline(updatedOrder, previousOrder.status);
      }

      return updatedOrder;
    } catch (e) {
      throw Exception('Failed to update order: $e');
    }
  }

  static Future<OrderModel> uploadPaymentProof(String orderId, String proofUrl) async {
    try {
      final response =
          await SupabaseConfig.client
              .from('orders')
              .update({
                'payment_proof_url': proofUrl,
                'payment_status': 'pending',
                'updated_at': DateTime.now().toIso8601String(),
              })
              .eq('id', orderId)
              .select()
              .single();

      return OrderModel.fromJson(response);
    } catch (e) {
      throw Exception('Failed to upload payment proof: $e');
    }
  }

  static Future<OrderModel> confirmPayment(String orderId, bool isConfirmed) async {
    try {
      final response =
          await SupabaseConfig.client
              .from('orders')
              .update({
                'payment_status': isConfirmed ? 'confirmed' : 'rejected',
                'status': isConfirmed ? 'paymentConfirmed' : 'paymentPending',
                'updated_at': DateTime.now().toIso8601String(),
              })
              .eq('id', orderId)
              .select()
              .single();

      return OrderModel.fromJson(response);
    } catch (e) {
      throw Exception('Failed to confirm payment: $e');
    }
  }

  /// Submit task for review (freelancer action)
  static Future<OrderModel> submitTask(String orderId) async {
    try {
      print('=== DEBUG: OrderService.submitTask called ===');
      print('OrderId: $orderId');

      // For demo purposes, update the cached demo data
      if (_cachedDemoOrders == null) {
        await getOrders(); // Initialize cache
      }

      final orderIndex = _cachedDemoOrders!.indexWhere((order) => order.id == orderId);
      if (orderIndex == -1) {
        throw Exception('Order not found');
      }

      final currentOrder = _cachedDemoOrders![orderIndex];
      print('=== DEBUG: Current order status: ${currentOrder.status} ===');

      final updatedOrder = currentOrder.copyWith(status: OrderStatus.submitted, updatedAt: DateTime.now());

      // Update the cached demo data (in production this would be a database update)
      _cachedDemoOrders![orderIndex] = updatedOrder;
      print('=== DEBUG: Order updated in cache, new status: ${updatedOrder.status} ===');

      // Create notification and timeline message for task submission
      await _createOrderStatusNotification(updatedOrder);
      await _createOrderStatusTimeline(updatedOrder, currentOrder.status);

      return updatedOrder;
    } catch (e) {
      print('=== DEBUG: Error in submitTask: $e ===');
      throw Exception('Failed to submit task: $e');
    }
  }

  static Future<OrderModel> deliverOrder(String orderId, String deliveryUrl, String? notes) async {
    try {
      print('=== DEBUG: OrderService.deliverOrder called for order: $orderId ===');
      // For demo purposes, simulate the order delivery
      await Future.delayed(const Duration(milliseconds: 500)); // Simulate API delay

      // Initialize cache if needed
      _getDemoOrders();
      print('=== DEBUG: Cache initialized, ${_cachedDemoOrders!.length} orders in cache ===');

      // Find the order in cached demo data and update it
      final orderIndex = _cachedDemoOrders!.indexWhere((order) => order.id == orderId);
      print('=== DEBUG: Order index found: $orderIndex ===');

      if (orderIndex == -1) {
        print('=== DEBUG: Order not found in cache ===');
        throw Exception('Order not found');
      }

      final currentOrder = _cachedDemoOrders![orderIndex];
      print('=== DEBUG: Current order status: ${currentOrder.status} ===');
      final updatedOrder = currentOrder.copyWith(
        status: OrderStatus.delivered,
        deliveryUrl: deliveryUrl,
        deliveryNotes: notes,
        deliveryDate: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Update the cached demo data (in production this would be a database update)
      _cachedDemoOrders![orderIndex] = updatedOrder;
      print('=== DEBUG: Order updated in cache, new status: ${updatedOrder.status} ===');

      // Create notification and timeline message for delivery
      await _createOrderStatusNotification(updatedOrder);
      await _createOrderStatusTimeline(updatedOrder, currentOrder.status);

      return updatedOrder;
    } catch (e) {
      print('=== DEBUG: Error in deliverOrder: $e ===');
      throw Exception('Failed to deliver order: $e');
    }
  }

  /// Confirm delivery by freelancer (new method for delivery workflow)
  static Future<OrderModel> confirmDelivery({
    required String orderId,
    required List<String> fileUrls,
    String? notes,
  }) async {
    try {
      final deliveryUrl = fileUrls.isNotEmpty ? fileUrls.first : '';
      final updatedOrder = await deliverOrder(orderId, deliveryUrl, notes);

      // Create specific delivery confirmation timeline
      final chat = await _getChatForOrder(updatedOrder);
      if (chat != null) {
        await TimelineService.createDeliveryConfirmationTimeline(
          chatId: chat.id,
          orderId: orderId,
          deliveryNotes: notes ?? 'Work has been delivered',
          fileUrls: fileUrls,
        );
      }

      return updatedOrder;
    } catch (e) {
      throw Exception('Failed to confirm delivery: $e');
    }
  }

  /// Confirm receipt by client (new method for delivery workflow)
  static Future<OrderModel> confirmReceipt({required String orderId, required double rating, String? review}) async {
    try {
      final completedOrder = await completeOrder(orderId, rating, review);

      // Create specific receipt confirmation timeline
      final chat = await _getChatForOrder(completedOrder);
      if (chat != null) {
        await TimelineService.createReceiptConfirmationTimeline(
          chatId: chat.id,
          orderId: orderId,
          rating: rating,
          review: review,
        );
      }

      // Update freelancer wallet balance
      await _updateFreelancerBalance(completedOrder.freelancerId, completedOrder.amount, completedOrder.id);

      return completedOrder;
    } catch (e) {
      throw Exception('Failed to confirm receipt: $e');
    }
  }

  /// Get orders by status
  static Future<List<OrderModel>> getOrdersByStatus(OrderStatus status) async {
    try {
      final response = await SupabaseConfig.client
          .from('orders')
          .select()
          .eq('status', status.toString().split('.').last)
          .order('created_at', ascending: false);

      return response.map<OrderModel>((json) => OrderModel.fromJson(json)).toList();
    } catch (e) {
      // For demo purposes, return filtered demo orders
      final demoOrders = _getDemoOrders();
      return demoOrders.where((order) => order.status == status).toList();
    }
  }

  /// Get chat for an order
  static Future<ChatModel?> _getChatForOrder(OrderModel order) async {
    try {
      return await ChatService.getChatByRequestId(order.requestId);
    } catch (e) {
      print('Error getting chat for order ${order.id}: $e');
      return null;
    }
  }

  /// Update freelancer wallet balance
  static Future<void> _updateFreelancerBalance(String freelancerId, double amount, [String? orderId]) async {
    try {
      // Add earnings to freelancer's wallet
      await WalletService.addEarnings(
        userId: freelancerId,
        amount: amount,
        orderId: orderId ?? 'unknown_order',
        description: 'Payment for completed order',
      );

      print('Successfully added $amount to freelancer $freelancerId wallet');
    } catch (e) {
      print('Error updating freelancer balance: $e');
      // For demo purposes, continue without failing
    }
  }

  static Future<OrderModel> completeOrder(String orderId, double rating, String? review) async {
    try {
      // For demo purposes, simulate the order completion
      await Future.delayed(const Duration(milliseconds: 500)); // Simulate API delay

      // Find the order in demo data and update it
      final demoOrders = _getDemoOrders();
      final orderIndex = demoOrders.indexWhere((order) => order.id == orderId);

      if (orderIndex == -1) {
        throw Exception('Order not found');
      }

      final currentOrder = demoOrders[orderIndex];
      final updatedOrder = currentOrder.copyWith(
        status: OrderStatus.completed,
        clientRating: rating,
        clientReview: review,
        updatedAt: DateTime.now(),
      );

      // In a real app, this would update the database
      // For demo, we just return the updated order
      return updatedOrder;
    } catch (e) {
      throw Exception('Failed to complete order: $e');
    }
  }

  static Future<OrderModel> requestRevision(String orderId, String revisionNotes) async {
    try {
      // For demo purposes, simulate the revision request
      await Future.delayed(const Duration(milliseconds: 500)); // Simulate API delay

      // Find the order in demo data and update it
      final demoOrders = _getDemoOrders();
      final orderIndex = demoOrders.indexWhere((order) => order.id == orderId);

      if (orderIndex == -1) {
        throw Exception('Order not found');
      }

      final currentOrder = demoOrders[orderIndex];
      final updatedOrder = currentOrder.copyWith(
        status: OrderStatus.editing,
        revisionNotes: revisionNotes,
        revisionRequestedAt: DateTime.now(),
        revisionCount: currentOrder.revisionCount + 1,
        updatedAt: DateTime.now(),
      );

      // In a real app, this would update the database
      // For demo, we just return the updated order
      return updatedOrder;
    } catch (e) {
      throw Exception('Failed to request revision: $e');
    }
  }

  static Future<OrderModel> cancelOrder(String orderId, String? cancellationReason) async {
    try {
      // For demo purposes, simulate the order cancellation
      await Future.delayed(const Duration(milliseconds: 500)); // Simulate API delay

      // Find the order in demo data and update it
      final demoOrders = _getDemoOrders();
      final orderIndex = demoOrders.indexWhere((order) => order.id == orderId);

      if (orderIndex == -1) {
        throw Exception('Order not found');
      }

      final currentOrder = demoOrders[orderIndex];

      // Check if order can be cancelled (only allow cancellation for certain statuses)
      if (currentOrder.status != OrderStatus.created && currentOrder.status != OrderStatus.paymentConfirmed) {
        throw Exception('Order cannot be cancelled at this stage');
      }

      final updatedOrder = currentOrder.copyWith(status: OrderStatus.cancelled, updatedAt: DateTime.now());

      // Update the demo data (in production this would be a database update)
      demoOrders[orderIndex] = updatedOrder;

      // Create notification and timeline for cancellation
      await _createOrderStatusNotification(updatedOrder);
      await _createOrderStatusTimeline(updatedOrder, currentOrder.status);

      return updatedOrder;
    } catch (e) {
      throw Exception('Failed to cancel order: $e');
    }
  }

  static Future<List<OrderModel>> getPendingPayments() async {
    try {
      final response = await SupabaseConfig.client
          .from('orders')
          .select()
          .eq('payment_status', 'pending')
          .order('created_at', ascending: false);

      return response.map<OrderModel>((json) => OrderModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to fetch pending payments: $e');
    }
  }

  static Future<OrderModel?> getOrderById(String orderId) async {
    try {
      // For demo purposes, find order in cached demo data
      _getDemoOrders(); // Initialize cache if needed
      final order = _cachedDemoOrders!.firstWhere(
        (order) => order.id == orderId,
        orElse: () => throw Exception('Order not found'),
      );
      return order;
    } catch (e) {
      return null;
    }
  }

  /// Create notification for order status changes
  static Future<void> _createOrderStatusNotification(OrderModel order) async {
    try {
      switch (order.status) {
        case OrderStatus.paymentConfirmed:
          // Use smart notification for payment confirmation
          await SmartNotificationService.notifyPaymentConfirmed(
            clientId: order.clientId,
            freelancerId: order.freelancerId,
            orderId: order.id,
            amount: order.amount,
          );
          break;
        case OrderStatus.delivered:
          // Get freelancer profile for name
          final freelancerProfile = await _getUserProfile(order.freelancerId);
          final freelancerName = freelancerProfile?.fullName ?? 'Freelancer';

          // Use smart notification for work delivery
          await SmartNotificationService.notifyWorkDelivered(
            clientId: order.clientId,
            orderId: order.id,
            freelancerName: freelancerName,
            deliveryNotes: order.deliveryNotes,
            fileUrls: order.deliveryUrl != null ? [order.deliveryUrl!] : null,
          );
          break;
        case OrderStatus.inProgress:
          await SmartNotificationService.createSmartNotification(
            userId: order.clientId,
            titleEn: 'Work Started',
            titleAr: 'بدأ العمل',
            descriptionEn: 'Work has started on your order #${order.id}.',
            descriptionAr: 'بدأ العمل في طلبك #${order.id}.',
            type: NotificationType.orderAccepted,
            relatedId: order.id,
            priority: NotificationPriority.high,
            actionUrl: '/orders/${order.id}',
          );
          break;
        case OrderStatus.submitted:
          await SmartNotificationService.createSmartNotification(
            userId: order.clientId,
            titleEn: 'Task Submitted',
            titleAr: 'تم تسليم المهمة',
            descriptionEn: 'The freelancer has submitted the task for order #${order.id}.',
            descriptionAr: 'قام المستقل بتسليم المهمة للطلب رقم #${order.id}.',
            type: NotificationType.orderStatus,
            relatedId: order.id,
            priority: NotificationPriority.high,
            actionUrl: '/orders/${order.id}',
          );
          break;
        case OrderStatus.editing:
          await SmartNotificationService.createSmartNotification(
            userId: order.freelancerId,
            titleEn: 'Revision Requested',
            titleAr: 'تم طلب مراجعة',
            descriptionEn: 'The client has requested a revision for order #${order.id}.',
            descriptionAr: 'طلب العميل مراجعة للطلب #${order.id}.',
            type: NotificationType.revisionRequested,
            relatedId: order.id,
            priority: NotificationPriority.high,
            actionUrl: '/orders/${order.id}',
          );
          break;
        case OrderStatus.completed:
          await SmartNotificationService.createSmartNotification(
            userId: order.clientId,
            titleEn: 'Order Completed',
            titleAr: 'تم إكمال الطلب',
            descriptionEn: 'Your order #${order.id} has been completed successfully.',
            descriptionAr: 'تم إكمال طلبك #${order.id} بنجاح.',
            type: NotificationType.orderCompleted,
            relatedId: order.id,
            priority: NotificationPriority.normal,
            actionUrl: '/orders/${order.id}',
          );

          // Notify freelancer
          await SmartNotificationService.createSmartNotification(
            userId: order.freelancerId,
            titleEn: 'Order Completed',
            titleAr: 'تم إكمال الطلب',
            descriptionEn: 'Order #${order.id} has been marked as completed by the client.',
            descriptionAr: 'تم وضع علامة إكمال على الطلب #${order.id} من قبل العميل.',
            type: NotificationType.orderCompleted,
            relatedId: order.id,
            priority: NotificationPriority.normal,
            actionUrl: '/orders/${order.id}',
          );
          break;
        case OrderStatus.cancelled:
          await SmartNotificationService.createSmartNotification(
            userId: order.clientId,
            titleEn: 'Order Cancelled',
            titleAr: 'تم إلغاء الطلب',
            descriptionEn: 'Your order #${order.id} has been cancelled.',
            descriptionAr: 'تم إلغاء طلبك #${order.id}.',
            type: NotificationType.orderCancelled,
            relatedId: order.id,
            priority: NotificationPriority.high,
            actionUrl: '/orders/${order.id}',
          );
          break;
        default:
          return; // Don't create notification for other statuses
      }
    } catch (e) {
      // Don't fail order update if notification fails
      print('Failed to create order status notification: $e');
    }
  }

  /// Create timeline message for order status changes
  static Future<void> _createOrderStatusTimeline(OrderModel order, OrderStatus previousStatus) async {
    try {
      // Get the chat for this order
      final chat = await ChatService.getChatByRequestId(order.requestId);
      if (chat == null) return;

      switch (order.status) {
        case OrderStatus.paymentConfirmed:
          await TimelineService.createPaymentConfirmedTimeline(
            chatId: chat.id,
            orderId: order.id,
            amount: order.amount,
          );
          break;
        case OrderStatus.inProgress:
          // Get freelancer profile to get their name
          final freelancerProfile = await _getUserProfile(order.freelancerId);
          final freelancerName = freelancerProfile?.fullName ?? 'Freelancer';

          await TimelineService.createOrderAcceptedTimeline(
            chatId: chat.id,
            orderId: order.id,
            freelancerName: freelancerName,
          );
          break;
        case OrderStatus.submitted:
          await TimelineService.createTaskSubmittedTimeline(chatId: chat.id, orderId: order.id);
          break;
        case OrderStatus.delivered:
          await TimelineService.createWorkDeliveredTimeline(
            chatId: chat.id,
            orderId: order.id,
            deliveryNotes: order.deliveryNotes ?? 'Work has been delivered',
            fileUrls: order.deliveryUrl != null ? [order.deliveryUrl!] : null,
          );
          break;
        case OrderStatus.editing:
          await TimelineService.createRevisionRequestedTimeline(
            chatId: chat.id,
            orderId: order.id,
            revisionNotes: order.revisionNotes ?? 'Revision requested',
          );
          break;
        case OrderStatus.completed:
          await TimelineService.createOrderCompletedTimeline(
            chatId: chat.id,
            orderId: order.id,
            rating: order.clientRating,
            review: order.clientReview,
          );
          break;
        case OrderStatus.cancelled:
          await TimelineService.createOrderCancelledTimeline(
            chatId: chat.id,
            orderId: order.id,
            reason: 'Order was cancelled',
          );
          break;
        default:
          // No timeline message for other statuses
          break;
      }
    } catch (e) {
      // Don't fail order update if timeline creation fails
      print('Failed to create order status timeline: $e');
    }
  }

  /// Create chat for a new order
  static Future<void> _createChatForOrder(OrderModel order) async {
    try {
      // Check if chat already exists
      final existingChat = await ChatService.getChatByRequestId(order.requestId);
      if (existingChat != null) return;

      // Create new chat
      final chat = ChatModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        requestId: order.requestId,
        clientId: order.clientId,
        freelancerId: order.freelancerId,
        createdAt: DateTime.now(),
      );

      await ChatService.createChat(chat);
    } catch (e) {
      // Don't fail order creation if chat creation fails
      print('Failed to create chat for order: $e');
    }
  }

  /// Create timeline message for order creation
  static Future<void> _createOrderCreationTimeline(OrderModel order) async {
    try {
      final chat = await ChatService.getChatByRequestId(order.requestId);
      if (chat == null) return;

      await TimelineService.createOrderCreatedTimeline(
        chatId: chat.id,
        orderId: order.id,
        amount: order.amount,
        serviceTitle: 'Service Order', // Generic title since OrderModel doesn't have serviceTitle
      );
    } catch (e) {
      // Don't fail order creation if timeline creation fails
      print('Failed to create order creation timeline: $e');
    }
  }
}

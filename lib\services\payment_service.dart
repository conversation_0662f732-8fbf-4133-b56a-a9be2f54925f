import '../config/supabase_config.dart';
import '../models/payment_request_model.dart';
import '../models/notification_model.dart';

class PaymentService {
  // Create a payment request when client clicks "Pay Now"
  static Future<PaymentRequestModel> createPaymentRequest({
    required String requestId,
    required String clientId,
    required String freelancerId,
    required String offerId,
    required double amount,
  }) async {
    try {
      final paymentRequest = PaymentRequestModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        requestId: requestId,
        clientId: clientId,
        freelancerId: freelancerId,
        offerId: offerId,
        amount: amount,
        createdAt: DateTime.now(),
      );

      final response =
          await SupabaseConfig.client.from('payment_requests').insert(paymentRequest.toJson()).select().single();

      return PaymentRequestModel.fromJson(response);
    } catch (e) {
      throw Exception('Failed to create payment request: $e');
    }
  }

  // Upload payment proof
  static Future<PaymentRequestModel> uploadPaymentProof({
    required String paymentRequestId,
    required String proofUrl,
  }) async {
    try {
      final response =
          await SupabaseConfig.client
              .from('payment_requests')
              .update({
                'payment_proof_url': proofUrl,
                'status': 'pending',
                'updated_at': DateTime.now().toIso8601String(),
              })
              .eq('id', paymentRequestId)
              .select()
              .single();

      return PaymentRequestModel.fromJson(response);
    } catch (e) {
      throw Exception('Failed to upload payment proof: $e');
    }
  }

  // Admin confirms payment
  static Future<PaymentRequestModel> confirmPayment({
    required String paymentRequestId,
    required String adminId,
    String? adminNotes,
  }) async {
    try {
      final response =
          await SupabaseConfig.client
              .from('payment_requests')
              .update({
                'status': 'confirmed',
                'confirmed_at': DateTime.now().toIso8601String(),
                'confirmed_by_admin_id': adminId,
                'admin_notes': adminNotes,
                'updated_at': DateTime.now().toIso8601String(),
              })
              .eq('id', paymentRequestId)
              .select()
              .single();

      final paymentRequest = PaymentRequestModel.fromJson(response);

      // Note: Job assignment functionality removed - integrate with offers system if needed

      // Send notification to freelancer
      await _notifyFreelancer(paymentRequest);

      return paymentRequest;
    } catch (e) {
      throw Exception('Failed to confirm payment: $e');
    }
  }

  // Admin rejects payment
  static Future<PaymentRequestModel> rejectPayment({
    required String paymentRequestId,
    required String adminId,
    String? adminNotes,
  }) async {
    try {
      final response =
          await SupabaseConfig.client
              .from('payment_requests')
              .update({
                'status': 'rejected',
                'confirmed_by_admin_id': adminId,
                'admin_notes': adminNotes,
                'updated_at': DateTime.now().toIso8601String(),
              })
              .eq('id', paymentRequestId)
              .select()
              .single();

      return PaymentRequestModel.fromJson(response);
    } catch (e) {
      throw Exception('Failed to reject payment: $e');
    }
  }

  // Get pending payment requests for admin
  static Future<List<PaymentRequestModel>> getPendingPaymentRequests() async {
    try {
      final response = await SupabaseConfig.client
          .from('payment_requests')
          .select()
          .eq('status', 'pending')
          .order('created_at', ascending: false);

      return response.map<PaymentRequestModel>((json) => PaymentRequestModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to fetch pending payment requests: $e');
    }
  }

  // Get payment request by ID
  static Future<PaymentRequestModel?> getPaymentRequest(String id) async {
    try {
      final response = await SupabaseConfig.client.from('payment_requests').select().eq('id', id).single();

      return PaymentRequestModel.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  // Get payment requests for a specific request
  static Future<List<PaymentRequestModel>> getPaymentRequestsForRequest(String requestId) async {
    try {
      final response = await SupabaseConfig.client
          .from('payment_requests')
          .select()
          .eq('request_id', requestId)
          .order('created_at', ascending: false);

      return response.map<PaymentRequestModel>((json) => PaymentRequestModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to fetch payment requests: $e');
    }
  }

  // Private method to notify freelancer
  static Future<void> _notifyFreelancer(PaymentRequestModel paymentRequest) async {
    try {
      final notification = NotificationModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: paymentRequest.freelancerId,
        titleEn: 'Payment Confirmed',
        titleAr: 'تم تأكيد الدفع',
        descriptionEn: 'Payment has been confirmed. You may now start the job.',
        descriptionAr: 'تم تأكيد الدفع. يمكنك الآن البدء في العمل.',
        type: NotificationType.paymentConfirmed,
        relatedId: paymentRequest.id,
        createdAt: DateTime.now(),
      );

      await SupabaseConfig.client.from('notifications').insert(notification.toJson());
    } catch (e) {
      throw Exception('Failed to send notification: $e');
    }
  }

  // Get bank account details for payment
  static Map<String, String> getBankAccountDetails() {
    return {
      'bankName': 'FreelanceHub Bank',
      'accountName': 'FreelanceHub Payments',
      'iban': '************************',
      'swiftCode': 'FHUBSARI',
      'accountNumber': '**********',
      'instructions': 'Please include your payment request ID in the transfer reference.',
    };
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../models/notification_model.dart';
import '../../providers/theme_provider.dart';
import '../../providers/language_provider.dart';

/// Toast notification widget for in-app notifications
class ToastNotification extends StatefulWidget {
  final NotificationModel notification;
  final VoidCallback? onTap;
  final VoidCallback? onDismiss;
  final Duration duration;
  final bool isArabic;

  const ToastNotification({
    super.key,
    required this.notification,
    this.onTap,
    this.onDismiss,
    this.duration = const Duration(seconds: 4),
    required this.isArabic,
  });

  @override
  State<ToastNotification> createState() => _ToastNotificationState();
}

class _ToastNotificationState extends State<ToastNotification> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(duration: const Duration(milliseconds: 300), vsync: this);

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeOut));

    _animationController.forward();

    // Auto dismiss after duration
    Future.delayed(widget.duration, () {
      if (mounted) {
        _dismiss();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _dismiss() async {
    await _animationController.reverse();
    if (mounted) {
      widget.onDismiss?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return SlideTransition(
          position: _slideAnimation,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Material(
                elevation: 8,
                borderRadius: BorderRadius.circular(12),
                color: isDark ? ThemeProvider.darkCardBackground : Colors.white,
                child: InkWell(
                  onTap: () {
                    HapticFeedback.lightImpact();
                    widget.onTap?.call();
                    _dismiss();
                  },
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: _getNotificationColor().withValues(alpha: 0.3), width: 1),
                    ),
                    child: Row(
                      children: [
                        // Notification Icon
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: _getNotificationColor().withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Icon(_getNotificationIcon(), color: _getNotificationColor(), size: 20),
                        ),
                        const SizedBox(width: 12),

                        // Notification Content
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                widget.notification.getTitle(widget.isArabic ? 'ar' : 'en'),
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                widget.notification.getDescription(widget.isArabic ? 'ar' : 'en'),
                                style: TextStyle(
                                  fontSize: 12,
                                  color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),

                        // Priority Indicator
                        if (widget.notification.priority == NotificationPriority.high ||
                            widget.notification.priority == NotificationPriority.urgent)
                          Container(
                            width: 8,
                            height: 8,
                            margin: const EdgeInsets.only(left: 8),
                            decoration: BoxDecoration(
                              color:
                                  widget.notification.priority == NotificationPriority.urgent
                                      ? Colors.red
                                      : Colors.orange,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),

                        // Dismiss Button
                        IconButton(
                          onPressed: _dismiss,
                          icon: Icon(
                            Icons.close,
                            size: 16,
                            color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
                          ),
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Color _getNotificationColor() {
    switch (widget.notification.type) {
      case NotificationType.orderNew:
      case NotificationType.offerReceived:
        return Colors.blue;
      case NotificationType.orderAccepted:
      case NotificationType.paymentConfirmed:
      case NotificationType.orderCompleted:
        return Colors.green;
      case NotificationType.orderDelivered:
      case NotificationType.workDelivered:
        return Colors.orange;
      case NotificationType.orderCancelled:
      case NotificationType.offerRejected:
        return Colors.red;
      case NotificationType.newMessage:
      case NotificationType.messageReceived:
        return Colors.blue;
      case NotificationType.systemAnnouncement:
      case NotificationType.adminMessage:
        return Colors.purple;
      case NotificationType.reminderPaymentDue:
      case NotificationType.reminderDeliveryPending:
      case NotificationType.reminderReviewPending:
      case NotificationType.reminderResponseNeeded:
      case NotificationType.reminderGeneral:
        return Colors.amber;
      default:
        return ThemeProvider.primaryBlue;
    }
  }

  IconData _getNotificationIcon() {
    switch (widget.notification.type) {
      case NotificationType.orderNew:
      case NotificationType.orderAccepted:
      case NotificationType.orderDelivered:
      case NotificationType.orderCompleted:
      case NotificationType.orderCancelled:
        return Icons.shopping_bag;
      case NotificationType.offerReceived:
      case NotificationType.offerAccepted:
      case NotificationType.offerRejected:
        return Icons.local_offer;
      case NotificationType.paymentConfirmed:
      case NotificationType.paymentPending:
        return Icons.payment;
      case NotificationType.newMessage:
      case NotificationType.messageReceived:
        return Icons.message;
      case NotificationType.workDelivered:
        return Icons.file_download;
      case NotificationType.systemAnnouncement:
      case NotificationType.adminMessage:
        return Icons.campaign;
      case NotificationType.reminderPaymentDue:
      case NotificationType.reminderDeliveryPending:
      case NotificationType.reminderReviewPending:
      case NotificationType.reminderResponseNeeded:
      case NotificationType.reminderGeneral:
        return Icons.schedule;
      case NotificationType.revisionRequested:
        return Icons.edit;
      case NotificationType.accountVerification:
        return Icons.verified_user;
      default:
        return Icons.notifications;
    }
  }
}

/// Toast notification manager to show notifications
class ToastNotificationManager {
  static OverlayEntry? _currentToast;

  /// Show a toast notification
  static void show({
    required BuildContext context,
    required NotificationModel notification,
    VoidCallback? onTap,
    Duration duration = const Duration(seconds: 4),
  }) {
    // Remove existing toast if any
    dismiss();

    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    final isArabic = languageProvider.isArabic;

    _currentToast = OverlayEntry(
      builder:
          (context) => Positioned(
            top: MediaQuery.of(context).padding.top + 10,
            left: 0,
            right: 0,
            child: ToastNotification(
              notification: notification,
              onTap: onTap,
              onDismiss: dismiss,
              duration: duration,
              isArabic: isArabic,
            ),
          ),
    );

    Overlay.of(context).insert(_currentToast!);
  }

  /// Dismiss current toast
  static void dismiss() {
    _currentToast?.remove();
    _currentToast = null;
  }
}

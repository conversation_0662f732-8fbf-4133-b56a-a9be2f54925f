{
    "dart.flutterSdkPath": "C:\\src\\flutter",
    "debug.enableStatusBarColor": false,
    // Auto-save settings for instant hot reload
    "files.autoSave": "afterDelay",
    "files.autoSaveDelay": 300,
    // Hot reload settings
    "dart.flutterHotReloadOnSave": "all",
    "dart.hotReloadOnSave": "all",
    "dart.previewHotReloadOnSaveWatcher": true,
    "dart.flutterHotRestartOnSave": "never",
    // Additional Flutter development settings
    "dart.debugExternalPackageLibraries": false,
    "dart.debugSdkLibraries": false,
    "dart.evaluateGettersInDebugViews": true,
    "dart.showInspectorNotificationsForWidgetErrors": false,
    // Disable debug overlays and inspector
    "dart.flutterShowWebServerDevice": false,
    "dart.showMainCodeLens": false,
    "dart.showTestCodeLens": false,
    // File watching for better hot reload
    "files.watcherExclude": {
        "**/.git/objects/**": true,
        "**/.git/subtree-cache/**": true,
        "**/node_modules/*/**": true,
        "**/.hg/store/**": true,
        "**/build/**": true,
        "**/.dart_tool/**": true
    },
    // Editor settings for better development experience
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.fixAll": "explicit"
    },
    // Dart/Flutter specific editor settings
    "dart.lineLength": 120,
    "dart.insertArgumentPlaceholders": false,
    "dart.completeFunctionCalls": true,
    "dart.showTodos": true
}
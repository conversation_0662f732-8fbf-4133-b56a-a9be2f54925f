import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/notification_model.dart';
import '../../providers/demo_auth_provider.dart';
import '../../providers/language_provider.dart';
import '../../providers/theme_provider.dart';
import '../../services/notification_integration_service.dart';
import '../../widgets/common/enhanced_widgets.dart';
import '../../widgets/notifications/toast_notification.dart';
import '../../widgets/notifications/notification_preview.dart';

class NotificationDemoScreen extends StatefulWidget {
  const NotificationDemoScreen({super.key});

  @override
  State<NotificationDemoScreen> createState() => _NotificationDemoScreenState();
}

class _NotificationDemoScreenState extends State<NotificationDemoScreen> {
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeNotificationSystem();
  }

  Future<void> _initializeNotificationSystem() async {
    setState(() => _isLoading = true);
    await NotificationIntegrationService.initialize();
    setState(() => _isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Consumer3<DemoAuthProvider, LanguageProvider, ThemeProvider>(
      builder: (context, authProvider, languageProvider, themeProvider, child) {
        final isArabic = languageProvider.isArabic;
        final isDark = themeProvider.isDarkMode;
        final userId = authProvider.user?.id ?? 'demo_user';

        return Directionality(
          textDirection: languageProvider.textDirection,
          child: Scaffold(
            backgroundColor: isDark ? ThemeProvider.darkBackground : ThemeProvider.lightBackground,
            appBar: AppBar(
              title: Text(
                isArabic ? 'عرض نظام الإشعارات' : 'Notification System Demo',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              backgroundColor: ThemeProvider.primaryBlue,
              foregroundColor: Colors.white,
              elevation: 0,
            ),
            body:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Notification Preview Widget
                          NotificationPreview(
                            onViewAll: () {
                              Navigator.pushNamed(context, '/notifications');
                            },
                          ),

                          const SizedBox(height: 24),

                          // Test Buttons Section
                          _buildSectionTitle(isArabic ? 'اختبار الإشعارات' : 'Test Notifications', isDark),
                          const SizedBox(height: 16),

                          // Core Notification Tests
                          _buildTestButtonGrid(userId, isArabic, isDark),

                          const SizedBox(height: 24),

                          // Advanced Tests Section
                          _buildSectionTitle(isArabic ? 'اختبارات متقدمة' : 'Advanced Tests', isDark),
                          const SizedBox(height: 16),

                          _buildAdvancedTestButtons(userId, isArabic, isDark),

                          const SizedBox(height: 24),

                          // Statistics Section
                          _buildSectionTitle(isArabic ? 'إحصائيات النظام' : 'System Statistics', isDark),
                          const SizedBox(height: 16),

                          _buildStatisticsCard(isDark, isArabic),
                        ],
                      ),
                    ),
          ),
        );
      },
    );
  }

  Widget _buildSectionTitle(String title, bool isDark) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
      ),
    );
  }

  Widget _buildTestButtonGrid(String userId, bool isArabic, bool isDark) {
    final testButtons = [
      _TestButton(
        titleEn: 'Offer Received',
        titleAr: 'عرض مستلم',
        icon: Icons.local_offer,
        color: Colors.blue,
        onTap: () => _testNotification(userId, NotificationType.offerReceived),
      ),
      _TestButton(
        titleEn: 'Payment Confirmed',
        titleAr: 'تأكيد الدفع',
        icon: Icons.payment,
        color: Colors.green,
        onTap: () => _testNotification(userId, NotificationType.paymentConfirmed),
      ),
      _TestButton(
        titleEn: 'Work Delivered',
        titleAr: 'تسليم العمل',
        icon: Icons.file_download,
        color: Colors.orange,
        onTap: () => _testNotification(userId, NotificationType.workDelivered),
      ),
      _TestButton(
        titleEn: 'Payment Reminder',
        titleAr: 'تذكير الدفع',
        icon: Icons.schedule,
        color: Colors.amber,
        onTap: () => _testNotification(userId, NotificationType.reminderPaymentDue),
      ),
      _TestButton(
        titleEn: 'System Announcement',
        titleAr: 'إعلان النظام',
        icon: Icons.campaign,
        color: Colors.purple,
        onTap: () => _testNotification(userId, NotificationType.systemAnnouncement),
      ),
      _TestButton(
        titleEn: 'Toast Notification',
        titleAr: 'إشعار منبثق',
        icon: Icons.notifications_active,
        color: Colors.teal,
        onTap: () => _showToastNotification(userId, isArabic),
      ),
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.2,
      ),
      itemCount: testButtons.length,
      itemBuilder: (context, index) {
        final button = testButtons[index];
        return _buildTestButton(button, isArabic, isDark);
      },
    );
  }

  Widget _buildTestButton(_TestButton button, bool isArabic, bool isDark) {
    return EnhancedCard(
      onTap: button.onTap,
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: button.color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(24),
            ),
            child: Icon(button.icon, color: button.color, size: 24),
          ),
          const SizedBox(height: 12),
          Text(
            isArabic ? button.titleAr : button.titleEn,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildAdvancedTestButtons(String userId, bool isArabic, bool isDark) {
    return Column(
      children: [
        EnhancedButton(
          text: isArabic ? 'اختبار شامل للنظام' : 'Run Full System Test',
          icon: Icons.play_arrow,
          backgroundColor: ThemeProvider.primaryBlue,
          onPressed: () => _runFullSystemTest(userId),
        ),
        const SizedBox(height: 12),
        EnhancedButton(
          text: isArabic ? 'محاكاة سيناريوهات واقعية' : 'Simulate Real Scenarios',
          icon: Icons.science,
          backgroundColor: Colors.green,
          onPressed: () => _simulateRealScenarios(userId),
        ),
        const SizedBox(height: 12),
        EnhancedButton(
          text: isArabic ? 'اختبار اللغات المتعددة' : 'Test Multi-Language',
          icon: Icons.language,
          backgroundColor: Colors.orange,
          onPressed: () => _testMultiLanguage(userId),
        ),
      ],
    );
  }

  Widget _buildStatisticsCard(bool isDark, bool isArabic) {
    return EnhancedCard(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'حالة النظام' : 'System Status',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          _buildStatusRow(isArabic ? 'نظام الإشعارات الذكي' : 'Smart Notification System', '✅ Active', isDark),
          _buildStatusRow(isArabic ? 'نظام التذكيرات' : 'Reminder System', '✅ Running', isDark),
          _buildStatusRow(isArabic ? 'إشعارات الإدارة' : 'Admin Notifications', '✅ Ready', isDark),
          _buildStatusRow(isArabic ? 'الإشعارات المنبثقة' : 'Push Notifications', '⚠️ Demo Mode', isDark),
        ],
      ),
    );
  }

  Widget _buildStatusRow(String label, String status, bool isDark) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(fontSize: 14, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
          ),
          Text(
            status,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _testNotification(String userId, NotificationType type) async {
    try {
      await NotificationIntegrationService.quickTest(userId: userId, type: type, context: context);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('✅ ${type.name} notification sent!'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Failed to send notification: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _showToastNotification(String userId, bool isArabic) {
    final notification = NotificationModel(
      id: 'toast_demo_${DateTime.now().millisecondsSinceEpoch}',
      userId: userId,
      titleEn: 'Toast Demo',
      titleAr: 'عرض التوست',
      descriptionEn: 'This is a demo toast notification with smooth animations!',
      descriptionAr: 'هذا إشعار توست تجريبي مع رسوم متحركة سلسة!',
      type: NotificationType.systemAnnouncement,
      priority: NotificationPriority.normal,
      createdAt: DateTime.now(),
    );

    ToastNotificationManager.show(context: context, notification: notification, duration: const Duration(seconds: 4));
  }

  Future<void> _runFullSystemTest(String userId) async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => const AlertDialog(
            content: Row(
              children: [CircularProgressIndicator(), SizedBox(width: 16), Text('Running full system test...')],
            ),
          ),
    );

    try {
      await NotificationIntegrationService.runNotificationTests(testUserId: userId);

      if (mounted) {
        Navigator.pop(context); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ Full system test completed successfully!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ System test failed: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Future<void> _simulateRealScenarios(String userId) async {
    await NotificationIntegrationService.simulateRealWorldScenarios(clientId: userId, freelancerId: 'demo_freelancer');

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('🌍 Real-world scenarios simulation started!'),
          backgroundColor: Colors.blue,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  Future<void> _testMultiLanguage(String userId) async {
    // Test both Arabic and English notifications
    await NotificationIntegrationService.quickTest(userId: userId, type: NotificationType.systemAnnouncement);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('🌐 Multi-language test completed!'),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }
}

class _TestButton {
  final String titleEn;
  final String titleAr;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;

  _TestButton({
    required this.titleEn,
    required this.titleAr,
    required this.icon,
    required this.color,
    required this.onTap,
  });
}

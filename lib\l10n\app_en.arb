{"@@locale": "en", "appTitle": "FreelanceHub", "welcomeBack": "Welcome Back", "signInToContinue": "Sign in to continue to FreelanceHub", "email": "Email", "password": "Password", "signIn": "Sign In", "signUp": "Sign Up", "dontHaveAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "createAccount": "Create Account", "joinFreelanceHub": "Join <PERSON>", "createYourAccount": "Create your account to get started", "fullName": "Full Name", "iWantTo": "I want to", "hireFreelancers": "Hire freelancers (Client)", "offerServices": "Offer services (Freelancer)", "confirmPassword": "Confirm Password", "quickDemoLogin": "Quick Demo Login", "loginAsClient": "<PERSON>gin as Client", "loginAsFreelancer": "<PERSON><PERSON> as Freelancer", "loginAsAdmin": "<PERSON><PERSON> as <PERSON><PERSON>", "dashboard": "Dashboard", "requests": "Requests", "orders": "Orders", "messages": "Messages", "browse": "Browse", "offers": "Offers", "jobs": "Jobs", "payments": "Payments", "users": "Users", "welcomeBackUser": "Welcome back, {name}!", "@welcomeBackUser": {"placeholders": {"name": {"type": "String"}}}, "readyToFindFreelancer": "Ready to find the perfect freelancer for your project?", "postNewRequest": "Post New Request", "activeOrders": "Active Orders", "totalRequests": "Total Requests", "recentRequests": "Recent Requests", "noRequestsYet": "No requests yet", "createFirstRequest": "Create your first service request to get started", "welcomeFreelancer": "Welcome, {name}!", "@welcomeFreelancer": {"placeholders": {"name": {"type": "String"}}}, "findNextOpportunity": "Find your next opportunity and grow your freelance career.", "browseJobs": "Browse Jobs", "profile": "Profile", "activeJobs": "Active Jobs", "pendingOffers": "Pending Offers", "latestOpportunities": "Latest Opportunities", "noOpportunitiesAvailable": "No opportunities available", "checkBackLater": "Check back later for new project requests", "recentOffers": "Recent Offers", "adminControlCenter": "Admin Control Center", "managePlatform": "Manage users, verify payments, and oversee platform operations.", "totalUsers": "Total Users", "totalOrders": "Total Orders", "revenue": "Revenue", "paymentVerificationRequired": "Payment Verification Required", "paymentsAwaitingVerification": "{count} payments awaiting verification", "@paymentsAwaitingVerification": {"placeholders": {"count": {"type": "int"}}}, "review": "Review", "quickActions": "Quick Actions", "verifyPayments": "Verify Payments", "manageUsers": "Manage Users", "orderManagement": "Order Management", "systemMessages": "System Messages", "recentActivity": "Recent Activity", "newUserRegistration": "New user registration", "joinedAsFreelancer": "{email} joined as freelancer", "@joinedAsFreelancer": {"placeholders": {"email": {"type": "String"}}}, "paymentSubmitted": "Payment submitted", "orderCompleted": "Order Completed", "orderMarkedCompleted": "Order #{orderId} marked as completed", "@orderMarkedCompleted": {"placeholders": {"orderId": {"type": "String"}}}, "minutesAgo": "{count}m ago", "@minutesAgo": {"placeholders": {"count": {"type": "int"}}}, "hoursAgo": "{count}h ago", "@hoursAgo": {"placeholders": {"count": {"type": "int"}}}, "daysAgo": "{count}d ago", "@daysAgo": {"placeholders": {"count": {"type": "int"}}}, "logout": "Logout", "settings": "Settings", "language": "Language", "english": "English", "arabic": "العربية", "darkMode": "Dark Mode", "lightMode": "Light Mode", "projectTitle": "Project Title", "whatDoYouNeed": "What do you need done?", "category": "Category", "projectDescription": "Project Description", "describeProject": "Describe your project in detail...", "budget": "Budget (SAR)", "enterBudget": "Enter your budget (optional)", "deadline": "Deadline", "selectDeadline": "Select deadline", "attachments": "Attachments", "addFiles": "Add Files", "canAttachFiles": "You can attach relevant files, images, or documents", "post": "POST", "webDevelopment": "Web Development", "mobileDevelopment": "Mobile Development", "graphicDesign": "Graphic Design", "contentWriting": "Content Writing", "digitalMarketing": "Digital Marketing", "dataEntry": "Data Entry", "translation": "Translation", "videoEditing": "Video Editing", "other": "Other", "pending": "PENDING", "inProgress": "In Progress", "completed": "Completed", "cancelled": "Cancelled", "normal": "Normal", "urgent": "<PERSON><PERSON>", "vip": "VIP", "close": "Close", "viewDetails": "View Details", "edit": "Edit", "postedTimeAgo": "Posted {time} ago", "@postedTimeAgo": {"placeholders": {"time": {"type": "String"}}}, "all": "All", "clients": "Clients", "freelancers": "Freelancers", "addNewUser": "Add New User", "fullNameLabel": "Full Name", "role": "Role", "cancel": "Cancel", "addUser": "Add User", "userAddedSuccessfully": "User added successfully!", "noName": "No Name", "joined": "Joined", "status": "Status", "verified": "Verified", "unverified": "Unverified", "rating": "Rating", "client": "Client", "freelancer": "FREELANCER", "admin": "ADMIN", "noPendingPayments": "No pending payments", "allPaymentsProcessed": "All payments have been processed", "clientLabel": "Client", "freelancerLabel": "Freelancer", "amount": "Amount", "paymentProof": "Payment Proof", "submitted": "Submitted", "view": "View", "reject": "Reject", "confirm": "Confirm", "confirmPayment": "Confirm Payment", "confirmPaymentMessage": "Are you sure you want to confirm this payment of ${amount}?\n\nThis will notify the freelancer to start working.", "completedOrders": "Completed Orders", "cancelledOrders": "Cancelled Orders", "successRate": "Success Rate", "freelancerHub": "Freelancer <PERSON><PERSON>", "searchRequests": "Search Requests", "enterKeywords": "Enter keywords...", "search": "Search", "filterByPriority": "Filter by Priority", "clear": "Clear", "clearFilters": "Clear Filters", "priority": "Priority", "due": "Due", "posted": "Posted", "file": "file", "files": "files", "sendOffer": "Send Offer", "requestDetails": "Request Details", "clientInfo": "Client Information", "projectRequirements": "Project Requirements", "attachedFiles": "Attached Files", "download": "Download", "makeOffer": "Make an Offer", "yourOffer": "Your Offer", "offerAmount": "Offer <PERSON> (SAR)", "enterAmount": "Enter amount", "deliveryTime": "Delivery Time", "days": "days", "selectDays": "Select number of days", "offerDescription": "Offer Description", "describeOffer": "Describe your offer and experience in this field...", "submitOffer": "Submit Offer", "offerSubmitted": "Offer Submitted Successfully!", "offerSubmittedMessage": "You will be notified when the client responds to your offer.", "ok": "OK", "myOffers": "My Offers", "noOffersYet": "No offers yet", "startBrowsing": "Start browsing requests and making offers", "browseRequests": "Browse Requests", "offerFor": "Offer for", "deliveryDays": "delivery days", "offerStatus": "Offer Status", "accepted": "Accepted", "rejected": "Rejected", "withdrawn": "Withdrawn", "viewOffer": "View Offer", "withdrawOffer": "With<PERSON><PERSON> Offer", "confirmWithdraw": "Confirm <PERSON>", "withdrawOfferMessage": "Are you sure you want to withdraw this offer? This action cannot be undone.", "withdraw": "Withdraw", "offerWithdrawn": "Offer Withdrawn", "myJobs": "My Jobs", "noActiveJobs": "No active jobs", "completeProjects": "Complete projects to build your reputation", "jobTitle": "Job Title", "dueDate": "Due Date", "progress": "Progress", "startWorking": "Start Working", "deliverWork": "Deliver Work", "viewJob": "View Job", "jobDetails": "Job Details", "deliveryInstructions": "Delivery Instructions", "uploadDelivery": "Upload Delivery", "deliveryFiles": "Delivery Files", "addDeliveryFiles": "Add delivery files", "deliveryNotes": "Delivery Notes", "addNotes": "Add optional notes...", "submitDelivery": "Submit Delivery", "workDelivered": "Work Delivered!", "deliverySubmitted": "Your delivery has been submitted successfully. You will be notified when the client reviews it.", "paymentPending": "Payment Pending", "paymentConfirmed": "Payment Confirmed", "delivered": "Delivered", "chatWithClient": "Chat with Client", "notifications": "Notifications", "noNotifications": "No notifications", "noUnreadNotifications": "No unread notifications", "noReadNotifications": "No read notifications", "notificationsWillAppear": "Your notifications will appear here when they arrive", "unreadNotificationsWillAppear": "New notifications will appear here", "readNotificationsWillAppear": "Notifications you've read will appear here", "markAllAsRead": "Mark all as read", "markAsRead": "<PERSON> as read", "clearAll": "Clear all", "clearAllNotifications": "Clear All Notifications", "deleteNotification": "Delete Notification", "areYouSureDeleteNotification": "Are you sure you want to delete this notification?", "areYouSureClearAllNotifications": "Are you sure you want to clear all notifications? This action cannot be undone.", "delete": "Delete", "refresh": "Refresh", "searchNotifications": "Search notifications...", "unread": "Unread", "read": "Read", "unreadOnly": "Unread only", "announcements": "Announcements", "applications": "Applications", "now": "Now", "weeksAgo": "{count}w ago", "@weeksAgo": {"placeholders": {"count": {"type": "int"}}}, "orderAccepted": "Order Accepted", "orderInProgress": "Order In Progress", "orderDelivered": "Order Delivered", "orderCancelled": "Order Cancelled", "newMessage": "New Message", "paymentReceived": "Payment Received", "paymentConfirmation": "Payment Confirmation", "systemMaintenance": "System Maintenance", "newOrderAvailable": "New Order Available", "chatMessages": "Messages", "noMessagesYet": "No messages yet", "typeMessage": "Type a message...", "sendMessage": "Send", "orderStatus": "Order Status", "payNow": "Pay Now", "viewDelivery": "View Delivery", "approveDelivery": "Approve Delivery", "requestRevision": "Request Revision", "cancelOrder": "Cancel Order", "markComplete": "Mark Complete", "revisionRequested": "Revision Requested", "revisionNotes": "Revision Notes", "enterRevisionNotes": "Please describe what needs to be revised...", "submitRevision": "Submit Revision Request", "revisionRequestSent": "Revision request sent successfully", "editing": "Editing", "orderCreated": "Created", "justNow": "Just now", "timelineOrderCreated": "Order #{orderId} created for \"{serviceTitle}\" - \\${amount}", "@timelineOrderCreated": {"placeholders": {"orderId": {"type": "String"}, "serviceTitle": {"type": "String"}, "amount": {"type": "String"}}}, "timelineOrderAccepted": "Order #{orderId} accepted by {freelancerName}", "@timelineOrderAccepted": {"placeholders": {"orderId": {"type": "String"}, "freelancerName": {"type": "String"}}}, "timelinePaymentConfirmed": "Payment confirmed for Order #{orderId} - \\${amount}", "@timelinePaymentConfirmed": {"placeholders": {"orderId": {"type": "String"}, "amount": {"type": "String"}}}, "timelineWorkDelivered": "Work delivered for Order #{orderId}", "@timelineWorkDelivered": {"placeholders": {"orderId": {"type": "String"}}}, "timelineOrderCompleted": "Order #{orderId} completed successfully", "@timelineOrderCompleted": {"placeholders": {"orderId": {"type": "String"}}}, "timelineOrderCancelled": "Order #{orderId} cancelled - {reason}", "@timelineOrderCancelled": {"placeholders": {"orderId": {"type": "String"}, "reason": {"type": "String"}}}, "timelineRevisionRequested": "Revision requested for Order #{orderId}", "@timelineRevisionRequested": {"placeholders": {"orderId": {"type": "String"}}}, "timelineDeadlineExtended": "Deadline extended for Order #{orderId}", "@timelineDeadlineExtended": {"placeholders": {"orderId": {"type": "String"}}}, "timelineMilestoneReached": "Milestone reached: {milestoneName}", "@timelineMilestoneReached": {"placeholders": {"milestoneName": {"type": "String"}}}, "messageSent": "<PERSON><PERSON>", "messageDelivered": "Delivered", "messageRead": "Read", "attachFile": "Attach File", "takePhoto": "Take Photo", "recordVoice": "Record Voice", "chatWith": "Chat with {name}", "@chatWith": {"placeholders": {"name": {"type": "String"}}}}
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/language_provider.dart';
import '../../models/order_model.dart';
import '../../services/order_service.dart';
import '../../services/chat_service.dart';

class TaskSubmissionButton extends StatefulWidget {
  final OrderModel order;
  final VoidCallback? onTaskSubmitted;

  const TaskSubmissionButton({super.key, required this.order, this.onTaskSubmitted});

  @override
  State<TaskSubmissionButton> createState() => _TaskSubmissionButtonState();
}

class _TaskSubmissionButtonState extends State<TaskSubmissionButton> with SingleTickerProviderStateMixin {
  bool _isSubmitting = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(duration: const Duration(milliseconds: 200), vsync: this);
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _submitTask() async {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    final isArabic = languageProvider.isArabic;

    // Animate button press
    _animationController.forward().then((_) {
      _animationController.reverse();
    });

    setState(() {
      _isSubmitting = true;
    });

    try {
      // Update order status to submitted
      await OrderService.submitTask(widget.order.id);

      // Send automated message in chat
      final chat = await ChatService.getChatByRequestId(widget.order.requestId);
      if (chat != null) {
        await ChatService.sendSystemMessage(chat.id, 'Freelancer has submitted the task for review.');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isArabic ? 'تم تسليم المهمة بنجاح' : 'Task submitted successfully'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          ),
        );
        widget.onTaskSubmitted?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isArabic ? 'خطأ في تسليم المهمة: $e' : 'Error submitting task: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isArabic = languageProvider.isArabic;

    // Show button if payment is confirmed and order is in progress OR submitted
    if (widget.order.paymentStatus != PaymentStatus.confirmed ||
        (widget.order.status != OrderStatus.paymentConfirmed &&
            widget.order.status != OrderStatus.inProgress &&
            widget.order.status != OrderStatus.submitted)) {
      return const SizedBox.shrink();
    }

    final isSubmitted = widget.order.status == OrderStatus.submitted;

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Positioned(
        top: 8,
        right: isArabic ? null : 8,
        left: isArabic ? 8 : null,
        child: AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: isSubmitted || _isSubmitting ? null : _submitTask,
                  borderRadius: BorderRadius.circular(20),
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: isSubmitted ? Colors.green[600] : (_isSubmitting ? Colors.blue[300] : Colors.blue[600]),
                      borderRadius: BorderRadius.circular(isSubmitted ? 25 : 20),
                      border: isSubmitted ? Border.all(color: Colors.green[800]!, width: 2) : null,
                      boxShadow: [
                        BoxShadow(
                          color: isSubmitted ? Colors.green.withValues(alpha: 0.4) : Colors.blue.withValues(alpha: 0.3),
                          blurRadius: isSubmitted ? 6 : 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        AnimatedSwitcher(
                          duration: const Duration(milliseconds: 300),
                          child:
                              _isSubmitting
                                  ? const SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                                  )
                                  : Icon(
                                    isSubmitted ? Icons.check_circle : Icons.upload,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                        ),
                        const SizedBox(width: 6),
                        AnimatedDefaultTextStyle(
                          duration: const Duration(milliseconds: 300),
                          style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.w600),
                          child: Text(
                            _isSubmitting
                                ? (isArabic ? 'جاري التسليم...' : 'Submitting...')
                                : (isSubmitted
                                    ? (isArabic ? 'تم التسليم' : 'Task Submitted')
                                    : (isArabic ? 'تسليم المهمة' : 'Submit Task')),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

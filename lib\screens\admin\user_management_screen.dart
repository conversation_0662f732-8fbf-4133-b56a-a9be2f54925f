import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/language_provider.dart';
import '../../utils/app_localizations.dart';
import '../../models/user_model.dart';

class UserManagementScreen extends StatefulWidget {
  const UserManagementScreen({super.key});

  @override
  State<UserManagementScreen> createState() => _UserManagementScreenState();
}

class _UserManagementScreenState extends State<UserManagementScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  List<UserModel> _users = [];
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadUsers();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadUsers() {
    // Demo data - in real app, this would come from API
    setState(() {
      _users = [
        UserModel(
          id: '1',
          email: '<EMAIL>',
          fullName: 'John Client',
          role: UserRole.client,
          createdAt: DateTime.now().subtract(const Duration(days: 30)),
        ),
        UserModel(
          id: '2',
          email: '<EMAIL>',
          fullName: 'Sarah Developer',
          role: UserRole.freelancer,
          createdAt: DateTime.now().subtract(const Duration(days: 45)),
        ),
        UserModel(
          id: '3',
          email: '<EMAIL>',
          fullName: 'Mike Designer',
          role: UserRole.freelancer,
          createdAt: DateTime.now().subtract(const Duration(days: 20)),
        ),
        UserModel(
          id: '4',
          email: '<EMAIL>',
          fullName: 'Alice Business',
          role: UserRole.client,
          createdAt: DateTime.now().subtract(const Duration(days: 60)),
        ),
        UserModel(
          id: '5',
          email: '<EMAIL>',
          fullName: 'Emma Writer',
          role: UserRole.freelancer,
          createdAt: DateTime.now().subtract(const Duration(days: 15)),
        ),
        UserModel(
          id: '6',
          email: '<EMAIL>',
          fullName: 'Tech Startup',
          role: UserRole.client,
          createdAt: DateTime.now().subtract(const Duration(days: 5)),
        ),
      ];
      _isLoading = false;
    });
  }

  List<UserModel> get _filteredUsers {
    var filtered = _users;

    if (_searchQuery.isNotEmpty) {
      filtered =
          filtered.where((user) {
            return (user.fullName?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false) ||
                user.email.toLowerCase().contains(_searchQuery.toLowerCase());
          }).toList();
    }

    return filtered;
  }

  List<UserModel> _getUsersByRole(UserRole role) {
    return _filteredUsers.where((user) => user.role == role).toList();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final languageProvider = Provider.of<LanguageProvider>(context);

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        appBar: AppBar(
          leading: IconButton(
            icon: Icon(languageProvider.isArabic ? Icons.arrow_forward : Icons.arrow_back),
            onPressed: () => Navigator.pop(context),
          ),
          title: Text(l10n.manageUsers),
          actions: [
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: () {
                _showSearchDialog();
              },
            ),
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () {
                _showAddUserDialog();
              },
            ),
          ],
          bottom: TabBar(
            controller: _tabController,
            tabs: [
              Tab(
                icon: const Icon(Icons.people),
                text: languageProvider.isArabic ? 'الكل (${_filteredUsers.length})' : 'All (${_filteredUsers.length})',
              ),
              Tab(
                icon: const Icon(Icons.business),
                text:
                    languageProvider.isArabic
                        ? 'العملاء (${_getUsersByRole(UserRole.client).length})'
                        : 'Clients (${_getUsersByRole(UserRole.client).length})',
              ),
              Tab(
                icon: const Icon(Icons.work),
                text:
                    languageProvider.isArabic
                        ? 'المستقلون (${_getUsersByRole(UserRole.freelancer).length})'
                        : 'Freelancers (${_getUsersByRole(UserRole.freelancer).length})',
              ),
            ],
          ),
        ),
        body:
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildUsersList(_filteredUsers, l10n, languageProvider),
                    _buildUsersList(_getUsersByRole(UserRole.client), l10n, languageProvider),
                    _buildUsersList(_getUsersByRole(UserRole.freelancer), l10n, languageProvider),
                  ],
                ),
      ),
    );
  }

  Widget _buildUsersList(List<UserModel> users, AppLocalizations l10n, LanguageProvider languageProvider) {
    if (users.isEmpty) {
      return _buildEmptyState(l10n);
    }

    return RefreshIndicator(
      onRefresh: () async {
        _loadUsers();
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: users.length,
        itemBuilder: (context, index) {
          final user = users[index];
          return _buildUserCard(user, l10n, languageProvider);
        },
      ),
    );
  }

  Widget _buildEmptyState(AppLocalizations l10n) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.people_outline, size: 64, color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5)),
          const SizedBox(height: 16),
          Text('No users found', style: Theme.of(context).textTheme.titleLarge),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty
                ? 'Try adjusting your search criteria'
                : 'Users will appear here when they register',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7)),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Search Users'),
            content: TextField(
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
              decoration: const InputDecoration(
                hintText: 'Enter name or email...',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.search),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  setState(() {
                    _searchQuery = '';
                  });
                  Navigator.pop(context);
                },
                child: const Text('Clear'),
              ),
              ElevatedButton(onPressed: () => Navigator.pop(context), child: const Text('Close')),
            ],
          ),
    );
  }

  void _showAddUserDialog() {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    final emailController = TextEditingController();
    final nameController = TextEditingController();
    UserRole selectedRole = UserRole.client;

    showDialog(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setDialogState) => AlertDialog(
                  title: Text(languageProvider.isArabic ? 'إضافة مستخدم جديد' : 'Add New User'),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextField(
                        controller: emailController,
                        decoration: InputDecoration(
                          labelText: languageProvider.isArabic ? 'البريد الإلكتروني' : 'Email',
                          border: const OutlineInputBorder(),
                        ),
                      ),
                      const SizedBox(height: 16),
                      TextField(
                        controller: nameController,
                        decoration: InputDecoration(
                          labelText: languageProvider.isArabic ? 'الاسم الكامل' : 'Full Name',
                          border: const OutlineInputBorder(),
                        ),
                      ),
                      const SizedBox(height: 16),
                      DropdownButtonFormField<UserRole>(
                        value: selectedRole,
                        decoration: InputDecoration(
                          labelText: languageProvider.isArabic ? 'الدور' : 'Role',
                          border: const OutlineInputBorder(),
                        ),
                        items:
                            UserRole.values.map((role) {
                              String roleText;
                              switch (role) {
                                case UserRole.client:
                                  roleText = languageProvider.isArabic ? 'عميل' : 'CLIENT';
                                  break;
                                case UserRole.freelancer:
                                  roleText = languageProvider.isArabic ? 'مستقل' : 'FREELANCER';
                                  break;
                                case UserRole.admin:
                                  roleText = languageProvider.isArabic ? 'مدير' : 'ADMIN';
                                  break;
                              }
                              return DropdownMenuItem(value: role, child: Text(roleText));
                            }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setDialogState(() {
                              selectedRole = value;
                            });
                          }
                        },
                      ),
                    ],
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text(languageProvider.isArabic ? 'إلغاء' : 'Cancel'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        if (emailController.text.isNotEmpty && nameController.text.isNotEmpty) {
                          setState(() {
                            _users.add(
                              UserModel(
                                id: DateTime.now().millisecondsSinceEpoch.toString(),
                                email: emailController.text,
                                fullName: nameController.text,
                                role: selectedRole,
                                createdAt: DateTime.now(),
                              ),
                            );
                          });
                          Navigator.pop(context);
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                languageProvider.isArabic ? 'تم إضافة المستخدم بنجاح!' : 'User added successfully!',
                              ),
                            ),
                          );
                        }
                      },
                      child: Text(languageProvider.isArabic ? 'إضافة مستخدم' : 'Add User'),
                    ),
                  ],
                ),
          ),
    );
  }

  Widget _buildUserCard(UserModel user, AppLocalizations l10n, LanguageProvider languageProvider) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with avatar and basic info
            Row(
              children: [
                CircleAvatar(
                  radius: 24,
                  backgroundColor: _getRoleColor(user.role),
                  child: Text(
                    (user.fullName?.isNotEmpty == true ? user.fullName![0] : user.email[0]).toUpperCase(),
                    style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 18),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user.fullName ?? (languageProvider.isArabic ? 'بلا اسم' : 'No Name'),
                        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      Text(
                        user.email,
                        style: TextStyle(color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7)),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getRoleColor(user.role).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getRoleText(user.role, languageProvider.isArabic),
                    style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold, color: _getRoleColor(user.role)),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // User details
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    languageProvider.isArabic ? 'انضم' : 'Joined',
                    languageProvider.formatDate(user.createdAt),
                    Icons.calendar_today,
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    languageProvider.isArabic ? 'الحالة' : 'Status',
                    user.isVerified
                        ? (languageProvider.isArabic ? 'موثق' : 'Verified')
                        : (languageProvider.isArabic ? 'غير موثق' : 'Unverified'),
                    user.isVerified ? Icons.verified : Icons.pending,
                  ),
                ),
              ],
            ),

            if (user.rating != null || user.completedJobs != null) ...[
              const SizedBox(height: 12),
              Row(
                children: [
                  if (user.rating != null)
                    Expanded(
                      child: _buildInfoItem(
                        languageProvider.isArabic ? 'التقييم' : 'Rating',
                        '${user.rating!.toStringAsFixed(1)} ⭐',
                        Icons.star,
                      ),
                    ),
                  if (user.completedJobs != null)
                    Expanded(
                      child: _buildInfoItem(
                        languageProvider.isArabic ? 'الوظائف' : 'Jobs',
                        languageProvider.isArabic ? '${user.completedJobs} مكتملة' : '${user.completedJobs} completed',
                        Icons.work,
                      ),
                    ),
                ],
              ),
            ],

            const SizedBox(height: 16),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _viewUserDetails(user),
                    icon: const Icon(Icons.visibility),
                    label: Text(languageProvider.isArabic ? 'عرض التفاصيل' : 'View Details'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _editUser(user),
                    icon: const Icon(Icons.edit),
                    label: Text(languageProvider.isArabic ? 'تعديل' : 'Edit'),
                  ),
                ),
                const SizedBox(width: 12),
                IconButton(
                  onPressed: () => _showUserActions(user),
                  icon: const Icon(Icons.more_vert),
                  style: IconButton.styleFrom(backgroundColor: Theme.of(context).colorScheme.surface),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(fontSize: 12, color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6)),
              ),
              Text(value, style: const TextStyle(fontWeight: FontWeight.w600)),
            ],
          ),
        ),
      ],
    );
  }

  Color _getRoleColor(UserRole role) {
    switch (role) {
      case UserRole.client:
        return Colors.blue;
      case UserRole.freelancer:
        return Colors.green;
      case UserRole.admin:
        return Colors.purple;
    }
  }

  String _getRoleText(UserRole role, bool isArabic) {
    switch (role) {
      case UserRole.client:
        return isArabic ? 'عميل' : 'CLIENT';
      case UserRole.freelancer:
        return isArabic ? 'مستقل' : 'FREELANCER';
      case UserRole.admin:
        return isArabic ? 'مدير' : 'ADMIN';
    }
  }

  void _viewUserDetails(UserModel user) {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            child: Container(
              padding: const EdgeInsets.all(24),
              constraints: const BoxConstraints(maxWidth: 500),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    languageProvider.isArabic ? 'تفاصيل المستخدم' : 'User Details',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  _buildDetailRow(languageProvider.isArabic ? 'المعرف' : 'ID', user.id),
                  _buildDetailRow(languageProvider.isArabic ? 'البريد الإلكتروني' : 'Email', user.email),
                  _buildDetailRow(
                    languageProvider.isArabic ? 'الاسم' : 'Name',
                    user.fullName ?? (languageProvider.isArabic ? 'غير محدد' : 'Not provided'),
                  ),
                  _buildDetailRow(
                    languageProvider.isArabic ? 'الدور' : 'Role',
                    _getRoleText(user.role, languageProvider.isArabic),
                  ),
                  _buildDetailRow(
                    languageProvider.isArabic ? 'موثق' : 'Verified',
                    user.isVerified
                        ? (languageProvider.isArabic ? 'نعم' : 'Yes')
                        : (languageProvider.isArabic ? 'لا' : 'No'),
                  ),
                  _buildDetailRow(
                    languageProvider.isArabic ? 'تاريخ الانضمام' : 'Joined',
                    user.createdAt.toString().split(' ')[0],
                  ),
                  if (user.bio != null) _buildDetailRow(languageProvider.isArabic ? 'النبذة' : 'Bio', user.bio!),
                  if (user.skills != null && user.skills!.isNotEmpty)
                    _buildDetailRow(languageProvider.isArabic ? 'المهارات' : 'Skills', user.skills!.join(', ')),
                  if (user.rating != null)
                    _buildDetailRow(
                      languageProvider.isArabic ? 'التقييم' : 'Rating',
                      '${user.rating!.toStringAsFixed(1)} / 5.0',
                    ),
                  if (user.completedJobs != null)
                    _buildDetailRow(
                      languageProvider.isArabic ? 'الوظائف المكتملة' : 'Completed Jobs',
                      user.completedJobs.toString(),
                    ),
                  const SizedBox(height: 24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text(languageProvider.isArabic ? 'إغلاق' : 'Close'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(width: 120, child: Text('$label:', style: const TextStyle(fontWeight: FontWeight.w600))),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _editUser(UserModel user) {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          languageProvider.isArabic
              ? 'سيتم تنفيذ وظيفة تعديل المستخدم هنا'
              : 'Edit user functionality would be implemented here',
        ),
      ),
    );
  }

  void _showUserActions(UserModel user) {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    showModalBottomSheet(
      context: context,
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: Icon(user.isVerified ? Icons.cancel : Icons.verified),
                  title: Text(
                    user.isVerified
                        ? (languageProvider.isArabic ? 'إلغاء توثيق المستخدم' : 'Unverify User')
                        : (languageProvider.isArabic ? 'توثيق المستخدم' : 'Verify User'),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _toggleUserVerification(user);
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.message),
                  title: Text(languageProvider.isArabic ? 'إرسال رسالة' : 'Send Message'),
                  onTap: () {
                    Navigator.pop(context);
                    _sendMessage(user);
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.block, color: Colors.red),
                  title: Text(languageProvider.isArabic ? 'تعليق المستخدم' : 'Suspend User'),
                  onTap: () {
                    Navigator.pop(context);
                    _suspendUser(user);
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.delete, color: Colors.red),
                  title: Text(languageProvider.isArabic ? 'حذف المستخدم' : 'Delete User'),
                  onTap: () {
                    Navigator.pop(context);
                    _deleteUser(user);
                  },
                ),
              ],
            ),
          ),
    );
  }

  void _toggleUserVerification(UserModel user) {
    setState(() {
      final index = _users.indexWhere((u) => u.id == user.id);
      if (index != -1) {
        _users[index] = user.copyWith(isVerified: !user.isVerified);
      }
    });
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(user.isVerified ? 'User unverified' : 'User verified')));
  }

  void _sendMessage(UserModel user) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Message functionality would be implemented here')));
  }

  void _suspendUser(UserModel user) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Suspend User'),
            content: Text('Are you sure you want to suspend ${user.fullName ?? user.email}?'),
            actions: [
              TextButton(onPressed: () => Navigator.pop(context), child: const Text('Cancel')),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('User suspended')));
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text('Suspend'),
              ),
            ],
          ),
    );
  }

  void _deleteUser(UserModel user) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete User'),
            content: Text(
              'Are you sure you want to permanently delete ${user.fullName ?? user.email}? This action cannot be undone.',
            ),
            actions: [
              TextButton(onPressed: () => Navigator.pop(context), child: const Text('Cancel')),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  setState(() {
                    _users.removeWhere((u) => u.id == user.id);
                  });
                  ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('User deleted')));
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/language_provider.dart';
import '../../models/order_model.dart';
import '../../models/user_model.dart';
import '../../models/review_model.dart';

class SubmitReviewScreen extends StatefulWidget {
  final OrderModel order;
  final UserModel freelancer;

  const SubmitReviewScreen({
    super.key,
    required this.order,
    required this.freelancer,
  });

  @override
  State<SubmitReviewScreen> createState() => _SubmitReviewScreenState();
}

class _SubmitReviewScreenState extends State<SubmitReviewScreen> {
  final _formKey = GlobalKey<FormState>();
  final _reviewController = TextEditingController();
  final _reviewArController = TextEditingController();

  double _rating = 5.0;
  final List<String> _selectedTags = [];
  bool _isSubmitting = false;
  bool _showArabicField = false;

  final List<String> _availableTags = [
    'Quality',
    'Communication',
    'Timeliness',
    'Professionalism',
    'Creativity',
    'Problem Solving',
    'Responsiveness',
    'Value for Money',
    'Exceeded Expectations',
    'Would Recommend',
  ];

  final Map<String, String> _tagTranslations = {
    'Quality': 'الجودة',
    'Communication': 'التواصل',
    'Timeliness': 'الالتزام بالوقت',
    'Professionalism': 'الاحترافية',
    'Creativity': 'الإبداع',
    'Problem Solving': 'حل المشاكل',
    'Responsiveness': 'سرعة الاستجابة',
    'Value for Money': 'القيمة مقابل المال',
    'Exceeded Expectations': 'تجاوز التوقعات',
    'Would Recommend': 'أنصح به',
  };

  @override
  void dispose() {
    _reviewController.dispose();
    _reviewArController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isArabic = languageProvider.isArabic;

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        appBar: AppBar(
          title: Text(isArabic ? 'تقييم المستقل' : 'Review Freelancer'),
          elevation: 0,
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Freelancer info card
                _buildFreelancerCard(isArabic),

                const SizedBox(height: 24),

                // Rating section
                _buildRatingSection(isArabic),

                const SizedBox(height: 24),

                // Review text section
                _buildReviewTextSection(isArabic),

                const SizedBox(height: 24),

                // Tags section
                _buildTagsSection(isArabic),

                const SizedBox(height: 32),

                // Submit button
                _buildSubmitButton(isArabic),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFreelancerCard(bool isArabic) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: Theme.of(context).colorScheme.primary,
              child: widget.freelancer.avatarUrl != null
                  ? ClipOval(
                      child: Image.network(
                        widget.freelancer.avatarUrl!,
                        width: 60,
                        height: 60,
                        fit: BoxFit.cover,
                      ),
                    )
                  : Text(
                      widget.freelancer.fullName?.substring(0, 1).toUpperCase() ?? 'F',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.freelancer.fullName ?? 'Freelancer',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(Icons.star, color: Colors.amber[700], size: 16),
                      const SizedBox(width: 4),
                      Text(
                        widget.freelancer.rating?.toStringAsFixed(1) ?? '0.0',
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '${widget.freelancer.completedJobs ?? 0} ${isArabic ? 'مشروع مكتمل' : 'completed jobs'}',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      isArabic ? 'مشروع: ${widget.order.id}' : 'Order: ${widget.order.id}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.blue,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRatingSection(bool isArabic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'تقييمك العام' : 'Overall Rating',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            children: [
              Text(
                _rating.toStringAsFixed(1),
                style: TextStyle(
                  fontSize: 48,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(5, (index) {
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _rating = (index + 1).toDouble();
                      });
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4),
                      child: Icon(
                        index < _rating.floor() ? Icons.star : Icons.star_border,
                        color: Colors.amber[700],
                        size: 32,
                      ),
                    ),
                  );
                }),
              ),
              const SizedBox(height: 8),
              Slider(
                value: _rating,
                min: 1.0,
                max: 5.0,
                divisions: 8,
                label: _rating.toStringAsFixed(1),
                onChanged: (value) {
                  setState(() {
                    _rating = value;
                  });
                },
              ),
              Text(
                _getRatingText(_rating, isArabic),
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildReviewTextSection(bool isArabic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              isArabic ? 'تفاصيل التقييم' : 'Review Details',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            TextButton.icon(
              onPressed: () {
                setState(() {
                  _showArabicField = !_showArabicField;
                });
              },
              icon: Icon(_showArabicField ? Icons.remove : Icons.add),
              label: Text(isArabic ? 'إضافة نص عربي' : 'Add Arabic Text'),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // English/Primary review
        TextFormField(
          controller: _reviewController,
          maxLines: 4,
          decoration: InputDecoration(
            labelText: isArabic ? 'التقييم (بالإنجليزية)' : 'Your Review',
            hintText: isArabic
                ? 'شارك تجربتك مع هذا المستقل...'
                : 'Share your experience with this freelancer...',
            border: const OutlineInputBorder(),
            alignLabelWithHint: true,
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return isArabic ? 'يرجى كتابة تقييم' : 'Please write a review';
            }
            if (value.trim().length < 10) {
              return isArabic
                  ? 'التقييم قصير جداً (10 أحرف على الأقل)'
                  : 'Review is too short (minimum 10 characters)';
            }
            return null;
          },
        ),

        // Arabic review field (optional)
        if (_showArabicField) ...[
          const SizedBox(height: 16),
          TextFormField(
            controller: _reviewArController,
            maxLines: 4,
            textDirection: TextDirection.rtl,
            decoration: const InputDecoration(
              labelText: 'التقييم (بالعربية)',
              hintText: 'اكتب تقييمك باللغة العربية (اختياري)...',
              border: OutlineInputBorder(),
              alignLabelWithHint: true,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildTagsSection(bool isArabic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'ما الذي أعجبك؟' : 'What did you like?',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          isArabic
              ? 'اختر الجوانب التي أعجبتك في العمل (اختياري)'
              : 'Select aspects you liked about the work (optional)',
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _availableTags.map((tag) {
            final isSelected = _selectedTags.contains(tag);
            return FilterChip(
              selected: isSelected,
              label: Text(
                isArabic ? (_tagTranslations[tag] ?? tag) : tag,
                style: TextStyle(
                  color: isSelected
                      ? Colors.white
                      : Theme.of(context).colorScheme.onSurface,
                ),
              ),
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    _selectedTags.add(tag);
                  } else {
                    _selectedTags.remove(tag);
                  }
                });
              },
              backgroundColor: Colors.grey[200],
              selectedColor: Theme.of(context).colorScheme.primary,
              checkmarkColor: Colors.white,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildSubmitButton(bool isArabic) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _isSubmitting ? null : _submitReview,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: _isSubmitting
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation(Colors.white),
                    ),
                  )
                : Text(
                    isArabic ? 'إرسال التقييم' : 'Submit Review',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
        ),
        const SizedBox(height: 12),
        Text(
          isArabic
              ? 'سيكون تقييمك مرئياً للجميع في ملف المستقل الشخصي'
              : 'Your review will be publicly visible on the freelancer\'s profile',
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 12,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  String _getRatingText(double rating, bool isArabic) {
    if (rating >= 4.5) {
      return isArabic ? 'ممتاز' : 'Excellent';
    } else if (rating >= 3.5) {
      return isArabic ? 'جيد جداً' : 'Very Good';
    } else if (rating >= 2.5) {
      return isArabic ? 'جيد' : 'Good';
    } else if (rating >= 1.5) {
      return isArabic ? 'مقبول' : 'Fair';
    } else {
      return isArabic ? 'ضعيف' : 'Poor';
    }
  }

  Future<void> _submitReview() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      // Create review model
      final review = ReviewModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        orderId: widget.order.id,
        clientId: widget.order.clientId,
        freelancerId: widget.order.freelancerId,
        clientName: 'Current User', // Replace with actual client name
        rating: _rating,
        reviewText: _reviewController.text.trim(),
        reviewTextAr: _reviewArController.text.trim().isNotEmpty
            ? _reviewArController.text.trim()
            : null,
        tags: _selectedTags,
        serviceType: 'Service Type', // Replace with actual service type
        createdAt: DateTime.now(),
      );

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              Provider.of<LanguageProvider>(context, listen: false).isArabic
                  ? 'تم إرسال التقييم بنجاح!'
                  : 'Review submitted successfully!',
            ),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate back
        Navigator.pop(context, review);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              Provider.of<LanguageProvider>(context, listen: false).isArabic
                  ? 'حدث خطأ أثناء إرسال التقييم'
                  : 'Error submitting review',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }
}

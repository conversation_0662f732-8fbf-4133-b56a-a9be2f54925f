import 'dart:async';
import 'dart:math';
import '../models/notification_model.dart';

class NotificationService {
  static final List<NotificationModel> _demoNotifications = [];
  static final StreamController<List<NotificationModel>> _notificationsController =
      StreamController<List<NotificationModel>>.broadcast();
  static Timer? _simulationTimer;

  /// Stream of notifications for real-time updates
  static Stream<List<NotificationModel>> get notificationsStream => _notificationsController.stream;

  /// Initialize the notification service with demo data
  static Future<void> initialize() async {
    if (_demoNotifications.isEmpty) {
      await _loadDemoNotifications();
      _startRealTimeSimulation();
    }
  }

  /// Get all notifications for a specific user
  static Future<List<NotificationModel>> getNotifications({
    String? userId,
    bool? isRead,
    NotificationType? type,
    int? limit,
  }) async {
    try {
      var notifications = List<NotificationModel>.from(_demoNotifications);

      // Filter by user ID
      if (userId != null) {
        notifications = notifications.where((n) => n.userId == userId).toList();
      }

      // Filter by read status
      if (isRead != null) {
        notifications = notifications.where((n) => n.isRead == isRead).toList();
      }

      // Filter by type
      if (type != null) {
        notifications = notifications.where((n) => n.type == type).toList();
      }

      // Sort by creation date (newest first)
      notifications.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      // Apply limit
      if (limit != null && limit > 0) {
        notifications = notifications.take(limit).toList();
      }

      return notifications;
    } catch (e) {
      throw Exception('Failed to fetch notifications: $e');
    }
  }

  /// Get unread notification count for a user
  static Future<int> getUnreadCount(String userId) async {
    try {
      final unreadNotifications = _demoNotifications.where((n) => n.userId == userId && !n.isRead).toList();
      return unreadNotifications.length;
    } catch (e) {
      throw Exception('Failed to get unread count: $e');
    }
  }

  /// Create a new notification
  static Future<NotificationModel> createNotification(NotificationModel notification) async {
    try {
      _demoNotifications.add(notification);
      // Delay notification to avoid setState during build
      Future.delayed(Duration.zero, () => _notifyListeners());
      return notification;
    } catch (e) {
      throw Exception('Failed to create notification: $e');
    }
  }

  /// Mark a notification as read
  static Future<NotificationModel> markAsRead(String notificationId) async {
    try {
      final index = _demoNotifications.indexWhere((n) => n.id == notificationId);
      if (index == -1) {
        throw Exception('Notification not found');
      }

      final updatedNotification = _demoNotifications[index].markAsRead();
      _demoNotifications[index] = updatedNotification;
      Future.delayed(Duration.zero, () => _notifyListeners());
      return updatedNotification;
    } catch (e) {
      throw Exception('Failed to mark notification as read: $e');
    }
  }

  /// Mark all notifications as read for a user
  static Future<void> markAllAsRead(String userId) async {
    try {
      for (int i = 0; i < _demoNotifications.length; i++) {
        if (_demoNotifications[i].userId == userId && !_demoNotifications[i].isRead) {
          _demoNotifications[i] = _demoNotifications[i].markAsRead();
        }
      }
      Future.delayed(Duration.zero, () => _notifyListeners());
    } catch (e) {
      throw Exception('Failed to mark all notifications as read: $e');
    }
  }

  /// Delete a notification
  static Future<void> deleteNotification(String notificationId) async {
    try {
      _demoNotifications.removeWhere((n) => n.id == notificationId);
      Future.delayed(Duration.zero, () => _notifyListeners());
    } catch (e) {
      throw Exception('Failed to delete notification: $e');
    }
  }

  /// Clear all notifications for a user
  static Future<void> clearAllNotifications(String userId) async {
    try {
      _demoNotifications.removeWhere((n) => n.userId == userId);
      Future.delayed(Duration.zero, () => _notifyListeners());
    } catch (e) {
      throw Exception('Failed to clear notifications: $e');
    }
  }

  /// Create notification for order status updates
  static Future<void> notifyOrderStatus({
    required String userId,
    required String orderId,
    required NotificationType type,
    required String titleEn,
    required String titleAr,
    required String descriptionEn,
    required String descriptionAr,
  }) async {
    final notification = NotificationModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: userId,
      titleEn: titleEn,
      titleAr: titleAr,
      descriptionEn: descriptionEn,
      descriptionAr: descriptionAr,
      type: type,
      relatedId: orderId,
      priority: NotificationPriority.normal,
      createdAt: DateTime.now(),
      metadata: {'order_id': orderId},
      actionUrl: '/orders/$orderId',
    );

    await createNotification(notification);
  }

  /// Create notification for new messages
  static Future<void> notifyNewMessage({
    required String userId,
    required String chatId,
    required String senderName,
    required String messagePreview,
  }) async {
    final notification = NotificationModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: userId,
      titleEn: 'New Message from $senderName',
      titleAr: 'رسالة جديدة من $senderName',
      descriptionEn: messagePreview,
      descriptionAr: messagePreview,
      type: NotificationType.newMessage,
      relatedId: chatId,
      priority: NotificationPriority.high,
      createdAt: DateTime.now(),
      metadata: {'chat_id': chatId, 'sender_name': senderName},
      actionUrl: '/chat/$chatId',
    );

    await createNotification(notification);
  }

  /// Create notification for payment updates
  static Future<void> notifyPayment({
    required String userId,
    required String paymentId,
    required NotificationType type,
    required String titleEn,
    required String titleAr,
    required String descriptionEn,
    required String descriptionAr,
    required double amount,
  }) async {
    final notification = NotificationModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: userId,
      titleEn: titleEn,
      titleAr: titleAr,
      descriptionEn: descriptionEn,
      descriptionAr: descriptionAr,
      type: type,
      relatedId: paymentId,
      priority: NotificationPriority.high,
      createdAt: DateTime.now(),
      metadata: {'payment_id': paymentId, 'amount': amount},
      actionUrl: '/payments/$paymentId',
    );

    await createNotification(notification);
  }

  /// Create system announcement notification
  static Future<void> notifySystemAnnouncement({
    required String titleEn,
    required String titleAr,
    required String descriptionEn,
    required String descriptionAr,
    NotificationPriority priority = NotificationPriority.normal,
    List<String>? targetUserIds,
  }) async {
    // If no specific users, send to all demo users
    final userIds = targetUserIds ?? ['demo_client_1', 'demo_freelancer_1', 'demo_admin_1'];

    for (final userId in userIds) {
      final notification = NotificationModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: userId,
        titleEn: titleEn,
        titleAr: titleAr,
        descriptionEn: descriptionEn,
        descriptionAr: descriptionAr,
        type: NotificationType.systemAnnouncement,
        priority: priority,
        createdAt: DateTime.now(),
      );

      await createNotification(notification);
    }
  }

  /// Notify listeners about notification changes
  static void _notifyListeners() {
    if (!_notificationsController.isClosed) {
      _notificationsController.add(List<NotificationModel>.from(_demoNotifications));
    }
  }

  /// Load demo notifications for testing
  static Future<void> _loadDemoNotifications() async {
    final now = DateTime.now();

    _demoNotifications.clear(); // Clear any existing notifications
    _demoNotifications.addAll([
      // Client notifications
      NotificationModel(
        id: 'notif_1',
        userId: 'demo_client_1',
        titleEn: 'Order Accepted',
        titleAr: 'تم قبول الطلب',
        descriptionEn: 'Your mobile app development order has been accepted by Ahmed Hassan.',
        descriptionAr: 'تم قبول طلب تطوير التطبيق المحمول الخاص بك من قبل أحمد حسن.',
        type: NotificationType.orderAccepted,
        relatedId: 'order_1',
        isRead: false,
        priority: NotificationPriority.high,
        createdAt: now.subtract(const Duration(minutes: 30)),
        metadata: {'freelancer_name': 'Ahmed Hassan'},
        actionUrl: '/orders/order_1',
      ),
      NotificationModel(
        id: 'notif_2',
        userId: 'demo_client_1',
        titleEn: 'New Message',
        titleAr: 'رسالة جديدة',
        descriptionEn: 'Ahmed Hassan sent you a message about your project.',
        descriptionAr: 'أرسل لك أحمد حسن رسالة حول مشروعك.',
        type: NotificationType.newMessage,
        relatedId: 'chat_1',
        isRead: true,
        priority: NotificationPriority.normal,
        createdAt: now.subtract(const Duration(hours: 2)),
        readAt: now.subtract(const Duration(hours: 1)),
        metadata: {'sender_name': 'Ahmed Hassan'},
        actionUrl: '/chat/chat_1',
      ),
      // Freelancer notifications
      NotificationModel(
        id: 'notif_3',
        userId: 'demo_freelancer_1',
        titleEn: 'Payment Received',
        titleAr: 'تم استلام الدفع',
        descriptionEn: 'You have received payment of \$500 for mobile app project.',
        descriptionAr: 'لقد استلمت دفعة بقيمة 500 دولار لمشروع التطبيق المحمول.',
        type: NotificationType.paymentConfirmation,
        relatedId: 'payment_1',
        isRead: false,
        priority: NotificationPriority.high,
        createdAt: now.subtract(const Duration(minutes: 15)),
        metadata: {'amount': 500.0},
        actionUrl: '/payments/payment_1',
      ),
      NotificationModel(
        id: 'notif_4',
        userId: 'demo_freelancer_1',
        titleEn: 'New Order Available',
        titleAr: 'طلب جديد متاح',
        descriptionEn: 'A new website design order is available for bidding.',
        descriptionAr: 'طلب جديد لتصميم موقع ويب متاح للمزايدة.',
        type: NotificationType.orderNew,
        relatedId: 'order_2',
        isRead: true,
        priority: NotificationPriority.normal,
        createdAt: now.subtract(const Duration(hours: 4)),
        readAt: now.subtract(const Duration(hours: 3)),
        actionUrl: '/orders/order_2',
      ),
      // System announcement
      NotificationModel(
        id: 'notif_5',
        userId: 'demo_client_1',
        titleEn: 'System Maintenance',
        titleAr: 'صيانة النظام',
        descriptionEn: 'Scheduled maintenance will occur tonight from 2-4 AM.',
        descriptionAr: 'ستتم الصيانة المجدولة الليلة من الساعة 2-4 صباحاً.',
        type: NotificationType.systemAnnouncement,
        isRead: false,
        priority: NotificationPriority.urgent,
        createdAt: now.subtract(const Duration(hours: 1)),
      ),
    ]);
  }

  /// Start real-time simulation for demo purposes
  static void _startRealTimeSimulation() {
    _simulationTimer?.cancel();
    _simulationTimer = Timer.periodic(const Duration(minutes: 2), (timer) {
      _simulateNewNotification();
    });
  }

  /// Simulate a new notification for demo purposes
  static void _simulateNewNotification() {
    final random = Random();
    final now = DateTime.now();
    final userId = ['demo_client_1', 'demo_freelancer_1'][random.nextInt(2)];

    final notifications = [
      NotificationModel(
        id: 'sim_${now.millisecondsSinceEpoch}',
        userId: userId,
        titleEn: 'New Message',
        titleAr: 'رسالة جديدة',
        descriptionEn: 'You have received a new message.',
        descriptionAr: 'لقد استلمت رسالة جديدة.',
        type: NotificationType.newMessage,
        priority: NotificationPriority.normal,
        createdAt: now,
      ),
      NotificationModel(
        id: 'sim_${now.millisecondsSinceEpoch}',
        userId: userId,
        titleEn: 'Order Update',
        titleAr: 'تحديث الطلب',
        descriptionEn: 'Your order status has been updated.',
        descriptionAr: 'تم تحديث حالة طلبك.',
        type: NotificationType.orderStatus,
        priority: NotificationPriority.normal,
        createdAt: now,
      ),
    ];

    final notification = notifications[random.nextInt(notifications.length)];
    _demoNotifications.add(notification);
    Future.delayed(Duration.zero, () => _notifyListeners());
  }

  /// Stop real-time simulation
  static void stopSimulation() {
    _simulationTimer?.cancel();
    _simulationTimer = null;
  }

  /// Dispose resources
  static void dispose() {
    stopSimulation();
    _notificationsController.close();
  }
}

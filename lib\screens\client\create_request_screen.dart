import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/demo_auth_provider.dart';
import '../../providers/language_provider.dart';
import '../../models/service_request_model.dart';
import '../../services/request_service.dart';
import '../../services/storage_service.dart';
import 'services_screen.dart';
import 'dart:io';

class CreateRequestScreen extends StatefulWidget {
  final ServiceItem? selectedService;

  const CreateRequestScreen({super.key, this.selectedService});

  @override
  State<CreateRequestScreen> createState() => _CreateRequestScreenState();
}

class _CreateRequestScreenState extends State<CreateRequestScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();

  String? _selectedCategory;
  DateTime? _deadline;
  final List<File> _attachments = [];
  bool _isLoading = false;

  final List<String> _categories = [
    'Academic Sources',
    'Scientific Reports',
    'Mind Maps',
    'Translation',
    'Summarization',
    'Scientific Projects',
    'Presentations',
    'SPSS Analysis',
    'Proofreading',
    'Programming',
    'Tutorials',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    // Pre-fill form if service is selected
    if (widget.selectedService != null) {
      _titleController.text = widget.selectedService!.titleEn;
      _selectedCategory = _getCategoryFromService(widget.selectedService!);
    }
  }

  String _getCategoryFromService(ServiceItem service) {
    switch (service.id) {
      case 'academic_sources':
        return 'Academic Sources';
      case 'scientific_reports':
        return 'Scientific Reports';
      case 'mind_maps':
        return 'Mind Maps';
      case 'translation':
        return 'Translation';
      case 'summarization':
        return 'Summarization';
      case 'scientific_projects':
        return 'Scientific Projects';
      case 'presentations':
        return 'Presentations';
      case 'spss_analysis':
        return 'SPSS Analysis';
      case 'proofreading':
        return 'Proofreading';
      case 'programming':
        return 'Programming';
      case 'tutorials':
        return 'Tutorials';
      default:
        return 'Other';
    }
  }

  String _getCategoryTranslation(String category, bool isArabic) {
    if (!isArabic) return category;

    switch (category) {
      case 'Academic Sources':
        return 'المصادر الأكاديمية';
      case 'Scientific Reports':
        return 'التقارير العلمية';
      case 'Mind Maps':
        return 'الخرائط الذهنية';
      case 'Translation':
        return 'الترجمة';
      case 'Summarization':
        return 'التلخيص';
      case 'Scientific Projects':
        return 'المشاريع العلمية';
      case 'Presentations':
        return 'العروض التقديمية';
      case 'SPSS Analysis':
        return 'تحليل SPSS';
      case 'Proofreading':
        return 'التدقيق اللغوي';
      case 'Programming':
        return 'البرمجة';
      case 'Tutorials':
        return 'الدروس التعليمية';
      case 'Other':
        return 'أخرى';
      default:
        return category;
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _pickFiles() async {
    try {
      final files = await StorageService.pickMultipleFiles();
      setState(() {
        _attachments.addAll(files);
      });
    } catch (e) {
      if (mounted) {
        final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
        final isArabic = languageProvider.isArabic;
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(isArabic ? 'خطأ في اختيار الملفات: $e' : 'Error picking files: $e')));
      }
    }
  }

  void _removeAttachment(int index) {
    setState(() {
      _attachments.removeAt(index);
    });
  }

  void _selectDeadline() async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 7)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() {
        _deadline = date;
      });
    }
  }

  void _submitRequest() async {
    if (!_formKey.currentState!.validate()) return;

    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    final isArabic = languageProvider.isArabic;

    if (_deadline == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(isArabic ? 'يرجى اختيار الموعد النهائي' : 'Please select a deadline')));
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<DemoAuthProvider>(context, listen: false);
      final userId = authProvider.user?.id;

      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Upload attachments
      List<String> attachmentUrls = [];
      if (_attachments.isNotEmpty) {
        attachmentUrls = await StorageService.uploadMultipleFiles(_attachments, 'request_attachments');
      }

      // Create request
      final request = ServiceRequestModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        clientId: userId,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        budget: null, // Budget removed
        attachments: attachmentUrls.isNotEmpty ? attachmentUrls : null,
        deadline: _deadline!,
        createdAt: DateTime.now(),
        category: _selectedCategory,
      );

      await RequestService.createRequest(request);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isArabic ? 'تم نشر الطلب بنجاح!' : 'Request posted successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isArabic ? 'خطأ في إنشاء الطلب: $e' : 'Error creating request: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isArabic = languageProvider.isArabic;

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        appBar: AppBar(title: Text(isArabic ? 'إنشاء طلب جديد' : 'Post New Request')),
        body: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title
                TextFormField(
                  controller: _titleController,
                  decoration: InputDecoration(
                    labelText: isArabic ? 'عنوان المشروع *' : 'Project Title *',
                    hintText: isArabic ? 'ما الذي تحتاج إنجازه؟' : 'What do you need done?',
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return isArabic ? 'يرجى إدخال عنوان المشروع' : 'Please enter a project title';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 20),

                // Category
                DropdownButtonFormField<String>(
                  value: _selectedCategory,
                  decoration: InputDecoration(labelText: isArabic ? 'الفئة *' : 'Category *'),
                  items:
                      _categories.map((category) {
                        return DropdownMenuItem(
                          value: category,
                          child: Text(_getCategoryTranslation(category, isArabic)),
                        );
                      }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedCategory = value;
                    });
                  },
                  validator: (value) {
                    if (value == null) {
                      return isArabic ? 'يرجى اختيار فئة' : 'Please select a category';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 20),

                // Description
                TextFormField(
                  controller: _descriptionController,
                  maxLines: 5,
                  decoration: InputDecoration(
                    labelText: isArabic ? 'وصف المشروع *' : 'Project Description *',
                    hintText: isArabic ? 'اوصف مشروعك بالتفصيل...' : 'Describe your project in detail...',
                    alignLabelWithHint: true,
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return isArabic ? 'يرجى إدخال وصف المشروع' : 'Please enter a project description';
                    }
                    if (value.length < 50) {
                      return isArabic
                          ? 'الوصف يجب أن يكون 50 حرف على الأقل'
                          : 'Description must be at least 50 characters';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 20),

                // Deadline
                InkWell(
                  onTap: _selectDeadline,
                  child: InputDecorator(
                    decoration: InputDecoration(
                      labelText: isArabic ? 'الموعد النهائي *' : 'Deadline *',
                      suffixIcon: const Icon(Icons.calendar_today),
                    ),
                    child: Text(
                      _deadline != null
                          ? '${_deadline!.day}/${_deadline!.month}/${_deadline!.year}'
                          : (isArabic ? 'اختر الموعد النهائي' : 'Select deadline'),
                      style: TextStyle(
                        color:
                            _deadline != null
                                ? Theme.of(context).textTheme.bodyLarge?.color
                                : Theme.of(context).hintColor,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // Attachments
                Text(isArabic ? 'المرفقات' : 'Attachments', style: Theme.of(context).textTheme.titleMedium),
                const SizedBox(height: 8),

                if (_attachments.isNotEmpty) ...[
                  ...List.generate(_attachments.length, (index) {
                    final file = _attachments[index];
                    return Card(
                      child: ListTile(
                        leading: const Icon(Icons.attach_file),
                        title: Text(file.path.split('/').last, overflow: TextOverflow.ellipsis),
                        trailing: IconButton(icon: const Icon(Icons.close), onPressed: () => _removeAttachment(index)),
                      ),
                    );
                  }),
                  const SizedBox(height: 12),
                ],

                OutlinedButton.icon(
                  onPressed: _pickFiles,
                  icon: const Icon(Icons.attach_file),
                  label: Text(isArabic ? 'إضافة ملفات' : 'Add Files'),
                ),
                const SizedBox(height: 8),
                Text(
                  isArabic
                      ? 'يمكنك إرفاق الملفات أو الصور أو المستندات ذات الصلة'
                      : 'You can attach relevant files, images, or documents',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),

                const SizedBox(height: 40),

                // POST Button
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _submitRequest,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    ),
                    child:
                        _isLoading
                            ? const SizedBox(
                              width: 24,
                              height: 24,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation(Colors.white),
                              ),
                            )
                            : Text(
                              isArabic ? 'نشر الطلب' : 'POST REQUEST',
                              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                            ),
                  ),
                ),

                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

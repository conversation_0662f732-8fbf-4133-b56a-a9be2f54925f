import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageProvider with ChangeNotifier {
  Locale _locale = const Locale('en');

  Locale get locale => _locale;
  bool get isArabic => _locale.languageCode == 'ar';
  bool get isEnglish => _locale.languageCode == 'en';

  LanguageProvider() {
    _loadLanguage();
  }

  void _loadLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    final languageCode = prefs.getString('language_code') ?? 'en';
    _locale = Locale(languageCode);
    notifyListeners();
  }

  Future<void> setLanguage(String languageCode) async {
    if (languageCode == _locale.languageCode) return;

    _locale = Locale(languageCode);
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('language_code', languageCode);
    notifyListeners();
  }

  Future<void> toggleLanguage() async {
    final newLanguageCode = _locale.languageCode == 'en' ? 'ar' : 'en';
    await setLanguage(newLanguageCode);
  }

  // Get text direction based on current locale
  TextDirection get textDirection {
    return _locale.languageCode == 'ar' ? TextDirection.rtl : TextDirection.ltr;
  }

  // Get alignment based on current locale
  Alignment get alignment {
    return _locale.languageCode == 'ar' ? Alignment.centerRight : Alignment.centerLeft;
  }

  // Get text align based on current locale
  TextAlign get textAlign {
    return _locale.languageCode == 'ar' ? TextAlign.right : TextAlign.left;
  }

  // Get cross axis alignment based on current locale
  CrossAxisAlignment get crossAxisAlignment {
    return _locale.languageCode == 'ar' ? CrossAxisAlignment.end : CrossAxisAlignment.start;
  }

  // Get main axis alignment for RTL support
  MainAxisAlignment get mainAxisAlignment {
    return _locale.languageCode == 'ar' ? MainAxisAlignment.end : MainAxisAlignment.start;
  }

  // Helper method to get localized number format
  String formatNumber(num number) {
    if (_locale.languageCode == 'ar') {
      // Convert to Arabic-Indic numerals
      return number.toString().replaceAllMapped(RegExp(r'[0-9]'), (match) {
        const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        return arabicNumerals[int.parse(match.group(0)!)];
      });
    }
    return number.toString();
  }

  // Helper method to get currency format
  String formatCurrency(double amount) {
    if (_locale.languageCode == 'ar') {
      return '${formatNumber(amount.round())} ريال';
    }
    return '${amount.toStringAsFixed(0)} SAR';
  }

  // Helper method for date formatting
  String formatDate(DateTime date) {
    if (_locale.languageCode == 'ar') {
      return '${formatNumber(date.day)}/${formatNumber(date.month)}/${formatNumber(date.year)}';
    }
    return '${date.day}/${date.month}/${date.year}';
  }

  // Helper method for time ago formatting
  String getTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      if (_locale.languageCode == 'ar') {
        return 'منذ ${formatNumber(difference.inDays)} ${difference.inDays == 1 ? 'يوم' : 'أيام'}';
      }
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      if (_locale.languageCode == 'ar') {
        return 'منذ ${formatNumber(difference.inHours)} ${difference.inHours == 1 ? 'ساعة' : 'ساعات'}';
      }
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else {
      if (_locale.languageCode == 'ar') {
        return 'منذ ${formatNumber(difference.inMinutes)} ${difference.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
      }
      return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    }
  }
}

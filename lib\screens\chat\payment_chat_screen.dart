import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:record/record.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:io';
import '../../providers/language_provider.dart';
import '../../services/payment_service.dart';
import '../../services/storage_service.dart';

class PaymentChatScreen extends StatefulWidget {
  final String requestId;
  final String requestTitle;
  final double amount;

  const PaymentChatScreen({super.key, required this.requestId, required this.requestTitle, required this.amount});

  @override
  State<PaymentChatScreen> createState() => _PaymentChatScreenState();
}

class _PaymentChatScreenState extends State<PaymentChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final AudioRecorder _audioRecorder = AudioRecorder();

  List<Map<String, dynamic>> _messages = [];
  bool _isUploading = false;
  bool _isRecording = false;
  String _paymentStatus = 'pending'; // pending, uploaded, confirmed, rejected

  @override
  void initState() {
    super.initState();
    _loadInitialMessages();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _audioRecorder.dispose();
    super.dispose();
  }

  void _loadInitialMessages() {
    final bankDetails = PaymentService.getBankAccountDetails();

    setState(() {
      _messages = [
        {
          'id': '1',
          'senderId': 'admin',
          'senderName': 'دعم الإدارة',
          'content': 'مرحباً! سأساعدك في عملية الدفع لـ "${widget.requestTitle}".',
          'timestamp': DateTime.now().subtract(const Duration(minutes: 5)),
          'isMe': false,
          'type': 'text',
        },
        {
          'id': '2',
          'senderId': 'admin',
          'senderName': 'دعم الإدارة',
          'content': 'مبلغ الدفع المطلوب: \$${widget.amount.toStringAsFixed(2)}',
          'timestamp': DateTime.now().subtract(const Duration(minutes: 4)),
          'isMe': false,
          'type': 'text',
        },
        {
          'id': '3',
          'senderId': 'admin',
          'senderName': 'دعم الإدارة',
          'content': 'يرجى تحويل المبلغ إلى الحساب المصرفي التالي:',
          'timestamp': DateTime.now().subtract(const Duration(minutes: 4)),
          'isMe': false,
          'type': 'text',
        },
        {
          'id': '4',
          'senderId': 'admin',
          'senderName': 'دعم الإدارة',
          'content': '',
          'timestamp': DateTime.now().subtract(const Duration(minutes: 3)),
          'isMe': false,
          'type': 'bank_details',
          'bankDetails': bankDetails,
        },
        {
          'id': '5',
          'senderId': 'admin',
          'senderName': 'دعم الإدارة',
          'content': 'بعد إجراء التحويل، يرجى رفع لقطة شاشة أو صورة لإيصال الدفع.',
          'timestamp': DateTime.now().subtract(const Duration(minutes: 2)),
          'isMe': false,
          'type': 'text',
        },
      ];
    });
  }

  void _sendMessage() {
    if (_messageController.text.trim().isEmpty) return;

    final newMessage = {
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
      'senderId': 'me',
      'senderName': 'أنت',
      'content': _messageController.text.trim(),
      'timestamp': DateTime.now(),
      'isMe': true,
      'type': 'text',
    };

    setState(() {
      _messages.add(newMessage);
    });

    _messageController.clear();
    _scrollToBottom();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _startVoiceRecording() async {
    try {
      // Check and request microphone permission
      final permission = await Permission.microphone.request();
      if (permission != PermissionStatus.granted) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('يجب السماح بالوصول للميكروفون لتسجيل الرسائل الصوتية'),
              duration: Duration(seconds: 3),
            ),
          );
        }
        return;
      }

      // Check if recorder is available
      if (await _audioRecorder.hasPermission()) {
        setState(() {
          _isRecording = true;
        });

        // Start recording
        await _audioRecorder.start(
          const RecordConfig(encoder: AudioEncoder.aacLc, bitRate: 128000, sampleRate: 44100),
          path: '${Directory.systemTemp.path}/voice_message_${DateTime.now().millisecondsSinceEpoch}.m4a',
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('بدأ التسجيل... اضغط مرة أخرى للإيقاف'), duration: Duration(seconds: 2)),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isRecording = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في بدء التسجيل: $e'), duration: const Duration(seconds: 3)));
      }
    }
  }

  void _stopVoiceRecording() async {
    try {
      final path = await _audioRecorder.stop();

      if (path != null && mounted) {
        setState(() {
          _isRecording = false;
        });

        // Upload the voice message to storage
        final voiceFile = File(path);
        final voiceUrl = await StorageService.uploadFile(voiceFile, 'voice_messages');

        if (voiceUrl != null) {
          // Add voice message to chat
          final voiceMessage = {
            'id': DateTime.now().millisecondsSinceEpoch.toString(),
            'senderId': 'me',
            'senderName': 'أنت',
            'content': 'رسالة صوتية',
            'timestamp': DateTime.now(),
            'isMe': true,
            'type': 'voice',
            'voiceUrl': voiceUrl,
            'duration': '0:00', // You can calculate actual duration if needed
          };

          setState(() {
            _messages.add(voiceMessage);
          });

          _scrollToBottom();

          // Clean up temporary file
          try {
            await voiceFile.delete();
          } catch (e) {
            // Ignore cleanup errors
          }

          if (mounted) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(const SnackBar(content: Text('تم إرسال الرسالة الصوتية'), duration: Duration(seconds: 2)));
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(const SnackBar(content: Text('فشل في رفع الرسالة الصوتية'), duration: Duration(seconds: 3)));
          }
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isRecording = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في إيقاف التسجيل: $e'), duration: const Duration(seconds: 3)));
      }
    }
  }

  void _uploadPaymentProof() async {
    try {
      setState(() {
        _isUploading = true;
      });

      final image = await StorageService.pickImage();
      if (image != null) {
        final imageUrl = await StorageService.uploadImage(image, 'payment_proofs');

        if (imageUrl != null) {
          setState(() {
            _paymentStatus = 'uploaded';
          });

          // Add message with payment proof
          final proofMessage = {
            'id': DateTime.now().millisecondsSinceEpoch.toString(),
            'senderId': 'me',
            'senderName': 'أنت',
            'content': 'تم رفع إثبات الدفع',
            'timestamp': DateTime.now(),
            'isMe': true,
            'type': 'payment_proof',
            'imageUrl': imageUrl,
          };

          setState(() {
            _messages.add(proofMessage);
          });

          // Admin response
          Future.delayed(const Duration(seconds: 2), () {
            final adminResponse = {
              'id': DateTime.now().millisecondsSinceEpoch.toString(),
              'senderId': 'admin',
              'senderName': 'دعم الإدارة',
              'content': 'شكراً لك! لقد استلمت إثبات الدفع. سأقوم بالتحقق منه والرد عليك قريباً.',
              'timestamp': DateTime.now(),
              'isMe': false,
              'type': 'text',
            };

            setState(() {
              _messages.add(adminResponse);
            });

            _scrollToBottom();

            // Simulate admin confirmation after 5 seconds
            Future.delayed(const Duration(seconds: 5), () {
              _simulatePaymentConfirmation();
            });
          });

          _scrollToBottom();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error uploading image: $e'), backgroundColor: Colors.red));
      }
    } finally {
      setState(() {
        _isUploading = false;
      });
    }
  }

  void _simulatePaymentConfirmation() {
    final confirmationMessage = {
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
      'senderId': 'admin',
      'senderName': 'دعم الإدارة',
      'content': '✅ تم تأكيد الدفع! تم إشعار المستقل ويمكنه الآن البدء في العمل على مشروعك.',
      'timestamp': DateTime.now(),
      'isMe': false,
      'type': 'confirmation',
    };

    setState(() {
      _messages.add(confirmationMessage);
      _paymentStatus = 'confirmed';
    });

    _scrollToBottom();

    // Show success dialog
    Future.delayed(const Duration(milliseconds: 500), () {
      _showPaymentConfirmedDialog();
    });
  }

  void _showPaymentConfirmedDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            icon: const Icon(Icons.check_circle, color: Colors.green, size: 48),
            title: const Text('تم تأكيد الدفع!'),
            content: const Text('تم التحقق من دفعتك بنجاح. سيتم إشعار المستقل ويمكنه الآن البدء في العمل على مشروعك.'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop(); // Close dialog
                  Navigator.of(context).pop(); // Go back to chat
                },
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('تم النسخ إلى الحافظة'), duration: Duration(seconds: 1)));
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        appBar: AppBar(
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                languageProvider.isArabic ? 'دعم الإدارة' : 'Admin Support',
                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              Text(
                languageProvider.isArabic ? 'معالجة الدفع' : 'Payment Processing',
                style: const TextStyle(fontSize: 12, fontWeight: FontWeight.normal),
              ),
            ],
          ),
          backgroundColor: Colors.green.withValues(alpha: 0.1),
        ),
        body: Column(
          children: [
            // Payment Status Banner
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: _getStatusColor().withValues(alpha: 0.1),
                border: Border(bottom: BorderSide(color: _getStatusColor().withValues(alpha: 0.3), width: 1)),
              ),
              child: Row(
                children: [
                  Icon(_getStatusIcon(), color: _getStatusColor()),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _getStatusTitle(),
                          style: TextStyle(fontWeight: FontWeight.bold, color: _getStatusColor()),
                        ),
                        Text(_getStatusDescription(), style: TextStyle(fontSize: 12, color: _getStatusColor())),
                      ],
                    ),
                  ),
                  if (_paymentStatus == 'pending')
                    ElevatedButton.icon(
                      onPressed: _isUploading ? null : _uploadPaymentProof,
                      icon:
                          _isUploading
                              ? const SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2))
                              : const Icon(Icons.upload),
                      label: Text(
                        _isUploading
                            ? (languageProvider.isArabic ? 'جاري الرفع...' : 'Uploading...')
                            : (languageProvider.isArabic ? 'رفع الإثبات' : 'Upload Proof'),
                      ),
                      style: ElevatedButton.styleFrom(backgroundColor: Colors.green, foregroundColor: Colors.white),
                    ),
                ],
              ),
            ),

            // Messages List
            Expanded(
              child: ListView.builder(
                controller: _scrollController,
                padding: const EdgeInsets.all(16),
                itemCount: _messages.length,
                itemBuilder: (context, index) {
                  final message = _messages[index];
                  return _buildMessageBubble(message, languageProvider);
                },
              ),
            ),

            // Message Input (only if payment not confirmed)
            if (_paymentStatus != 'confirmed')
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  border: Border(top: BorderSide(color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2))),
                ),
                child: Row(
                  children: [
                    // Message Input Field
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(25),
                          border: Border.all(color: Colors.grey[300]!, width: 1),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: TextField(
                                controller: _messageController,
                                decoration: InputDecoration(
                                  hintText: languageProvider.isArabic ? 'رسالة' : 'Message',
                                  hintStyle: const TextStyle(color: Colors.grey, fontSize: 16),
                                  border: InputBorder.none,
                                  contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                                ),
                                maxLines: null,
                                textInputAction: TextInputAction.send,
                                onSubmitted: (_) => _sendMessage(),
                                onChanged: (text) {
                                  setState(() {
                                    // Trigger rebuild to show/hide send button
                                  });
                                },
                              ),
                            ),
                            // Attachment Icon
                            IconButton(
                              icon: Icon(Icons.attach_file, color: Colors.grey[600], size: 24),
                              onPressed: () {
                                // Implement file attachment
                              },
                            ),
                            // Camera Icon
                            IconButton(
                              icon: Icon(Icons.camera_alt_outlined, color: Colors.grey[600], size: 24),
                              onPressed: _isUploading ? null : _uploadPaymentProof,
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Voice/Send Button
                    GestureDetector(
                      onTap:
                          _messageController.text.isNotEmpty
                              ? _sendMessage
                              : (_isRecording ? _stopVoiceRecording : null),
                      onLongPress: _messageController.text.isEmpty && !_isRecording ? _startVoiceRecording : null,
                      child: Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color:
                              _isRecording
                                  ? Colors.red
                                  : (_messageController.text.isNotEmpty
                                      ? Theme.of(context).colorScheme.primary
                                      : Colors.black87),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          _isRecording ? Icons.stop : (_messageController.text.isNotEmpty ? Icons.send : Icons.mic),
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageBubble(Map<String, dynamic> message, LanguageProvider languageProvider) {
    final isMe = message['isMe'] as bool;
    final timestamp = message['timestamp'] as DateTime;
    final type = message['type'] as String;

    if (type == 'bank_details') {
      return _buildBankDetailsCard(message['bankDetails']);
    }

    if (type == 'payment_proof') {
      return _buildPaymentProofMessage(message, languageProvider);
    }

    if (type == 'confirmation') {
      return _buildConfirmationMessage(message, languageProvider);
    }

    if (type == 'voice') {
      return _buildVoiceMessage(message, languageProvider);
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isMe) ...[
            const CircleAvatar(
              radius: 16,
              backgroundColor: Colors.green,
              child: Icon(Icons.support_agent, size: 16, color: Colors.white),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: isMe ? Theme.of(context).colorScheme.primary : Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(18).copyWith(
                  bottomLeft: isMe ? const Radius.circular(18) : const Radius.circular(4),
                  bottomRight: isMe ? const Radius.circular(4) : const Radius.circular(18),
                ),
                border: !isMe ? Border.all(color: Colors.green.withValues(alpha: 0.3)) : null,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message['content'],
                    style: TextStyle(color: isMe ? Theme.of(context).colorScheme.onPrimary : Colors.green[800]),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    languageProvider.getTimeAgo(timestamp),
                    style: TextStyle(
                      fontSize: 10,
                      color: isMe ? Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.7) : Colors.green[600],
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (isMe) ...[const SizedBox(width: 8), const CircleAvatar(radius: 16, child: Icon(Icons.person, size: 16))],
        ],
      ),
    );
  }

  Widget _buildBankDetailsCard(Map<String, String> bankDetails) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.account_balance, color: Colors.blue[700]),
              const SizedBox(width: 8),
              Text('تفاصيل التحويل المصرفي', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.blue[700])),
            ],
          ),
          const SizedBox(height: 16),
          ...bankDetails.entries.where((e) => e.key != 'instructions').map((entry) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(_formatBankDetailKey(entry.key), style: const TextStyle(fontWeight: FontWeight.w500)),
                  ),
                  Expanded(
                    flex: 3,
                    child: Row(
                      children: [
                        Expanded(child: Text(entry.value, style: const TextStyle(fontFamily: 'monospace'))),
                        IconButton(
                          icon: const Icon(Icons.copy, size: 16),
                          onPressed: () => _copyToClipboard(entry.value),
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }),
          if (bankDetails['instructions'] != null) ...[
            const Divider(),
            Text(
              bankDetails['instructions']!,
              style: TextStyle(fontSize: 12, fontStyle: FontStyle.italic, color: Colors.blue[600]),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPaymentProofMessage(Map<String, dynamic> message, LanguageProvider languageProvider) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                borderRadius: BorderRadius.circular(18).copyWith(bottomRight: const Radius.circular(4)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message['content'],
                    style: TextStyle(color: Theme.of(context).colorScheme.onPrimary, fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      width: 200,
                      height: 150,
                      color: Colors.grey[300],
                      child: const Icon(Icons.image, size: 48, color: Colors.grey),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    languageProvider.getTimeAgo(message['timestamp']),
                    style: TextStyle(
                      fontSize: 10,
                      color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 8),
          const CircleAvatar(radius: 16, child: Icon(Icons.person, size: 16)),
        ],
      ),
    );
  }

  Widget _buildConfirmationMessage(Map<String, dynamic> message, LanguageProvider languageProvider) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          const Icon(Icons.check_circle, color: Colors.green, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(message['content'], style: const TextStyle(fontWeight: FontWeight.w500, color: Colors.green)),
                const SizedBox(height: 4),
                Text(
                  languageProvider.getTimeAgo(message['timestamp']),
                  style: TextStyle(fontSize: 10, color: Colors.green[600]),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatBankDetailKey(String key) {
    switch (key) {
      case 'bankName':
        return 'اسم البنك';
      case 'accountName':
        return 'اسم الحساب';
      case 'iban':
        return 'رقم الآيبان';
      case 'swiftCode':
        return 'رمز السويفت';
      case 'accountNumber':
        return 'رقم الحساب';
      default:
        return '$key:';
    }
  }

  Color _getStatusColor() {
    switch (_paymentStatus) {
      case 'pending':
        return Colors.orange;
      case 'uploaded':
        return Colors.blue;
      case 'confirmed':
        return Colors.green;
      case 'rejected':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon() {
    switch (_paymentStatus) {
      case 'pending':
        return Icons.schedule;
      case 'uploaded':
        return Icons.upload_file;
      case 'confirmed':
        return Icons.check_circle;
      case 'rejected':
        return Icons.cancel;
      default:
        return Icons.info;
    }
  }

  String _getStatusTitle() {
    switch (_paymentStatus) {
      case 'pending':
        return 'دفع مطلوب';
      case 'uploaded':
        return 'الدفع قيد المراجعة';
      case 'confirmed':
        return 'تم تأكيد الدفع';
      case 'rejected':
        return 'تم رفض الدفع';
      default:
        return 'حالة الدفع';
    }
  }

  String _getStatusDescription() {
    switch (_paymentStatus) {
      case 'pending':
        return 'يرجى إجراء التحويل المصرفي ورفع الإثبات';
      case 'uploaded':
        return 'الإدارة تراجع إثبات الدفع الخاص بك';
      case 'confirmed':
        return 'تم إشعار المستقل لبدء العمل';
      case 'rejected':
        return 'يرجى التحقق من دفعتك والمحاولة مرة أخرى';
      default:
        return '';
    }
  }

  Widget _buildVoiceMessage(Map<String, dynamic> message, LanguageProvider languageProvider) {
    final isMe = message['isMe'] as bool;
    final duration = message['duration'] as String? ?? '0:00';

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isMe) ...[
            const CircleAvatar(
              radius: 16,
              backgroundColor: Colors.green,
              child: Icon(Icons.support_agent, size: 16, color: Colors.white),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: isMe ? Theme.of(context).colorScheme.primary : Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(18).copyWith(
                  bottomLeft: isMe ? const Radius.circular(18) : const Radius.circular(4),
                  bottomRight: isMe ? const Radius.circular(4) : const Radius.circular(18),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.play_arrow, color: isMe ? Colors.white : Theme.of(context).colorScheme.primary, size: 24),
                  const SizedBox(width: 8),
                  Container(
                    width: 100,
                    height: 4,
                    decoration: BoxDecoration(
                      color: (isMe ? Colors.white : Theme.of(context).colorScheme.primary).withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(2),
                    ),
                    child: Align(
                      alignment: Alignment.centerLeft,
                      child: Container(
                        width: 30,
                        height: 4,
                        decoration: BoxDecoration(
                          color: isMe ? Colors.white : Theme.of(context).colorScheme.primary,
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(duration, style: TextStyle(color: isMe ? Colors.white : Colors.black87, fontSize: 12)),
                ],
              ),
            ),
          ),
          if (isMe) ...[
            const SizedBox(width: 8),
            const CircleAvatar(
              radius: 16,
              backgroundColor: Colors.blue,
              child: Icon(Icons.person, size: 16, color: Colors.white),
            ),
          ],
        ],
      ),
    );
  }
}

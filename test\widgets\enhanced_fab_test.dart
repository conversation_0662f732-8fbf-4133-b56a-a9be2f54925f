import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:anotherchance/widgets/common/enhanced_widgets.dart';
import 'package:anotherchance/providers/theme_provider.dart';
import 'package:anotherchance/providers/language_provider.dart';

void main() {
  group('Enhanced Floating Action Button Tests', () {
    testWidgets('FAB displays correctly with proper styling', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => ThemeProvider()),
            ChangeNotifierProvider(create: (_) => LanguageProvider()),
          ],
          child: MaterialApp(
            home: Scaffold(
              body: const Center(child: Text('Test')),
              floatingActionButton: EnhancedFloatingActionButton(
                onPressed: () {},
                icon: Icons.add,
                tooltip: 'Test FAB',
              ),
            ),
          ),
        ),
      );

      // Verify FAB is present
      expect(find.byType(EnhancedFloatingActionButton), findsOneWidget);
      expect(find.byIcon(Icons.add), findsOneWidget);
    });

    testWidgets('FAB responds to tap with animation', (WidgetTester tester) async {
      bool tapped = false;

      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => ThemeProvider()),
            ChangeNotifierProvider(create: (_) => LanguageProvider()),
          ],
          child: MaterialApp(
            home: Scaffold(
              body: const Center(child: Text('Test')),
              floatingActionButton: EnhancedFloatingActionButton(
                onPressed: () {
                  tapped = true;
                },
                icon: Icons.add,
                tooltip: 'Test FAB',
              ),
            ),
          ),
        ),
      );

      // Tap the FAB
      await tester.tap(find.byType(EnhancedFloatingActionButton));
      await tester.pump();

      // Verify tap was registered
      expect(tapped, isTrue);
    });

    testWidgets('FAB shows tooltip on long press', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => ThemeProvider()),
            ChangeNotifierProvider(create: (_) => LanguageProvider()),
          ],
          child: MaterialApp(
            home: Scaffold(
              body: const Center(child: Text('Test')),
              floatingActionButton: EnhancedFloatingActionButton(
                onPressed: () {},
                icon: Icons.add,
                tooltip: 'Create New Request',
              ),
            ),
          ),
        ),
      );

      // Long press to show tooltip
      await tester.longPress(find.byType(EnhancedFloatingActionButton));
      await tester.pump(const Duration(milliseconds: 500));

      // Verify tooltip is shown
      expect(find.text('Create New Request'), findsOneWidget);
    });

    testWidgets('FAB supports different sizes', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => ThemeProvider()),
            ChangeNotifierProvider(create: (_) => LanguageProvider()),
          ],
          child: MaterialApp(
            home: Scaffold(
              body: Column(
                children: [
                  EnhancedFloatingActionButton(onPressed: () {}, icon: Icons.add, tooltip: 'Regular FAB', mini: false),
                  EnhancedFloatingActionButton(onPressed: () {}, icon: Icons.add, tooltip: 'Mini FAB', mini: true),
                ],
              ),
            ),
          ),
        ),
      );

      // Verify both FABs are present
      expect(find.byType(EnhancedFloatingActionButton), findsNWidgets(2));
    });

    testWidgets('FAB supports custom colors', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => ThemeProvider()),
            ChangeNotifierProvider(create: (_) => LanguageProvider()),
          ],
          child: MaterialApp(
            home: Scaffold(
              body: const Center(child: Text('Test')),
              floatingActionButton: EnhancedFloatingActionButton(
                onPressed: () {},
                icon: Icons.add,
                tooltip: 'Custom Color FAB',
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ),
      );

      // Verify FAB is present with custom styling
      expect(find.byType(EnhancedFloatingActionButton), findsOneWidget);
    });
  });
}

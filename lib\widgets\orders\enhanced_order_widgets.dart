import 'package:flutter/material.dart';
import '../../providers/theme_provider.dart';

/// Enhanced Order Card with improved styling
class EnhancedOrderCard extends StatelessWidget {
  final String title;
  final String clientName;
  final String price;
  final String deadline;
  final String status;
  final double rating;
  final VoidCallback onTap;
  final bool isArabic;
  final List<String> tags;

  const EnhancedOrderCard({
    super.key,
    required this.title,
    required this.clientName,
    required this.price,
    required this.deadline,
    required this.status,
    required this.rating,
    required this.onTap,
    this.isArabic = false,
    this.tags = const [],
  });

  Color _getStatusColor() {
    switch (status.toLowerCase()) {
      case 'active':
      case 'نشط':
        return ThemeProvider.warningOrange;
      case 'delivered':
      case 'مسلم':
        return ThemeProvider.primaryBlue;
      case 'completed':
      case 'مكتمل':
        return ThemeProvider.successGreen;
      case 'cancelled':
      case 'ملغي':
        return Colors.red;
      default:
        return ThemeProvider.neutralGray;
    }
  }

  IconData _getStatusIcon() {
    switch (status.toLowerCase()) {
      case 'active':
      case 'نشط':
        return Icons.work_outline;
      case 'delivered':
      case 'مسلم':
        return Icons.check_circle_outline;
      case 'completed':
      case 'مكتمل':
        return Icons.check_circle_outline;
      case 'cancelled':
      case 'ملغي':
        return Icons.cancel_outlined;
      default:
        return Icons.info_outline;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final statusColor = _getStatusColor();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: isDark ? ThemeProvider.darkCardBackground : ThemeProvider.cardBackground,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
          BoxShadow(color: statusColor.withValues(alpha: 0.1), blurRadius: 40, offset: const Offset(0, 8)),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with status
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: statusColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(_getStatusIcon(), size: 16, color: statusColor),
                          const SizedBox(width: 6),
                          Text(status, style: TextStyle(color: statusColor, fontWeight: FontWeight.w600, fontSize: 12)),
                        ],
                      ),
                    ),
                    const Spacer(),
                    if (rating > 0) ...[
                      const Icon(Icons.star, size: 16, color: Colors.amber),
                      const SizedBox(width: 4),
                      Text(
                        rating.toStringAsFixed(1),
                        style: TextStyle(
                          color: isDark ? ThemeProvider.darkTextSecondary : ThemeProvider.neutralGray,
                          fontWeight: FontWeight.w600,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ],
                ),

                const SizedBox(height: 16),

                // Title
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                    height: 1.3,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 12),

                // Client info
                Row(
                  children: [
                    const Icon(Icons.person_outline, size: 16, color: ThemeProvider.primaryBlue),
                    const SizedBox(width: 6),
                    Text(
                      clientName,
                      style: TextStyle(
                        color: isDark ? ThemeProvider.darkTextSecondary : ThemeProvider.neutralGray,
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                // Price and deadline
                Row(
                  children: [
                    Expanded(
                      child: Row(
                        children: [
                          const Icon(Icons.attach_money, size: 16, color: ThemeProvider.successGreen),
                          const SizedBox(width: 6),
                          Text(
                            price,
                            style: const TextStyle(
                              color: ThemeProvider.successGreen,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Row(
                      children: [
                        const Icon(Icons.schedule, size: 16, color: ThemeProvider.warningOrange),
                        const SizedBox(width: 6),
                        Text(
                          deadline,
                          style: TextStyle(
                            color: isDark ? ThemeProvider.darkTextSecondary : ThemeProvider.neutralGray,
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                // Tags
                if (tags.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Wrap(
                    spacing: 8,
                    runSpacing: 6,
                    children:
                        tags.take(3).map((tag) {
                          return Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: ThemeProvider.primaryBlue.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              tag,
                              style: const TextStyle(
                                color: ThemeProvider.primaryBlue,
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          );
                        }).toList(),
                  ),
                ],

                const SizedBox(height: 16),

                // Action button
                Container(
                  width: double.infinity,
                  height: 44,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(colors: [statusColor, statusColor.withValues(alpha: 0.8)]),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(color: statusColor.withValues(alpha: 0.3), blurRadius: 8, offset: const Offset(0, 2)),
                    ],
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: onTap,
                      borderRadius: BorderRadius.circular(12),
                      child: Center(
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              isArabic ? 'عرض التفاصيل' : 'View Details',
                              style: const TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.w600),
                            ),
                            const SizedBox(width: 8),
                            Icon(isArabic ? Icons.arrow_back : Icons.arrow_forward, color: Colors.white, size: 18),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Enhanced Progress Indicator for orders
class EnhancedProgressIndicator extends StatelessWidget {
  final double progress;
  final String currentStep;
  final List<String> steps;
  final bool isArabic;

  const EnhancedProgressIndicator({
    super.key,
    required this.progress,
    required this.currentStep,
    required this.steps,
    this.isArabic = false,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark ? ThemeProvider.darkCardBackground : ThemeProvider.cardBackground,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'تقدم المشروع' : 'Project Progress',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
            ),
          ),
          const SizedBox(height: 16),

          // Progress bar
          Container(
            height: 8,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: isDark ? ThemeProvider.darkTextSecondary.withValues(alpha: 0.3) : Colors.grey[300],
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: progress,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  gradient: const LinearGradient(colors: [ThemeProvider.primaryBlue, ThemeProvider.successGreen]),
                ),
              ),
            ),
          ),

          const SizedBox(height: 12),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                currentStep,
                style: const TextStyle(color: ThemeProvider.primaryBlue, fontWeight: FontWeight.w600, fontSize: 14),
              ),
              Text(
                '${(progress * 100).toInt()}%',
                style: TextStyle(
                  color: isDark ? ThemeProvider.darkTextSecondary : ThemeProvider.neutralGray,
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

import '../models/wallet_model.dart';
import '../config/supabase_config.dart';

class WalletService {
  /// Get wallet for a user
  static Future<WalletModel?> getWallet(String userId) async {
    try {
      final response = await SupabaseConfig.client.from('wallets').select().eq('user_id', userId).single();

      return WalletModel.fromJson(response);
    } catch (e) {
      // Return demo wallet for demo purposes
      return _getDemoWallet(userId);
    }
  }

  /// Create wallet for a user
  static Future<WalletModel> createWallet(String userId) async {
    try {
      final wallet = WalletModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: userId,
        balance: 0.0,
        createdAt: DateTime.now(),
      );

      final response = await SupabaseConfig.client.from('wallets').insert(wallet.toJson()).select().single();

      return WalletModel.from<PERSON><PERSON>(response);
    } catch (e) {
      throw Exception('Failed to create wallet: $e');
    }
  }

  /// Add earnings to wallet
  static Future<WalletModel> addEarnings({
    required String userId,
    required double amount,
    required String orderId,
    required String description,
  }) async {
    try {
      // Get current wallet
      WalletModel? wallet = await getWallet(userId);

      // Create wallet if it doesn't exist
      wallet ??= await createWallet(userId);

      // Create transaction
      final transaction = TransactionModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        walletId: wallet.id,
        orderId: orderId,
        type: TransactionType.earning,
        status: TransactionStatus.completed,
        amount: amount,
        description: description,
        createdAt: DateTime.now(),
        completedAt: DateTime.now(),
      );

      // Add transaction
      await _addTransaction(transaction);

      // Update wallet balance
      final updatedWallet = wallet.copyWith(
        balance: wallet.balance + amount,
        totalEarnings: wallet.totalEarnings + amount,
        updatedAt: DateTime.now(),
      );

      await _updateWallet(updatedWallet);

      return updatedWallet;
    } catch (e) {
      throw Exception('Failed to add earnings: $e');
    }
  }

  /// Process withdrawal
  static Future<WalletModel> processWithdrawal({
    required String userId,
    required double amount,
    required String description,
    Map<String, dynamic>? paymentDetails,
  }) async {
    try {
      final wallet = await getWallet(userId);
      if (wallet == null) {
        throw Exception('Wallet not found');
      }

      if (wallet.balance < amount) {
        throw Exception('Insufficient balance');
      }

      // Create withdrawal transaction
      final transaction = TransactionModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        walletId: wallet.id,
        type: TransactionType.withdrawal,
        status: TransactionStatus.pending,
        amount: amount,
        description: description,
        metadata: paymentDetails,
        createdAt: DateTime.now(),
      );

      // Add transaction
      await _addTransaction(transaction);

      // Update wallet balance
      final updatedWallet = wallet.copyWith(
        balance: wallet.balance - amount,
        totalWithdrawals: wallet.totalWithdrawals + amount,
        updatedAt: DateTime.now(),
      );

      await _updateWallet(updatedWallet);

      return updatedWallet;
    } catch (e) {
      throw Exception('Failed to process withdrawal: $e');
    }
  }

  /// Get transaction history
  static Future<List<TransactionModel>> getTransactionHistory(String userId) async {
    try {
      final wallet = await getWallet(userId);
      if (wallet == null) return [];

      final response = await SupabaseConfig.client
          .from('transactions')
          .select()
          .eq('wallet_id', wallet.id)
          .order('created_at', ascending: false);

      return response.map<TransactionModel>((json) => TransactionModel.fromJson(json)).toList();
    } catch (e) {
      // Return demo transactions for demo purposes
      return _getDemoTransactions(userId);
    }
  }

  /// Update wallet
  static Future<void> _updateWallet(WalletModel wallet) async {
    try {
      await SupabaseConfig.client.from('wallets').update(wallet.toJson()).eq('id', wallet.id);
    } catch (e) {
      // For demo purposes, just log the update
      print('Demo: Updated wallet ${wallet.id} with balance ${wallet.balance}');
    }
  }

  /// Add transaction
  static Future<void> _addTransaction(TransactionModel transaction) async {
    try {
      await SupabaseConfig.client.from('transactions').insert(transaction.toJson());
    } catch (e) {
      // For demo purposes, just log the transaction
      print('Demo: Added transaction ${transaction.id} for ${transaction.formattedAmount}');
    }
  }

  /// Get demo wallet
  static WalletModel _getDemoWallet(String userId) {
    return WalletModel(
      id: 'demo_wallet_$userId',
      userId: userId,
      balance: 2450.00,
      totalEarnings: 5200.00,
      totalWithdrawals: 2750.00,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now(),
    );
  }

  /// Get demo transactions
  static List<TransactionModel> _getDemoTransactions(String userId) {
    final now = DateTime.now();
    return [
      TransactionModel(
        id: 'demo_tx_1',
        walletId: 'demo_wallet_$userId',
        orderId: 'order_1',
        type: TransactionType.earning,
        status: TransactionStatus.completed,
        amount: 1500.00,
        description: 'Payment for Mobile App Development',
        createdAt: now.subtract(const Duration(days: 2)),
        completedAt: now.subtract(const Duration(days: 2)),
      ),
      TransactionModel(
        id: 'demo_tx_2',
        walletId: 'demo_wallet_$userId',
        type: TransactionType.withdrawal,
        status: TransactionStatus.completed,
        amount: 1000.00,
        description: 'Withdrawal to Vodafone Cash',
        metadata: {'method': 'vodafone_cash', 'phone': '01234567890'},
        createdAt: now.subtract(const Duration(days: 5)),
        completedAt: now.subtract(const Duration(days: 4)),
      ),
      TransactionModel(
        id: 'demo_tx_3',
        walletId: 'demo_wallet_$userId',
        orderId: 'order_2',
        type: TransactionType.earning,
        status: TransactionStatus.completed,
        amount: 2000.00,
        description: 'Payment for Website Development',
        createdAt: now.subtract(const Duration(days: 8)),
        completedAt: now.subtract(const Duration(days: 8)),
      ),
      TransactionModel(
        id: 'demo_tx_4',
        walletId: 'demo_wallet_$userId',
        type: TransactionType.withdrawal,
        status: TransactionStatus.pending,
        amount: 500.00,
        description: 'Withdrawal to InstaPay',
        metadata: {'method': 'instapay', 'account': '<EMAIL>'},
        createdAt: now.subtract(const Duration(hours: 6)),
      ),
      TransactionModel(
        id: 'demo_tx_5',
        walletId: 'demo_wallet_$userId',
        orderId: 'order_3',
        type: TransactionType.earning,
        status: TransactionStatus.completed,
        amount: 1700.00,
        description: 'Payment for Logo Design',
        createdAt: now.subtract(const Duration(days: 12)),
        completedAt: now.subtract(const Duration(days: 12)),
      ),
    ];
  }

  /// Format currency
  static String formatCurrency(double amount) {
    return '${amount.toStringAsFixed(2)} ريال';
  }

  /// Check if withdrawal amount is valid
  static bool isValidWithdrawalAmount(double amount, double balance) {
    const double minWithdrawal = 500.0; // Minimum SAR 500 as mentioned in requirements
    return amount >= minWithdrawal && amount <= balance;
  }

  /// Get minimum withdrawal amount
  static double get minimumWithdrawalAmount => 500.0;
}

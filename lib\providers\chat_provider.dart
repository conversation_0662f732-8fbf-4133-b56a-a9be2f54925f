import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/chat_model.dart';
import '../models/message_model.dart';
import '../models/user_model.dart';
import '../services/chat_service.dart';

class Chat<PERSON>rovider extends ChangeNotifier {
  List<ChatModel> _chats = [];
  final Map<String, List<MessageModel>> _chatMessages = {};
  final Map<String, StreamSubscription> _messageSubscriptions = {};
  String? _currentUserId;
  bool _isLoading = false;
  String? _error;

  // Getters
  List<ChatModel> get chats => _chats;
  Map<String, List<MessageModel>> get chatMessages => _chatMessages;
  bool get isLoading => _isLoading;
  String? get error => _error;
  String? get currentUserId => _currentUserId;

  /// Get chats with customer service chat included for clients
  List<ChatModel> getChatsWithCustomerService({UserRole? userRole}) {
    if (userRole == UserRole.client && _currentUserId != null) {
      // Customer service chat will be handled in UI layer
      // This method is prepared for future enhancement
      return _chats;
    }
    return _chats;
  }

  /// Get messages for a specific chat
  List<MessageModel> getMessagesForChat(String chatId) {
    return _chatMessages[chatId] ?? [];
  }

  /// Get unread count for a specific chat
  int getUnreadCount(String chatId) {
    final messages = _chatMessages[chatId] ?? [];
    return messages.where((m) => !m.isRead && m.senderId != _currentUserId).length;
  }

  /// Get total unread count across all chats
  int get totalUnreadCount {
    return _chats.fold(0, (sum, chat) => sum + getUnreadCount(chat.id));
  }

  /// Set current user and load their chats
  Future<void> setUser(String userId) async {
    if (_currentUserId == userId) return;

    _currentUserId = userId;
    await loadChats();
  }

  /// Load chats for current user
  Future<void> loadChats() async {
    if (_currentUserId == null) return;

    _setLoading(true);
    try {
      _chats = await ChatService.getChats(_currentUserId!);
      _error = null;
      notifyListeners();

      // Load messages for each chat
      for (final chat in _chats) {
        await _loadMessagesForChat(chat.id);
      }
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  /// Load messages for a specific chat
  Future<void> _loadMessagesForChat(String chatId) async {
    try {
      final messages = await ChatService.getMessages(chatId);
      _chatMessages[chatId] = messages;

      // Subscribe to real-time updates
      _subscribeToMessages(chatId);

      notifyListeners();
    } catch (e) {
      print('Failed to load messages for chat $chatId: $e');
    }
  }

  /// Subscribe to real-time message updates
  void _subscribeToMessages(String chatId) {
    _messageSubscriptions[chatId]?.cancel();
    _messageSubscriptions[chatId] = ChatService.subscribeToMessages(chatId).listen(
      (messages) {
        final previousMessages = _chatMessages[chatId] ?? [];
        _chatMessages[chatId] = messages;

        // Check for new messages and trigger notifications
        _checkForNewMessages(chatId, previousMessages, messages);

        notifyListeners();
      },
      onError: (error) {
        print('Error in message subscription for chat $chatId: $error');
      },
    );
  }

  /// Check for new messages and trigger notifications/sounds
  void _checkForNewMessages(String chatId, List<MessageModel> previousMessages, List<MessageModel> newMessages) {
    if (_currentUserId == null) return;

    final newMessageCount = newMessages.length - previousMessages.length;
    if (newMessageCount > 0) {
      final latestMessages = newMessages.skip(previousMessages.length).toList();

      for (final message in latestMessages) {
        // Only notify for messages not sent by current user
        if (message.senderId != _currentUserId && !message.isSystemMessage) {
          _triggerMessageNotification(message);
        }
      }
    }
  }

  /// Trigger notification and haptic feedback for new message
  void _triggerMessageNotification(MessageModel message) {
    // Haptic feedback
    HapticFeedback.lightImpact();

    // Sound notification would go here (requires audio package)
    // AudioPlayer.play('assets/sounds/message_notification.mp3');

    // The notification is already created in ChatService.sendMessage
    // This is just for additional UI feedback
  }

  /// Send a message
  Future<MessageModel?> sendMessage({
    required String chatId,
    required String content,
    MessageType type = MessageType.text,
    String? fileUrl,
    String? fileName,
    Map<String, dynamic>? metadata,
  }) async {
    if (_currentUserId == null) return null;

    try {
      final message = MessageModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        chatId: chatId,
        senderId: _currentUserId!,
        content: content,
        type: type,
        fileUrl: fileUrl,
        fileName: fileName,
        createdAt: DateTime.now(),
        metadata: metadata,
      );

      final sentMessage = await ChatService.sendMessage(message);

      // Update local state immediately for better UX
      final currentMessages = _chatMessages[chatId] ?? [];
      _chatMessages[chatId] = [...currentMessages, sentMessage];
      notifyListeners();

      return sentMessage;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  /// Send a timeline message (system-generated)
  Future<MessageModel?> sendTimelineMessage({
    required String chatId,
    required String content,
    Map<String, dynamic>? metadata,
  }) async {
    return await sendMessage(chatId: chatId, content: content, type: MessageType.timeline, metadata: metadata);
  }

  /// Mark messages as read
  Future<void> markMessagesAsRead(String chatId) async {
    if (_currentUserId == null) return;

    try {
      await ChatService.markMessagesAsRead(chatId, _currentUserId!);

      // Update local state
      final messages = _chatMessages[chatId] ?? [];
      _chatMessages[chatId] =
          messages.map((m) {
            if (m.senderId != _currentUserId && !m.isRead) {
              return m.markAsRead();
            }
            return m;
          }).toList();

      notifyListeners();
    } catch (e) {
      print('Failed to mark messages as read: $e');
    }
  }

  /// Create a new chat
  Future<ChatModel?> createChat({
    required String requestId,
    required String clientId,
    required String freelancerId,
  }) async {
    try {
      final chat = ChatModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        requestId: requestId,
        clientId: clientId,
        freelancerId: freelancerId,
        createdAt: DateTime.now(),
      );

      final createdChat = await ChatService.createChat(chat);
      _chats.insert(0, createdChat);
      _chatMessages[createdChat.id] = [];

      // Subscribe to messages for the new chat
      _subscribeToMessages(createdChat.id);

      notifyListeners();
      return createdChat;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  /// Get or create chat for a request
  Future<ChatModel?> getOrCreateChatForRequest(String requestId, String clientId, String freelancerId) async {
    // First try to find existing chat
    final existingChat = await ChatService.getChatByRequestId(requestId);
    if (existingChat != null) {
      return existingChat;
    }

    // Create new chat if none exists
    return await createChat(requestId: requestId, clientId: clientId, freelancerId: freelancerId);
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  @override
  void dispose() {
    // Cancel all subscriptions
    for (final subscription in _messageSubscriptions.values) {
      subscription.cancel();
    }
    _messageSubscriptions.clear();
    super.dispose();
  }
}

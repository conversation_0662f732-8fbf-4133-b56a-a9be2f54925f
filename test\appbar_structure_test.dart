import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('AppBar Structure Tests', () {
    testWidgets('AppBar with TabBar displays correctly', (WidgetTester tester) async {
      // Create a test widget that mimics the my_orders_screen AppBar structure
      await tester.pumpWidget(
        MaterialApp(
          home: DefaultTabController(
            length: 4,
            child: Scaffold(
              appBar: AppBar(
                title: const Text(
                  'My Orders',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    fontSize: 20,
                  ),
                ),
                elevation: 0,
                backgroundColor: Colors.transparent,
                toolbarHeight: 56,
                automaticallyImplyLeading: false,
                flexibleSpace: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.blue,
                        Colors.blue.withValues(alpha: 0.8),
                      ],
                    ),
                  ),
                ),
                foregroundColor: Colors.white,
                bottom: PreferredSize(
                  preferredSize: const Size.fromHeight(48),
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border(
                        top: BorderSide(
                          color: Colors.white.withValues(alpha: 0.2),
                          width: 0.5,
                        ),
                      ),
                    ),
                    child: TabBar(
                      isScrollable: true,
                      labelColor: Colors.white,
                      unselectedLabelColor: Colors.white.withValues(alpha: 0.6),
                      indicatorColor: Colors.white,
                      indicatorWeight: 2,
                      indicatorSize: TabBarIndicatorSize.tab,
                      labelPadding: const EdgeInsets.symmetric(horizontal: 12),
                      labelStyle: const TextStyle(
                        fontSize: 11,
                        fontWeight: FontWeight.w600,
                      ),
                      unselectedLabelStyle: const TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                      ),
                      tabs: const [
                        Tab(
                          icon: Icon(Icons.assignment, size: 18),
                          text: 'Active',
                        ),
                        Tab(
                          icon: Icon(Icons.upload, size: 18),
                          text: 'Delivered',
                        ),
                        Tab(
                          icon: Icon(Icons.check_circle, size: 18),
                          text: 'Completed',
                        ),
                        Tab(
                          icon: Icon(Icons.cancel, size: 18),
                          text: 'Cancelled',
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              body: const TabBarView(
                children: [
                  Center(child: Text('Active Orders')),
                  Center(child: Text('Delivered Orders')),
                  Center(child: Text('Completed Orders')),
                  Center(child: Text('Cancelled Orders')),
                ],
              ),
            ),
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Test AppBar title
      expect(find.text('My Orders'), findsOneWidget);
      
      // Test TabBar tabs
      expect(find.text('Active'), findsOneWidget);
      expect(find.text('Delivered'), findsOneWidget);
      expect(find.text('Completed'), findsOneWidget);
      expect(find.text('Cancelled'), findsOneWidget);
      
      // Test tab icons
      expect(find.byIcon(Icons.assignment), findsOneWidget);
      expect(find.byIcon(Icons.upload), findsOneWidget);
      expect(find.byIcon(Icons.check_circle), findsOneWidget);
      expect(find.byIcon(Icons.cancel), findsOneWidget);
      
      // Test AppBar structure
      expect(find.byType(AppBar), findsOneWidget);
      expect(find.byType(TabBar), findsOneWidget);
      expect(find.byType(TabBarView), findsOneWidget);
    });

    testWidgets('TabBar responds to taps', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: DefaultTabController(
            length: 4,
            child: Scaffold(
              appBar: AppBar(
                title: const Text('My Orders'),
                bottom: const TabBar(
                  tabs: [
                    Tab(icon: Icon(Icons.assignment), text: 'Active'),
                    Tab(icon: Icon(Icons.upload), text: 'Delivered'),
                    Tab(icon: Icon(Icons.check_circle), text: 'Completed'),
                    Tab(icon: Icon(Icons.cancel), text: 'Cancelled'),
                  ],
                ),
              ),
              body: const TabBarView(
                children: [
                  Center(child: Text('Active Orders')),
                  Center(child: Text('Delivered Orders')),
                  Center(child: Text('Completed Orders')),
                  Center(child: Text('Cancelled Orders')),
                ],
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Tap on different tabs
      await tester.tap(find.text('Delivered'));
      await tester.pumpAndSettle();

      await tester.tap(find.text('Completed'));
      await tester.pumpAndSettle();

      await tester.tap(find.text('Cancelled'));
      await tester.pumpAndSettle();

      // Should not throw any errors
      expect(tester.takeException(), isNull);
    });

    testWidgets('AppBar styling properties work correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: AppBar(
              title: const Text('Test AppBar'),
              toolbarHeight: 56,
              elevation: 0,
              backgroundColor: Colors.transparent,
              automaticallyImplyLeading: false,
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find AppBar
      final appBarFinder = find.byType(AppBar);
      expect(appBarFinder, findsOneWidget);

      // Verify no overflow or rendering issues
      expect(tester.takeException(), isNull);
    });
  });
}

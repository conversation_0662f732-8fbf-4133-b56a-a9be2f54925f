// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'FreelanceHub';

  @override
  String get welcomeBack => 'Welcome Back';

  @override
  String get signInToContinue => 'Sign in to continue to FreelanceHub';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get signIn => 'Sign In';

  @override
  String get signUp => 'Sign Up';

  @override
  String get dontHaveAccount => 'Don\'t have an account?';

  @override
  String get alreadyHaveAccount => 'Already have an account?';

  @override
  String get createAccount => 'Create Account';

  @override
  String get joinFreelanceHub => 'Join FreelanceHub';

  @override
  String get createYourAccount => 'Create your account to get started';

  @override
  String get fullName => 'Full Name';

  @override
  String get iWantTo => 'I want to';

  @override
  String get hireFreelancers => 'Hire freelancers (Client)';

  @override
  String get offerServices => 'Offer services (Freelancer)';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get quickDemoLogin => 'Quick Demo Login';

  @override
  String get loginAsClient => 'Login as Client';

  @override
  String get loginAsFreelancer => 'Login as Freelancer';

  @override
  String get loginAsAdmin => 'Login as Admin';

  @override
  String get dashboard => 'Dashboard';

  @override
  String get requests => 'Requests';

  @override
  String get orders => 'Orders';

  @override
  String get messages => 'Messages';

  @override
  String get browse => 'Browse';

  @override
  String get offers => 'Offers';

  @override
  String get jobs => 'Jobs';

  @override
  String get payments => 'Payments';

  @override
  String get users => 'Users';

  @override
  String welcomeBackUser(String name) {
    return 'Welcome back, $name!';
  }

  @override
  String get readyToFindFreelancer =>
      'Ready to find the perfect freelancer for your project?';

  @override
  String get postNewRequest => 'Post New Request';

  @override
  String get activeOrders => 'Active Orders';

  @override
  String get totalRequests => 'Total Requests';

  @override
  String get recentRequests => 'Recent Requests';

  @override
  String get noRequestsYet => 'No requests yet';

  @override
  String get createFirstRequest =>
      'Create your first service request to get started';

  @override
  String welcomeFreelancer(String name) {
    return 'Welcome, $name!';
  }

  @override
  String get findNextOpportunity =>
      'Find your next opportunity and grow your freelance career.';

  @override
  String get browseJobs => 'Browse Jobs';

  @override
  String get profile => 'Profile';

  @override
  String get activeJobs => 'Active Jobs';

  @override
  String get pendingOffers => 'Pending Offers';

  @override
  String get latestOpportunities => 'Latest Opportunities';

  @override
  String get noOpportunitiesAvailable => 'No opportunities available';

  @override
  String get checkBackLater => 'Check back later for new project requests';

  @override
  String get recentOffers => 'Recent Offers';

  @override
  String get adminControlCenter => 'Admin Control Center';

  @override
  String get managePlatform =>
      'Manage users, verify payments, and oversee platform operations.';

  @override
  String get totalUsers => 'Total Users';

  @override
  String get totalOrders => 'Total Orders';

  @override
  String get revenue => 'Revenue';

  @override
  String get paymentVerificationRequired => 'Payment Verification Required';

  @override
  String paymentsAwaitingVerification(int count) {
    return '$count payments awaiting verification';
  }

  @override
  String get review => 'Review';

  @override
  String get quickActions => 'Quick Actions';

  @override
  String get verifyPayments => 'Verify Payments';

  @override
  String get manageUsers => 'Manage Users';

  @override
  String get orderManagement => 'Order Management';

  @override
  String get systemMessages => 'System Messages';

  @override
  String get recentActivity => 'Recent Activity';

  @override
  String get newUserRegistration => 'New user registration';

  @override
  String joinedAsFreelancer(String email) {
    return '$email joined as freelancer';
  }

  @override
  String get paymentSubmitted => 'Payment submitted';

  @override
  String get orderCompleted => 'Order Completed';

  @override
  String orderMarkedCompleted(String orderId) {
    return 'Order #$orderId marked as completed';
  }

  @override
  String minutesAgo(int count) {
    return '${count}m ago';
  }

  @override
  String hoursAgo(int count) {
    return '${count}h ago';
  }

  @override
  String daysAgo(int count) {
    return '${count}d ago';
  }

  @override
  String get logout => 'Logout';

  @override
  String get settings => 'Settings';

  @override
  String get language => 'Language';

  @override
  String get english => 'English';

  @override
  String get arabic => 'العربية';

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get lightMode => 'Light Mode';

  @override
  String get projectTitle => 'Project Title';

  @override
  String get whatDoYouNeed => 'What do you need done?';

  @override
  String get category => 'Category';

  @override
  String get projectDescription => 'Project Description';

  @override
  String get describeProject => 'Describe your project in detail...';

  @override
  String get budget => 'Budget (SAR)';

  @override
  String get enterBudget => 'Enter your budget (optional)';

  @override
  String get deadline => 'Deadline';

  @override
  String get selectDeadline => 'Select deadline';

  @override
  String get attachments => 'Attachments';

  @override
  String get addFiles => 'Add Files';

  @override
  String get canAttachFiles =>
      'You can attach relevant files, images, or documents';

  @override
  String get post => 'POST';

  @override
  String get webDevelopment => 'Web Development';

  @override
  String get mobileDevelopment => 'Mobile Development';

  @override
  String get graphicDesign => 'Graphic Design';

  @override
  String get contentWriting => 'Content Writing';

  @override
  String get digitalMarketing => 'Digital Marketing';

  @override
  String get dataEntry => 'Data Entry';

  @override
  String get translation => 'Translation';

  @override
  String get videoEditing => 'Video Editing';

  @override
  String get other => 'Other';

  @override
  String get pending => 'PENDING';

  @override
  String get inProgress => 'In Progress';

  @override
  String get completed => 'Completed';

  @override
  String get cancelled => 'Cancelled';

  @override
  String get normal => 'Normal';

  @override
  String get urgent => 'Urgent';

  @override
  String get vip => 'VIP';

  @override
  String get close => 'Close';

  @override
  String get viewDetails => 'View Details';

  @override
  String get edit => 'Edit';

  @override
  String postedTimeAgo(String time) {
    return 'Posted $time ago';
  }

  @override
  String get all => 'All';

  @override
  String get clients => 'Clients';

  @override
  String get freelancers => 'Freelancers';

  @override
  String get addNewUser => 'Add New User';

  @override
  String get fullNameLabel => 'Full Name';

  @override
  String get role => 'Role';

  @override
  String get cancel => 'Cancel';

  @override
  String get addUser => 'Add User';

  @override
  String get userAddedSuccessfully => 'User added successfully!';

  @override
  String get noName => 'No Name';

  @override
  String get joined => 'Joined';

  @override
  String get status => 'Status';

  @override
  String get verified => 'Verified';

  @override
  String get unverified => 'Unverified';

  @override
  String get rating => 'Rating';

  @override
  String get client => 'Client';

  @override
  String get freelancer => 'FREELANCER';

  @override
  String get admin => 'ADMIN';

  @override
  String get noPendingPayments => 'No pending payments';

  @override
  String get allPaymentsProcessed => 'All payments have been processed';

  @override
  String get clientLabel => 'Client';

  @override
  String get freelancerLabel => 'Freelancer';

  @override
  String get amount => 'Amount';

  @override
  String get paymentProof => 'Payment Proof';

  @override
  String get submitted => 'Submitted';

  @override
  String get view => 'View';

  @override
  String get reject => 'Reject';

  @override
  String get confirm => 'Confirm';

  @override
  String get confirmPayment => 'Confirm Payment';

  @override
  String confirmPaymentMessage(Object amount) {
    return 'Are you sure you want to confirm this payment of \$$amount?\n\nThis will notify the freelancer to start working.';
  }

  @override
  String get completedOrders => 'Completed Orders';

  @override
  String get cancelledOrders => 'Cancelled Orders';

  @override
  String get successRate => 'Success Rate';

  @override
  String get freelancerHub => 'Freelancer Hub';

  @override
  String get searchRequests => 'Search Requests';

  @override
  String get enterKeywords => 'Enter keywords...';

  @override
  String get search => 'Search';

  @override
  String get filterByPriority => 'Filter by Priority';

  @override
  String get clear => 'Clear';

  @override
  String get clearFilters => 'Clear Filters';

  @override
  String get priority => 'Priority';

  @override
  String get due => 'Due';

  @override
  String get posted => 'Posted';

  @override
  String get file => 'file';

  @override
  String get files => 'files';

  @override
  String get sendOffer => 'Send Offer';

  @override
  String get requestDetails => 'Request Details';

  @override
  String get clientInfo => 'Client Information';

  @override
  String get projectRequirements => 'Project Requirements';

  @override
  String get attachedFiles => 'Attached Files';

  @override
  String get download => 'Download';

  @override
  String get makeOffer => 'Make an Offer';

  @override
  String get yourOffer => 'Your Offer';

  @override
  String get offerAmount => 'Offer Amount (SAR)';

  @override
  String get enterAmount => 'Enter amount';

  @override
  String get deliveryTime => 'Delivery Time';

  @override
  String get days => 'days';

  @override
  String get selectDays => 'Select number of days';

  @override
  String get offerDescription => 'Offer Description';

  @override
  String get describeOffer =>
      'Describe your offer and experience in this field...';

  @override
  String get submitOffer => 'Submit Offer';

  @override
  String get offerSubmitted => 'Offer Submitted Successfully!';

  @override
  String get offerSubmittedMessage =>
      'You will be notified when the client responds to your offer.';

  @override
  String get ok => 'OK';

  @override
  String get myOffers => 'My Offers';

  @override
  String get noOffersYet => 'No offers yet';

  @override
  String get startBrowsing => 'Start browsing requests and making offers';

  @override
  String get browseRequests => 'Browse Requests';

  @override
  String get offerFor => 'Offer for';

  @override
  String get deliveryDays => 'delivery days';

  @override
  String get offerStatus => 'Offer Status';

  @override
  String get accepted => 'Accepted';

  @override
  String get rejected => 'Rejected';

  @override
  String get withdrawn => 'Withdrawn';

  @override
  String get viewOffer => 'View Offer';

  @override
  String get withdrawOffer => 'Withdraw Offer';

  @override
  String get confirmWithdraw => 'Confirm Withdrawal';

  @override
  String get withdrawOfferMessage =>
      'Are you sure you want to withdraw this offer? This action cannot be undone.';

  @override
  String get withdraw => 'Withdraw';

  @override
  String get offerWithdrawn => 'Offer Withdrawn';

  @override
  String get myJobs => 'My Jobs';

  @override
  String get noActiveJobs => 'No active jobs';

  @override
  String get completeProjects => 'Complete projects to build your reputation';

  @override
  String get jobTitle => 'Job Title';

  @override
  String get dueDate => 'Due Date';

  @override
  String get progress => 'Progress';

  @override
  String get startWorking => 'Start Working';

  @override
  String get deliverWork => 'Deliver Work';

  @override
  String get viewJob => 'View Job';

  @override
  String get jobDetails => 'Job Details';

  @override
  String get deliveryInstructions => 'Delivery Instructions';

  @override
  String get uploadDelivery => 'Upload Delivery';

  @override
  String get deliveryFiles => 'Delivery Files';

  @override
  String get addDeliveryFiles => 'Add delivery files';

  @override
  String get deliveryNotes => 'Delivery Notes';

  @override
  String get addNotes => 'Add optional notes...';

  @override
  String get submitDelivery => 'Submit Delivery';

  @override
  String get workDelivered => 'Work Delivered!';

  @override
  String get deliverySubmitted =>
      'Your delivery has been submitted successfully. You will be notified when the client reviews it.';

  @override
  String get paymentPending => 'Payment Pending';

  @override
  String get paymentConfirmed => 'Payment Confirmed';

  @override
  String get delivered => 'Delivered';

  @override
  String get chatWithClient => 'Chat with Client';

  @override
  String get notifications => 'Notifications';

  @override
  String get noNotifications => 'No notifications';

  @override
  String get noUnreadNotifications => 'No unread notifications';

  @override
  String get noReadNotifications => 'No read notifications';

  @override
  String get notificationsWillAppear =>
      'Your notifications will appear here when they arrive';

  @override
  String get unreadNotificationsWillAppear =>
      'New notifications will appear here';

  @override
  String get readNotificationsWillAppear =>
      'Notifications you\'ve read will appear here';

  @override
  String get markAllAsRead => 'Mark all as read';

  @override
  String get markAsRead => 'Mark as read';

  @override
  String get clearAll => 'Clear all';

  @override
  String get clearAllNotifications => 'Clear All Notifications';

  @override
  String get deleteNotification => 'Delete Notification';

  @override
  String get areYouSureDeleteNotification =>
      'Are you sure you want to delete this notification?';

  @override
  String get areYouSureClearAllNotifications =>
      'Are you sure you want to clear all notifications? This action cannot be undone.';

  @override
  String get delete => 'Delete';

  @override
  String get refresh => 'Refresh';

  @override
  String get searchNotifications => 'Search notifications...';

  @override
  String get unread => 'Unread';

  @override
  String get read => 'Read';

  @override
  String get unreadOnly => 'Unread only';

  @override
  String get announcements => 'Announcements';

  @override
  String get applications => 'Applications';

  @override
  String get now => 'Now';

  @override
  String weeksAgo(int count) {
    return '${count}w ago';
  }

  @override
  String get orderAccepted => 'Order Accepted';

  @override
  String get orderInProgress => 'Order In Progress';

  @override
  String get orderDelivered => 'Order Delivered';

  @override
  String get orderCancelled => 'Order Cancelled';

  @override
  String get newMessage => 'New Message';

  @override
  String get paymentReceived => 'Payment Received';

  @override
  String get paymentConfirmation => 'Payment Confirmation';

  @override
  String get systemMaintenance => 'System Maintenance';

  @override
  String get newOrderAvailable => 'New Order Available';

  @override
  String get chatMessages => 'Messages';

  @override
  String get noMessagesYet => 'No messages yet';

  @override
  String get typeMessage => 'Type a message...';

  @override
  String get sendMessage => 'Send';

  @override
  String get orderStatus => 'Order Status';

  @override
  String get payNow => 'Pay Now';

  @override
  String get viewDelivery => 'View Delivery';

  @override
  String get approveDelivery => 'Approve Delivery';

  @override
  String get requestRevision => 'Request Revision';

  @override
  String get cancelOrder => 'Cancel Order';

  @override
  String get markComplete => 'Mark Complete';

  @override
  String get revisionRequested => 'Revision Requested';

  @override
  String get revisionNotes => 'Revision Notes';

  @override
  String get enterRevisionNotes =>
      'Please describe what needs to be revised...';

  @override
  String get submitRevision => 'Submit Revision Request';

  @override
  String get revisionRequestSent => 'Revision request sent successfully';

  @override
  String get editing => 'Editing';

  @override
  String get orderCreated => 'Created';

  @override
  String get justNow => 'Just now';

  @override
  String timelineOrderCreated(
    String orderId,
    String serviceTitle,
    String amount,
  ) {
    return 'Order #$orderId created for \"$serviceTitle\" - \\\$$amount';
  }

  @override
  String timelineOrderAccepted(String orderId, String freelancerName) {
    return 'Order #$orderId accepted by $freelancerName';
  }

  @override
  String timelinePaymentConfirmed(String orderId, String amount) {
    return 'Payment confirmed for Order #$orderId - \\\$$amount';
  }

  @override
  String timelineWorkDelivered(String orderId) {
    return 'Work delivered for Order #$orderId';
  }

  @override
  String timelineOrderCompleted(String orderId) {
    return 'Order #$orderId completed successfully';
  }

  @override
  String timelineOrderCancelled(String orderId, String reason) {
    return 'Order #$orderId cancelled - $reason';
  }

  @override
  String timelineRevisionRequested(String orderId) {
    return 'Revision requested for Order #$orderId';
  }

  @override
  String timelineDeadlineExtended(String orderId) {
    return 'Deadline extended for Order #$orderId';
  }

  @override
  String timelineMilestoneReached(String milestoneName) {
    return 'Milestone reached: $milestoneName';
  }

  @override
  String get messageSent => 'Sent';

  @override
  String get messageDelivered => 'Delivered';

  @override
  String get messageRead => 'Read';

  @override
  String get attachFile => 'Attach File';

  @override
  String get takePhoto => 'Take Photo';

  @override
  String get recordVoice => 'Record Voice';

  @override
  String chatWith(String name) {
    return 'Chat with $name';
  }
}

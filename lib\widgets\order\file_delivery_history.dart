import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../models/order_model.dart';
import '../../providers/theme_provider.dart';
import '../../widgets/common/enhanced_widgets.dart';

class FileDeliveryHistory extends StatelessWidget {
  final OrderModel order;
  final bool isArabic;
  final bool isDark;

  const FileDeliveryHistory({super.key, required this.order, required this.isArabic, required this.isDark});

  @override
  Widget build(BuildContext context) {
    final fileHistory = _getFileHistory();

    if (fileHistory.isEmpty) {
      return _buildEmptyState();
    }

    return EnhancedCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.history, color: ThemeProvider.primaryBlue, size: 24),
              const SizedBox(width: 12),
              Text(
                isArabic ? 'سجل تسليم الملفات' : 'File Delivery History',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          ...fileHistory.map((file) => _buildFileHistoryItem(context, file)),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return EnhancedCard(
      padding: const EdgeInsets.all(40),
      child: Center(
        child: Column(
          children: [
            Icon(Icons.folder_open, size: 60, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              isArabic ? 'لا توجد ملفات مسلمة بعد' : 'No files delivered yet',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              isArabic ? 'ستظهر الملفات المسلمة هنا عند توفرها' : 'Delivered files will appear here when available',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 14, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[500]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFileHistoryItem(BuildContext context, FileHistoryItem file) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: (isDark ? ThemeProvider.darkCardBackground : Colors.grey[50]),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: isDark ? Colors.grey[700]! : Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // File header
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: _getFileTypeColor(file.type).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(_getFileTypeIcon(file.type), color: _getFileTypeColor(file.type), size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      file.name,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _formatFileSize(file.size),
                      style: TextStyle(
                        fontSize: 12,
                        color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              _buildSenderBadge(file.sender),
            ],
          ),

          const SizedBox(height: 12),

          // File description
          if (file.description.isNotEmpty) ...[
            Text(
              file.description,
              style: TextStyle(fontSize: 14, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[700]),
            ),
            const SizedBox(height: 12),
          ],

          // Timestamp and actions
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(Icons.access_time, size: 16, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[500]),
                  const SizedBox(width: 4),
                  Text(
                    _formatTimestamp(file.timestamp),
                    style: TextStyle(fontSize: 12, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[500]),
                  ),
                ],
              ),
              Row(
                children: [
                  if (file.url.isNotEmpty) ...[
                    IconButton(
                      onPressed: () => _downloadFile(file.url),
                      icon: const Icon(Icons.download, size: 20),
                      style: IconButton.styleFrom(
                        backgroundColor: ThemeProvider.primaryBlue.withValues(alpha: 0.1),
                        foregroundColor: ThemeProvider.primaryBlue,
                      ),
                    ),
                    const SizedBox(width: 8),
                  ],
                  IconButton(
                    onPressed: () => _previewFile(context, file),
                    icon: const Icon(Icons.visibility, size: 20),
                    style: IconButton.styleFrom(
                      backgroundColor: Colors.grey.withValues(alpha: 0.1),
                      foregroundColor: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSenderBadge(FileSender sender) {
    Color color;
    String text;
    IconData icon;

    switch (sender) {
      case FileSender.client:
        color = ThemeProvider.primaryBlue;
        text = isArabic ? 'العميل' : 'Client';
        icon = Icons.person;
        break;
      case FileSender.freelancer:
        color = ThemeProvider.successGreen;
        text = isArabic ? 'المستقل' : 'Freelancer';
        icon = Icons.work;
        break;
      case FileSender.admin:
        color = ThemeProvider.warningOrange;
        text = isArabic ? 'الإدارة' : 'Admin';
        icon = Icons.admin_panel_settings;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          Text(text, style: TextStyle(fontSize: 10, fontWeight: FontWeight.bold, color: color)),
        ],
      ),
    );
  }

  List<FileHistoryItem> _getFileHistory() {
    List<FileHistoryItem> history = [];

    // Add delivery files if available
    if (order.deliveryUrl != null) {
      history.add(
        FileHistoryItem(
          name: isArabic ? 'ملف التسليم الرئيسي' : 'Main Delivery File',
          description: order.deliveryNotes ?? (isArabic ? 'ملف التسليم النهائي' : 'Final delivery file'),
          url: order.deliveryUrl!,
          type: FileType.document,
          size: 2048000, // 2MB placeholder
          sender: FileSender.freelancer,
          timestamp: order.deliveryDate ?? order.updatedAt ?? order.createdAt,
        ),
      );
    }

    // Add demo files for demonstration
    if (order.status == OrderStatus.delivered || order.status == OrderStatus.completed) {
      history.addAll([
        FileHistoryItem(
          name: isArabic ? 'متطلبات المشروع.pdf' : 'project_requirements.pdf',
          description: isArabic ? 'الملف الأولي للمتطلبات' : 'Initial requirements document',
          url: 'https://example.com/requirements.pdf',
          type: FileType.pdf,
          size: 1024000, // 1MB
          sender: FileSender.client,
          timestamp: order.createdAt,
        ),
        FileHistoryItem(
          name: isArabic ? 'تصميم أولي.zip' : 'initial_design.zip',
          description: isArabic ? 'التصميم الأولي للمراجعة' : 'Initial design for review',
          url: 'https://example.com/design.zip',
          type: FileType.archive,
          size: 5120000, // 5MB
          sender: FileSender.freelancer,
          timestamp: order.createdAt.add(const Duration(days: 2)),
        ),
      ]);
    }

    // Sort by timestamp (newest first)
    history.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return history;
  }

  Color _getFileTypeColor(FileType type) {
    switch (type) {
      case FileType.document:
        return ThemeProvider.primaryBlue;
      case FileType.pdf:
        return Colors.red;
      case FileType.image:
        return Colors.green;
      case FileType.archive:
        return Colors.orange;
      case FileType.video:
        return Colors.purple;
      case FileType.other:
        return Colors.grey;
    }
  }

  IconData _getFileTypeIcon(FileType type) {
    switch (type) {
      case FileType.document:
        return Icons.description;
      case FileType.pdf:
        return Icons.picture_as_pdf;
      case FileType.image:
        return Icons.image;
      case FileType.archive:
        return Icons.archive;
      case FileType.video:
        return Icons.video_file;
      case FileType.other:
        return Icons.insert_drive_file;
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return isArabic ? 'منذ ${difference.inDays} يوم' : '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return isArabic ? 'منذ ${difference.inHours} ساعة' : '${difference.inHours}h ago';
    } else {
      return isArabic ? 'منذ ${difference.inMinutes} دقيقة' : '${difference.inMinutes}m ago';
    }
  }

  Future<void> _downloadFile(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      // Handle error
    }
  }

  void _previewFile(BuildContext context, FileHistoryItem file) {
    // Handle different file types for preview
    switch (file.type) {
      case FileType.image:
        _showImagePreview(context, file);
        break;
      case FileType.pdf:
        _showPdfPreview(context, file);
        break;
      case FileType.document:
        _showDocumentPreview(context, file);
        break;
      case FileType.video:
        _showVideoPreview(context, file);
        break;
      case FileType.archive:
        _showArchivePreview(context, file);
        break;
      case FileType.other:
        _showGenericPreview(context, file);
        break;
    }
  }

  void _showImagePreview(BuildContext context, FileHistoryItem file) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder:
          (context) => Dialog(
            backgroundColor: Colors.transparent,
            child: Stack(
              children: [
                // Background tap to close
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Container(color: Colors.black54, width: double.infinity, height: double.infinity),
                ),
                // Image preview
                Center(
                  child: Container(
                    margin: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: isDark ? ThemeProvider.darkCardBackground : Colors.white,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Header
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: isDark ? Colors.grey[800] : Colors.grey[100],
                            borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.image, color: _getFileTypeColor(file.type)),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  file.name,
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                                  ),
                                ),
                              ),
                              IconButton(onPressed: () => Navigator.pop(context), icon: const Icon(Icons.close)),
                            ],
                          ),
                        ),
                        // Image content
                        Container(
                          constraints: BoxConstraints(
                            maxHeight: MediaQuery.of(context).size.height * 0.6,
                            maxWidth: MediaQuery.of(context).size.width * 0.9,
                          ),
                          child:
                              file.url.isNotEmpty
                                  ? Image.network(
                                    file.url,
                                    fit: BoxFit.contain,
                                    loadingBuilder: (context, child, loadingProgress) {
                                      if (loadingProgress == null) return child;
                                      return SizedBox(
                                        height: 200,
                                        child: Center(
                                          child: CircularProgressIndicator(
                                            value:
                                                loadingProgress.expectedTotalBytes != null
                                                    ? loadingProgress.cumulativeBytesLoaded /
                                                        loadingProgress.expectedTotalBytes!
                                                    : null,
                                          ),
                                        ),
                                      );
                                    },
                                    errorBuilder:
                                        (context, error, stackTrace) => SizedBox(
                                          height: 200,
                                          child: Column(
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            children: [
                                              const Icon(Icons.error, size: 48, color: Colors.red),
                                              const SizedBox(height: 8),
                                              Text(
                                                isArabic ? 'خطأ في تحميل الصورة' : 'Error loading image',
                                                style: const TextStyle(color: Colors.red),
                                              ),
                                            ],
                                          ),
                                        ),
                                  )
                                  : SizedBox(
                                    height: 200,
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        const Icon(Icons.image_not_supported, size: 48, color: Colors.grey),
                                        const SizedBox(height: 8),
                                        Text(
                                          isArabic ? 'لا يوجد رابط للصورة' : 'No image URL available',
                                          style: const TextStyle(color: Colors.grey),
                                        ),
                                      ],
                                    ),
                                  ),
                        ),
                        // Actions
                        Container(
                          padding: const EdgeInsets.all(16),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              if (file.url.isNotEmpty)
                                ElevatedButton.icon(
                                  onPressed: () {
                                    Navigator.pop(context);
                                    _downloadFile(file.url);
                                  },
                                  icon: const Icon(Icons.download),
                                  label: Text(isArabic ? 'تحميل' : 'Download'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: ThemeProvider.primaryBlue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  void _showPdfPreview(BuildContext context, FileHistoryItem file) {
    _showGenericFilePreview(
      context,
      file,
      Icons.picture_as_pdf,
      isArabic ? 'معاينة PDF' : 'PDF Preview',
      isArabic
          ? 'لا يمكن معاينة ملفات PDF في التطبيق. يرجى تحميل الملف لعرضه.'
          : 'PDF files cannot be previewed in-app. Please download to view.',
    );
  }

  void _showDocumentPreview(BuildContext context, FileHistoryItem file) {
    _showGenericFilePreview(
      context,
      file,
      Icons.description,
      isArabic ? 'معاينة المستند' : 'Document Preview',
      isArabic
          ? 'لا يمكن معاينة المستندات في التطبيق. يرجى تحميل الملف لعرضه.'
          : 'Documents cannot be previewed in-app. Please download to view.',
    );
  }

  void _showVideoPreview(BuildContext context, FileHistoryItem file) {
    _showGenericFilePreview(
      context,
      file,
      Icons.video_file,
      isArabic ? 'معاينة الفيديو' : 'Video Preview',
      isArabic
          ? 'لا يمكن معاينة ملفات الفيديو في التطبيق. يرجى تحميل الملف لعرضه.'
          : 'Video files cannot be previewed in-app. Please download to view.',
    );
  }

  void _showArchivePreview(BuildContext context, FileHistoryItem file) {
    _showGenericFilePreview(
      context,
      file,
      Icons.archive,
      isArabic ? 'معاينة الأرشيف' : 'Archive Preview',
      isArabic
          ? 'لا يمكن معاينة الملفات المضغوطة في التطبيق. يرجى تحميل الملف لاستخراجه.'
          : 'Archive files cannot be previewed in-app. Please download to extract.',
    );
  }

  void _showGenericPreview(BuildContext context, FileHistoryItem file) {
    _showGenericFilePreview(
      context,
      file,
      Icons.insert_drive_file,
      isArabic ? 'معاينة الملف' : 'File Preview',
      isArabic
          ? 'لا يمكن معاينة هذا النوع من الملفات في التطبيق. يرجى تحميل الملف لعرضه.'
          : 'This file type cannot be previewed in-app. Please download to view.',
    );
  }

  void _showGenericFilePreview(
    BuildContext context,
    FileHistoryItem file,
    IconData icon,
    String title,
    String message,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
            title: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: _getFileTypeColor(file.type).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: _getFileTypeColor(file.type), size: 20),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                    ),
                  ),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  file.name,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _formatFileSize(file.size),
                  style: TextStyle(fontSize: 12, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
                ),
                if (file.description.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Text(
                    file.description,
                    style: TextStyle(fontSize: 14, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[700]),
                  ),
                ],
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: (isDark ? Colors.grey[800] : Colors.grey[100]),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 20,
                        color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          message,
                          style: TextStyle(
                            fontSize: 13,
                            color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(onPressed: () => Navigator.pop(context), child: Text(isArabic ? 'إغلاق' : 'Close')),
              if (file.url.isNotEmpty)
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.pop(context);
                    _downloadFile(file.url);
                  },
                  icon: const Icon(Icons.download),
                  label: Text(isArabic ? 'تحميل' : 'Download'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ThemeProvider.primaryBlue,
                    foregroundColor: Colors.white,
                  ),
                ),
            ],
          ),
    );
  }
}

class FileHistoryItem {
  final String name;
  final String description;
  final String url;
  final FileType type;
  final int size;
  final FileSender sender;
  final DateTime timestamp;

  FileHistoryItem({
    required this.name,
    required this.description,
    required this.url,
    required this.type,
    required this.size,
    required this.sender,
    required this.timestamp,
  });
}

enum FileType { document, pdf, image, archive, video, other }

enum FileSender { client, freelancer, admin }

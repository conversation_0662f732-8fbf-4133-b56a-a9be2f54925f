import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/language_provider.dart';
import '../../providers/theme_provider.dart';
import '../../models/order_model.dart';

class MinimalChatTopBar extends StatelessWidget {
  final OrderModel? order;
  final bool isFreelancer;
  final VoidCallback? onConfirmSubmission;
  final VoidCallback? onConfirmPayment;
  final VoidCallback? onConfirmDelivery;

  const MinimalChatTopBar({
    super.key,
    this.order,
    required this.isFreelancer,
    this.onConfirmSubmission,
    this.onConfirmPayment,
    this.onConfirmDelivery,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isArabic = languageProvider.isArabic;

    // Don't show if no order
    if (order == null) return const SizedBox.shrink();

    // Get buttons based on role and order status
    final buttons = _getActionButtons(isArabic);

    // Don't show if no buttons
    if (buttons.isEmpty) return const SizedBox.shrink();

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Container(
        height: 56,
        width: double.infinity,
        decoration: BoxDecoration(
          color:
              themeProvider.isDarkMode
                  ? Colors.grey[900]!.withValues(alpha: 0.95)
                  : Colors.white.withValues(alpha: 0.95),
          border: Border(
            bottom: BorderSide(color: themeProvider.isDarkMode ? Colors.grey[700]! : Colors.grey[200]!, width: 1),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Row(
            children:
                buttons.length == 1
                    ? [const SizedBox(width: 16), Expanded(flex: 3, child: buttons.first), const SizedBox(width: 16)]
                    : buttons.map((button) => Expanded(child: button)).toList(),
          ),
        ),
      ),
    );
  }

  List<Widget> _getActionButtons(bool isArabic) {
    List<Widget> buttons = [];

    if (isFreelancer) {
      // Freelancer Interface: Show different states before and after confirmation
      if (order!.status == OrderStatus.paymentPending ||
          order!.status == OrderStatus.paymentConfirmed ||
          order!.status == OrderStatus.inProgress) {
        // BEFORE confirmation - Active button
        buttons.add(
          _buildActionButton(
            label: isArabic ? 'تأكيد التسليم' : 'Confirm Submission',
            icon: Icons.check_circle,
            color: Colors.green,
            onTap: onConfirmSubmission,
          ),
        );
      } else if (order!.status == OrderStatus.delivered) {
        // AFTER confirmation - Delivered state (disabled/different styling)
        buttons.add(
          _buildActionButton(
            label: isArabic ? 'تم التسليم' : 'Delivered',
            icon: Icons.check_circle,
            color: Colors.grey,
            onTap: null, // Disabled
          ),
        );
      }
    } else {
      // Client Interface: Show payment and delivery buttons

      // Confirm Payment button (if not paid yet)
      if (order!.status == OrderStatus.created || order!.status == OrderStatus.paymentPending) {
        buttons.add(
          _buildActionButton(
            label: isArabic ? 'تأكيد الدفع' : 'Confirm Payment',
            icon: Icons.payment,
            color: Colors.green,
            onTap: onConfirmPayment,
          ),
        );
      }

      // Confirm Delivery button (after freelancer submits)
      if (order!.status == OrderStatus.delivered) {
        buttons.add(
          _buildActionButton(
            label: isArabic ? 'تأكيد التسليم' : 'Confirm Delivery',
            icon: Icons.check_circle,
            color: Colors.blue,
            onTap: onConfirmDelivery,
          ),
        );
      }
    }

    return buttons;
  }

  Widget _buildActionButton({
    required String label,
    required IconData icon,
    required Color color,
    required VoidCallback? onTap,
  }) {
    final isDisabled = onTap == null;

    if (isDisabled) {
      // AFTER confirmation - Match the grey button style above
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Container(
          height: 44,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(22), // More rounded like the grey button above
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.check_circle, color: Colors.grey[600], size: 18),
              const SizedBox(width: 8),
              Text(
                label,
                style: TextStyle(color: Colors.grey[600], fontSize: 14, fontWeight: FontWeight.w500),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    } else {
      // BEFORE confirmation - Active green button
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(8),
            child: Container(
              height: 44,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(8), // Less rounded for active state
                boxShadow: [BoxShadow(color: color.withValues(alpha: 0.3), blurRadius: 4, offset: const Offset(0, 2))],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(icon, color: Colors.white, size: 18),
                  const SizedBox(width: 8),
                  Text(
                    label,
                    style: const TextStyle(color: Colors.white, fontSize: 14, fontWeight: FontWeight.w600),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    }
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'utils/app_localizations.dart';
import 'config/supabase_config.dart';
import 'providers/demo_auth_provider.dart';
import 'providers/theme_provider.dart';
import 'providers/language_provider.dart';
import 'providers/notification_provider.dart';
import 'providers/chat_provider.dart';
import 'screens/auth/login_screen.dart';
import 'screens/auth/register_screen.dart';
import 'screens/client/client_dashboard.dart';
import 'screens/freelancer/freelancer_main_screen.dart';
import 'screens/admin/admin_dashboard.dart';
import 'screens/splash_screen.dart';
import 'services/notification_integration_service.dart';
import 'models/user_model.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تعطيل جميع إعدادات Debug
  if (kDebugMode) {
    debugPaintSizeEnabled = false;
    debugRepaintRainbowEnabled = false;
    debugRepaintTextRainbowEnabled = false;
    debugPrintRebuildDirtyWidgets = false;
    debugPrintBuildScope = false;
    debugPrintScheduleBuildForStacks = false;
    debugPrintGlobalKeyedWidgetLifecycle = false;
    debugProfileBuildsEnabled = false;
    debugEnhanceBuildTimelineArguments = false;
    debugDisableShadows = false;
    debugPrintMarkNeedsLayoutStacks = false;
    debugPrintMarkNeedsPaintStacks = false;
  }

  await SupabaseConfig.initialize();

  // Initialize Smart Notification System
  await NotificationIntegrationService.initialize();

  runApp(const TasklyApp());
}

class TasklyApp extends StatelessWidget {
  const TasklyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => DemoAuthProvider()),
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => LanguageProvider()),
        ChangeNotifierProvider(create: (_) => NotificationProvider()),
        ChangeNotifierProvider(create: (_) => ChatProvider()),
      ],
      child: Consumer3<DemoAuthProvider, ThemeProvider, LanguageProvider>(
        builder: (context, authProvider, themeProvider, languageProvider, child) {
          return MaterialApp.router(
            title: 'Taskly',
            debugShowCheckedModeBanner: false,
            showPerformanceOverlay: false,
            showSemanticsDebugger: false,
            checkerboardRasterCacheImages: false,
            checkerboardOffscreenLayers: false,
            theme: themeProvider.themeData,
            locale: languageProvider.locale,
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [Locale('en'), Locale('ar')],
            routerConfig: GoRouter(
              initialLocation: '/splash',
              redirect: (context, state) {
                final authProvider = Provider.of<DemoAuthProvider>(context, listen: false);
                final isAuthenticated = authProvider.isAuthenticated;
                final userProfile = authProvider.userProfile;
                final currentLocation = state.uri.toString();

                // Allow splash screen to show initially
                if (currentLocation == '/splash') {
                  return null;
                }

                // If not authenticated, redirect to login (except for register page)
                if (!isAuthenticated) {
                  if (currentLocation == '/register') {
                    return null;
                  }
                  return '/login';
                }

                // If authenticated but no profile, redirect to login
                if (userProfile == null) {
                  return '/login';
                }

                // If authenticated and trying to access auth pages, redirect to dashboard
                if (currentLocation == '/login' || currentLocation == '/register' || currentLocation == '/splash') {
                  switch (userProfile.role) {
                    case UserRole.client:
                      return '/client-dashboard';
                    case UserRole.freelancer:
                      return '/freelancer-dashboard';
                    case UserRole.admin:
                      return '/admin-dashboard';
                  }
                }

                return null;
              },
              routes: [
                GoRoute(path: '/splash', builder: (context, state) => const SplashScreen()),
                GoRoute(path: '/login', builder: (context, state) => const LoginScreen()),
                GoRoute(path: '/register', builder: (context, state) => const RegisterScreen()),
                GoRoute(path: '/client-dashboard', builder: (context, state) => const ClientDashboard()),
                GoRoute(path: '/freelancer-dashboard', builder: (context, state) => const FreelancerMainScreen()),
                GoRoute(path: '/admin-dashboard', builder: (context, state) => const AdminDashboard()),
              ],
            ),
          );
        },
      ),
    );
  }
}

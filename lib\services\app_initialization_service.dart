import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/chat_provider.dart';
import '../providers/notification_provider.dart';
import '../models/user_model.dart';
import 'customer_service_chat_service.dart';

/// Service to handle app initialization and provider coordination
class AppInitializationService {
  /// Initialize providers when user logs in
  static Future<void> initializeUserSession(BuildContext context, String userId, {UserRole? userRole}) async {
    try {
      // Get providers
      final chatProvider = Provider.of<ChatProvider>(context, listen: false);
      final notificationProvider = Provider.of<NotificationProvider>(context, listen: false);

      // Initialize notification provider first
      await notificationProvider.setUser(userId);

      // Initialize chat provider
      await chatProvider.setUser(userId);

      // If user is a client, ensure customer service chat exists
      if (userRole == UserRole.client) {
        await _initializeCustomerServiceChat(userId);
      }

      print('User session initialized successfully for user: $userId');
    } catch (e) {
      print('Failed to initialize user session: $e');
    }
  }

  /// Initialize customer service chat for clients
  static Future<void> _initializeCustomerServiceChat(String clientId) async {
    try {
      print('🔄 Initializing customer service chat for client: $clientId');
      await CustomerServiceChatService.ensureCustomerServiceChat(clientId);
      print('✅ Customer service chat initialized for client: $clientId');
    } catch (e) {
      print('❌ Failed to initialize customer service chat: $e');
    }
  }

  /// Clean up providers when user logs out
  static Future<void> cleanupUserSession(BuildContext context) async {
    try {
      // Get providers
      final chatProvider = Provider.of<ChatProvider>(context, listen: false);
      final notificationProvider = Provider.of<NotificationProvider>(context, listen: false);

      // Clear chat provider
      await chatProvider.setUser('');

      // Clear notification provider
      await notificationProvider.setUser('');

      print('User session cleaned up successfully');
    } catch (e) {
      print('Failed to cleanup user session: $e');
    }
  }

  /// Initialize app-wide services
  static Future<void> initializeApp() async {
    try {
      // Initialize any global services here
      print('App initialized successfully');
    } catch (e) {
      print('Failed to initialize app: $e');
    }
  }
}

import '../config/supabase_config.dart';
import '../models/chat_model.dart';
import '../models/message_model.dart';
import '../models/user_model.dart';
import 'notification_service.dart';

class ChatService {
  /// Get user profile by ID from Supabase profiles table
  static Future<UserModel?> _getUserProfile(String userId) async {
    try {
      final response = await SupabaseConfig.client.from('profiles').select().eq('id', userId).single();

      return UserModel.fromJson(response);
    } catch (e) {
      // Return null if user not found or error occurs
      return null;
    }
  }

  static Future<List<ChatModel>> getChats(String userId) async {
    try {
      final response = await SupabaseConfig.client
          .from('chats')
          .select()
          .or('client_id.eq.$userId,freelancer_id.eq.$userId')
          .order('updated_at', ascending: false);

      return response.map<ChatModel>((json) => ChatModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to fetch chats: $e');
    }
  }

  static Future<ChatModel> createChat(ChatModel chat) async {
    try {
      final response = await SupabaseConfig.client.from('chats').insert(chat.toJson()).select().single();

      return ChatModel.fromJson(response);
    } catch (e) {
      throw Exception('Failed to create chat: $e');
    }
  }

  static Future<ChatModel?> getChatByRequestId(String requestId) async {
    try {
      final response = await SupabaseConfig.client.from('chats').select().eq('request_id', requestId).single();

      return ChatModel.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  static Future<ChatModel?> getChatById(String chatId) async {
    try {
      final response = await SupabaseConfig.client.from('chats').select().eq('id', chatId).single();

      return ChatModel.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  static Future<List<MessageModel>> getMessages(String chatId) async {
    try {
      final response = await SupabaseConfig.client
          .from('messages')
          .select()
          .eq('chat_id', chatId)
          .order('created_at', ascending: true);

      return response.map<MessageModel>((json) => MessageModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to fetch messages: $e');
    }
  }

  static Future<MessageModel> sendMessage(MessageModel message) async {
    try {
      final response = await SupabaseConfig.client.from('messages').insert(message.toJson()).select().single();

      // Update chat with last message
      await SupabaseConfig.client
          .from('chats')
          .update({
            'last_message': message.content,
            'last_message_time': message.createdAt.toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', message.chatId);

      // Create notification for message recipient
      try {
        final chat = await getChatById(message.chatId);
        if (chat != null) {
          final recipientId = chat.clientId == message.senderId ? chat.freelancerId : chat.clientId;

          // Get sender profile to get their name
          final senderProfile = await _getUserProfile(message.senderId);
          final senderName = senderProfile?.fullName ?? 'User';

          await NotificationService.notifyNewMessage(
            userId: recipientId,
            chatId: message.chatId,
            senderName: senderName,
            messagePreview: message.content.length > 50 ? '${message.content.substring(0, 50)}...' : message.content,
          );
        }
      } catch (e) {
        // Don't fail message sending if notification fails
        print('Failed to create message notification: $e');
      }

      return MessageModel.fromJson(response);
    } catch (e) {
      throw Exception('Failed to send message: $e');
    }
  }

  static Future<void> markMessagesAsRead(String chatId, String userId) async {
    try {
      await SupabaseConfig.client
          .from('messages')
          .update({'is_read': true})
          .eq('chat_id', chatId)
          .neq('sender_id', userId);
    } catch (e) {
      throw Exception('Failed to mark messages as read: $e');
    }
  }

  static Stream<List<MessageModel>> subscribeToMessages(String chatId) {
    return SupabaseConfig.client
        .from('messages')
        .stream(primaryKey: ['id'])
        .eq('chat_id', chatId)
        .order('created_at')
        .map((data) => data.map<MessageModel>((json) => MessageModel.fromJson(json)).toList());
  }

  static Stream<List<ChatModel>> subscribeToChats(String userId) {
    return SupabaseConfig.client
        .from('chats')
        .stream(primaryKey: ['id'])
        .eq('client_id', userId)
        .order('updated_at', ascending: false)
        .map((data) => data.map<ChatModel>((json) => ChatModel.fromJson(json)).toList());
  }

  static Future<MessageModel> sendSystemMessage(String chatId, String content) async {
    try {
      final message = MessageModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        chatId: chatId,
        senderId: 'system',
        content: content,
        type: MessageType.system,
        createdAt: DateTime.now(),
      );

      final response = await SupabaseConfig.client.from('messages').insert(message.toJson()).select().single();

      return MessageModel.fromJson(response);
    } catch (e) {
      throw Exception('Failed to send system message: $e');
    }
  }

  static Future<int> getUnreadCount(String chatId, String userId) async {
    try {
      final response = await SupabaseConfig.client
          .from('messages')
          .select('id')
          .eq('chat_id', chatId)
          .neq('sender_id', userId)
          .eq('is_read', false);

      return response.length;
    } catch (e) {
      return 0;
    }
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/language_provider.dart';
import '../../providers/theme_provider.dart';
import '../../models/order_model.dart';
import '../../services/order_service.dart';
import '../../services/auto_reminder_service.dart';
import '../../widgets/chat/delivery_confirmation_widget.dart';
import '../../widgets/chat/receipt_confirmation_widget.dart';

class DeliveryWorkflowDemo extends StatefulWidget {
  const DeliveryWorkflowDemo({super.key});

  @override
  State<DeliveryWorkflowDemo> createState() => _DeliveryWorkflowDemoState();
}

class _DeliveryWorkflowDemoState extends State<DeliveryWorkflowDemo> {
  OrderModel? _demoOrder;
  bool _isLoading = false;
  String _currentStep = 'Payment Confirmed';

  @override
  void initState() {
    super.initState();
    _createDemoOrder();
  }

  void _createDemoOrder() {
    setState(() {
      _demoOrder = OrderModel(
        id: 'demo_delivery_order',
        requestId: 'demo_request',
        clientId: 'demo_client',
        freelancerId: 'demo_freelancer',
        offerId: 'demo_offer',
        amount: 1500.0,
        status: OrderStatus.paymentConfirmed,
        paymentStatus: PaymentStatus.confirmed,
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        updatedAt: DateTime.now(),
      );
    });
  }

  Future<void> _simulateDeliveryConfirmation() async {
    if (_demoOrder == null) return;

    setState(() {
      _isLoading = true;
      _currentStep = 'Confirming Delivery...';
    });

    try {
      // Simulate delivery confirmation
      await Future.delayed(const Duration(seconds: 2));
      
      final updatedOrder = await OrderService.confirmDelivery(
        orderId: _demoOrder!.id,
        fileUrls: ['https://example.com/deliverable.zip'],
        notes: 'Work completed as per requirements. Please review.',
      );

      setState(() {
        _demoOrder = updatedOrder;
        _currentStep = 'Work Delivered - Awaiting Client Confirmation';
        _isLoading = false;
      });

      _showSuccessMessage('Delivery confirmed successfully!');
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorMessage('Error confirming delivery: $e');
    }
  }

  Future<void> _simulateReceiptConfirmation() async {
    if (_demoOrder == null) return;

    setState(() {
      _isLoading = true;
      _currentStep = 'Confirming Receipt...';
    });

    try {
      // Simulate receipt confirmation
      await Future.delayed(const Duration(seconds: 2));
      
      final completedOrder = await OrderService.confirmReceipt(
        orderId: _demoOrder!.id,
        rating: 5.0,
        review: 'Excellent work! Delivered on time and exceeded expectations.',
      );

      setState(() {
        _demoOrder = completedOrder;
        _currentStep = 'Order Completed - Payment Released';
        _isLoading = false;
      });

      _showSuccessMessage('Receipt confirmed and payment released!');
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorMessage('Error confirming receipt: $e');
    }
  }

  Future<void> _simulateAutoReminder() async {
    if (_demoOrder == null) return;

    setState(() {
      _isLoading = true;
      _currentStep = 'Sending Reminder...';
    });

    try {
      await AutoReminderService.sendManualReminder(_demoOrder!.id);
      
      setState(() {
        _isLoading = false;
        _currentStep = 'Reminder Sent to Client';
      });

      _showSuccessMessage('Reminder sent to client successfully!');
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorMessage('Error sending reminder: $e');
    }
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _resetDemo() {
    _createDemoOrder();
    setState(() {
      _currentStep = 'Payment Confirmed';
    });
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isArabic = languageProvider.isArabic;

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        appBar: AppBar(
          title: Text(isArabic ? 'تجربة سير عمل التسليم' : 'Delivery Workflow Demo'),
          actions: [
            IconButton(
              onPressed: _resetDemo,
              icon: const Icon(Icons.refresh),
              tooltip: isArabic ? 'إعادة تعيين' : 'Reset',
            ),
          ],
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Current Step Indicator
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: themeProvider.isDarkMode ? Colors.grey[800] : Colors.blue[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.blue.withValues(alpha: 0.3),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isArabic ? 'الخطوة الحالية' : 'Current Step',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.blue[700],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        if (_isLoading) ...[
                          const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          const SizedBox(width: 8),
                        ],
                        Expanded(
                          child: Text(
                            _currentStep,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Order Information
              if (_demoOrder != null) ...[
                Text(
                  isArabic ? 'معلومات الطلب' : 'Order Information',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: themeProvider.isDarkMode ? Colors.grey[800] : Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.grey.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildInfoRow(
                        isArabic ? 'رقم الطلب:' : 'Order ID:',
                        _demoOrder!.id,
                      ),
                      _buildInfoRow(
                        isArabic ? 'المبلغ:' : 'Amount:',
                        '\$${_demoOrder!.amount.toStringAsFixed(2)}',
                      ),
                      _buildInfoRow(
                        isArabic ? 'الحالة:' : 'Status:',
                        _getStatusText(_demoOrder!.status, isArabic),
                      ),
                      _buildInfoRow(
                        isArabic ? 'حالة الدفع:' : 'Payment Status:',
                        _getPaymentStatusText(_demoOrder!.paymentStatus, isArabic),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
              ],

              // Delivery Widgets Demo
              Text(
                isArabic ? 'واجهات التسليم' : 'Delivery Widgets',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),

              // Show appropriate widget based on order status
              if (_demoOrder != null) ...[
                if (_demoOrder!.status == OrderStatus.paymentConfirmed)
                  DeliveryConfirmationWidget(
                    order: _demoOrder!,
                    onDeliveryConfirmed: () => _simulateDeliveryConfirmation(),
                  ),
                if (_demoOrder!.status == OrderStatus.delivered)
                  ReceiptConfirmationWidget(
                    order: _demoOrder!,
                    onReceiptConfirmed: () => _simulateReceiptConfirmation(),
                  ),
                if (_demoOrder!.status == OrderStatus.completed)
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.green[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.green.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Column(
                      children: [
                        Icon(
                          Icons.check_circle,
                          color: Colors.green[600],
                          size: 48,
                        ),
                        const SizedBox(height: 12),
                        Text(
                          isArabic ? 'تم إكمال الطلب بنجاح!' : 'Order Completed Successfully!',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.green[700],
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          isArabic 
                            ? 'تم إطلاق الدفعة للمستقل وإكمال العملية بنجاح.'
                            : 'Payment has been released to the freelancer and the process is complete.',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.green[600],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
              ],
              const SizedBox(height: 24),

              // Action Buttons
              Text(
                isArabic ? 'إجراءات التجربة' : 'Demo Actions',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 12,
                runSpacing: 12,
                children: [
                  if (_demoOrder?.status == OrderStatus.delivered)
                    ElevatedButton.icon(
                      onPressed: _isLoading ? null : _simulateAutoReminder,
                      icon: const Icon(Icons.notifications, size: 16),
                      label: Text(isArabic ? 'إرسال تذكير' : 'Send Reminder'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ElevatedButton.icon(
                    onPressed: _resetDemo,
                    icon: const Icon(Icons.refresh, size: 16),
                    label: Text(isArabic ? 'إعادة تعيين التجربة' : 'Reset Demo'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[600],
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getStatusText(OrderStatus status, bool isArabic) {
    if (isArabic) {
      switch (status) {
        case OrderStatus.paymentConfirmed:
          return 'تم تأكيد الدفع';
        case OrderStatus.delivered:
          return 'تم التسليم';
        case OrderStatus.completed:
          return 'مكتمل';
        default:
          return status.toString();
      }
    } else {
      switch (status) {
        case OrderStatus.paymentConfirmed:
          return 'Payment Confirmed';
        case OrderStatus.delivered:
          return 'Delivered';
        case OrderStatus.completed:
          return 'Completed';
        default:
          return status.toString();
      }
    }
  }

  String _getPaymentStatusText(PaymentStatus status, bool isArabic) {
    if (isArabic) {
      switch (status) {
        case PaymentStatus.confirmed:
          return 'مؤكد';
        case PaymentStatus.pending:
          return 'في الانتظار';
        default:
          return status.toString();
      }
    } else {
      switch (status) {
        case PaymentStatus.confirmed:
          return 'Confirmed';
        case PaymentStatus.pending:
          return 'Pending';
        default:
          return status.toString();
      }
    }
  }
}

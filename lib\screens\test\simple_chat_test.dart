import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/language_provider.dart';
import '../../models/chat_model.dart';
import '../../models/message_model.dart';
import '../../models/order_model.dart';
import '../../widgets/chat/enhanced_chat_widgets.dart';

/// Simple test screen to verify enhanced chat widgets work
class SimpleChatTest extends StatelessWidget {
  const SimpleChatTest({super.key});

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isArabic = languageProvider.locale.languageCode == 'ar';
    
    return Scaffold(
      appBar: AppBar(
        title: Text(isArabic ? 'اختبار المحادثة' : 'Chat Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Test Header
            Text(
              isArabic ? 'اختبار ميزات المحادثة المحسنة' : 'Enhanced Chat Features Test',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Test 1: Enhanced Chat List Item
            Text(
              isArabic ? '1. عنصر قائمة المحادثة مع شارة عدم القراءة:' : '1. Chat List Item with Unread Badge:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            
            EnhancedChatListItem(
              chat: ChatModel(
                id: 'test1',
                requestId: 'req1',
                clientId: 'client1',
                freelancerId: 'freelancer1',
                lastMessage: isArabic ? 'مرحبا، كيف يمكنني مساعدتك؟' : 'Hello, how can I help you?',
                lastMessageTime: DateTime.now().subtract(const Duration(minutes: 5)),
                createdAt: DateTime.now(),
              ),
              unreadCount: 3,
              recipientName: isArabic ? 'أحمد المطور' : 'Ahmed Developer',
              recipientAvatar: '👨‍💻',
              onTap: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(isArabic ? 'تم النقر على المحادثة' : 'Chat tapped')),
                );
              },
            ),
            
            const SizedBox(height: 24),
            
            // Test 2: Timeline Message
            Text(
              isArabic ? '2. رسالة الجدول الزمني:' : '2. Timeline Message:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            
            TimelineMessageWidget(
              message: MessageModel(
                id: 'timeline1',
                chatId: 'test1',
                senderId: 'system',
                content: isArabic 
                    ? 'تم إنشاء الطلب #12345 لـ "تطوير تطبيق فلاتر" - \$500'
                    : 'Order #12345 created for "Flutter App Development" - \$500',
                type: MessageType.timeline,
                createdAt: DateTime.now().subtract(const Duration(hours: 1)),
                metadata: {
                  'type': 'order_created',
                  'order_id': '12345',
                  'amount': 500.0,
                  'service_title': 'Flutter App Development',
                },
              ),
              languageCode: languageProvider.locale.languageCode,
            ),
            
            const SizedBox(height: 24),
            
            // Test 3: Regular Message Bubble
            Text(
              isArabic ? '3. فقاعة الرسالة العادية:' : '3. Regular Message Bubble:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            
            RegularMessageBubble(
              message: MessageModel(
                id: 'msg1',
                chatId: 'test1',
                senderId: 'user1',
                content: isArabic ? 'مرحبا! كيف يمكنني مساعدتك اليوم؟' : 'Hello! How can I help you today?',
                type: MessageType.text,
                status: MessageStatus.read,
                createdAt: DateTime.now().subtract(const Duration(minutes: 10)),
              ),
              isMe: false,
              senderName: isArabic ? 'أحمد' : 'Ahmed',
            ),
            
            const SizedBox(height: 8),
            
            RegularMessageBubble(
              message: MessageModel(
                id: 'msg2',
                chatId: 'test1',
                senderId: 'me',
                content: isArabic ? 'أحتاج مساعدة في تطوير تطبيق فلاتر' : 'I need help with Flutter app development',
                type: MessageType.text,
                status: MessageStatus.delivered,
                createdAt: DateTime.now().subtract(const Duration(minutes: 5)),
              ),
              isMe: true,
            ),
            
            const SizedBox(height: 24),
            
            // Test 4: Quick Actions
            Text(
              isArabic ? '4. الإجراءات السريعة:' : '4. Quick Actions:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            
            ChatQuickActions(
              orderId: '12345',
              orderStatus: OrderStatus.delivered,
              isClient: true,
              onApproveDelivery: () => _showSnackBar(context, isArabic ? 'تم قبول التسليم' : 'Delivery Approved'),
              onRequestRevision: () => _showSnackBar(context, isArabic ? 'تم طلب المراجعة' : 'Revision Requested'),
              onViewDelivery: () => _showSnackBar(context, isArabic ? 'عرض التسليم' : 'View Delivery'),
            ),
            
            const SizedBox(height: 24),
            
            // Test 5: Order Status Indicator
            Text(
              isArabic ? '5. مؤشر حالة الطلب:' : '5. Order Status Indicator:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            
            const OrderStatusIndicator(
              status: OrderStatus.delivered,
              orderId: '12345',
            ),
            
            const SizedBox(height: 24),
            
            // Success Message
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
              ),
              child: Column(
                children: [
                  const Icon(Icons.check_circle, color: Colors.green, size: 32),
                  const SizedBox(height: 8),
                  Text(
                    isArabic ? '✅ جميع ميزات المحادثة المحسنة تعمل بشكل صحيح!' : '✅ All Enhanced Chat Features Working!',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Colors.green,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    isArabic 
                        ? 'إذا كنت ترى هذه الرسالة، فإن نظام المحادثة المحسن يعمل بشكل مثالي!'
                        : 'If you can see this message, the enhanced chat system is working perfectly!',
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import '../../providers/theme_provider.dart';

/// Enhanced Notification Item
class EnhancedNotificationItem extends StatelessWidget {
  final String title;
  final String message;
  final String time;
  final bool isRead;
  final IconData icon;
  final Color iconColor;
  final VoidCallback? onTap;
  final VoidCallback? onMarkAsRead;
  final VoidCallback? onDelete;
  final bool isArabic;

  const EnhancedNotificationItem({
    super.key,
    required this.title,
    required this.message,
    required this.time,
    required this.isRead,
    required this.icon,
    required this.iconColor,
    this.onTap,
    this.onMarkAsRead,
    this.onDelete,
    this.isArabic = false,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color:
            isRead
                ? (isDark ? ThemeProvider.darkCardBackground : ThemeProvider.cardBackground)
                : (isDark
                    ? ThemeProvider.primaryBlue.withValues(alpha: 0.1)
                    : ThemeProvider.primaryBlue.withValues(alpha: 0.05)),
        borderRadius: BorderRadius.circular(16),
        border: isRead ? null : Border.all(color: ThemeProvider.primaryBlue.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Icon
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: iconColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, size: 24),
                ),

                const SizedBox(width: 16),

                // Content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              title,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: isRead ? FontWeight.w600 : FontWeight.bold,
                                color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                              ),
                            ),
                          ),
                          if (!isRead)
                            Container(
                              width: 8,
                              height: 8,
                              decoration: const BoxDecoration(color: ThemeProvider.primaryBlue, shape: BoxShape.circle),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        message,
                        style: TextStyle(
                          fontSize: 14,
                          color: isDark ? ThemeProvider.darkTextSecondary : ThemeProvider.neutralGray,
                          height: 1.4,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(
                            Icons.schedule,
                            size: 14,
                            color: isDark ? ThemeProvider.darkTextSecondary : ThemeProvider.neutralGray,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            time,
                            style: TextStyle(
                              fontSize: 12,
                              color: isDark ? ThemeProvider.darkTextSecondary : ThemeProvider.neutralGray,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Actions
                PopupMenuButton<String>(
                  icon: Icon(
                    Icons.more_vert,
                    color: isDark ? ThemeProvider.darkTextSecondary : ThemeProvider.neutralGray,
                  ),
                  onSelected: (value) {
                    switch (value) {
                      case 'mark_read':
                        onMarkAsRead?.call();
                        break;
                      case 'delete':
                        onDelete?.call();
                        break;
                    }
                  },
                  itemBuilder:
                      (context) => [
                        if (!isRead)
                          PopupMenuItem(
                            value: 'mark_read',
                            child: Row(
                              children: [
                                const Icon(Icons.mark_email_read, size: 18),
                                const SizedBox(width: 8),
                                Text(isArabic ? 'تحديد كمقروء' : 'Mark as read'),
                              ],
                            ),
                          ),
                        PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              const Icon(Icons.delete, size: 18, color: Colors.red),
                              const SizedBox(width: 8),
                              Text(isArabic ? 'حذف' : 'Delete', style: const TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                      ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Enhanced Notification Badge
class EnhancedNotificationBadge extends StatelessWidget {
  final int count;
  final Widget child;

  const EnhancedNotificationBadge({super.key, required this.count, required this.child});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (count > 0)
          Positioned(
            right: 0,
            top: 0,
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(10),
                border: Border.all(color: Colors.white, width: 2),
              ),
              constraints: const BoxConstraints(minWidth: 20, minHeight: 20),
              child: Text(
                count > 99 ? '99+' : count.toString(),
                style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
            ),
          ),
      ],
    );
  }
}

/// Enhanced Notification Filter Chips
class EnhancedNotificationFilters extends StatelessWidget {
  final List<NotificationFilter> filters;
  final String selectedFilter;
  final Function(String) onFilterChanged;
  final bool isArabic;

  const EnhancedNotificationFilters({
    super.key,
    required this.filters,
    required this.selectedFilter,
    required this.onFilterChanged,
    this.isArabic = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: filters.length,
        itemBuilder: (context, index) {
          final filter = filters[index];
          final isSelected = selectedFilter == filter.id;

          return Padding(
            padding: const EdgeInsets.only(right: 12),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              child: FilterChip(
                selected: isSelected,
                elevation: isSelected ? 4 : 1,
                shadowColor: isSelected ? ThemeProvider.primaryBlue.withValues(alpha: 0.3) : null,
                label: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(filter.icon, size: 18, color: isSelected ? Colors.white : ThemeProvider.primaryBlue),
                    const SizedBox(width: 6),
                    Text(
                      isArabic ? filter.nameAr : filter.nameEn,
                      style: TextStyle(fontWeight: isSelected ? FontWeight.bold : FontWeight.w500),
                    ),
                    if (filter.count > 0) ...[
                      const SizedBox(width: 6),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color:
                              isSelected
                                  ? Colors.white.withValues(alpha: 0.2)
                                  : ThemeProvider.primaryBlue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Text(
                          filter.count.toString(),
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: isSelected ? Colors.white : ThemeProvider.primaryBlue,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                backgroundColor: isSelected ? ThemeProvider.primaryBlue : Colors.white,
                selectedColor: ThemeProvider.primaryBlue,
                labelStyle: TextStyle(color: isSelected ? Colors.white : ThemeProvider.primaryBlue),
                onSelected: (selected) {
                  onFilterChanged(filter.id);
                },
              ),
            ),
          );
        },
      ),
    );
  }
}

class NotificationFilter {
  final String id;
  final String nameEn;
  final String nameAr;
  final IconData icon;
  final int count;

  const NotificationFilter({
    required this.id,
    required this.nameEn,
    required this.nameAr,
    required this.icon,
    this.count = 0,
  });
}

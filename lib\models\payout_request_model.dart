enum PayoutRequestStatus { pending, approved, rejected, processing, completed }

enum PayoutMethod { vodafoneCash, instaPay }

class PayoutRequestModel {
  final String id;
  final String freelancerId;
  final double amount;
  final PayoutMethod payoutMethod;
  final String accountDetails; // Phone number for Vodafone Cash or account info for InstaPay
  final PayoutRequestStatus status;
  final String? adminNotes;
  final String? rejectionReason;
  final DateTime createdAt;
  final DateTime? processedAt;
  final String? processedByAdminId;

  PayoutRequestModel({
    required this.id,
    required this.freelancerId,
    required this.amount,
    required this.payoutMethod,
    required this.accountDetails,
    this.status = PayoutRequestStatus.pending,
    this.adminNotes,
    this.rejectionReason,
    required this.createdAt,
    this.processedAt,
    this.processedByAdminId,
  });

  factory PayoutRequestModel.fromJson(Map<String, dynamic> json) {
    return PayoutRequestModel(
      id: json['id'],
      freelancerId: json['freelancer_id'],
      amount: json['amount'].toDouble(),
      payoutMethod: PayoutMethod.values.firstWhere(
        (e) => e.toString().split('.').last == json['payout_method'],
      ),
      accountDetails: json['account_details'],
      status: PayoutRequestStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
      ),
      adminNotes: json['admin_notes'],
      rejectionReason: json['rejection_reason'],
      createdAt: DateTime.parse(json['created_at']),
      processedAt: json['processed_at'] != null ? DateTime.parse(json['processed_at']) : null,
      processedByAdminId: json['processed_by_admin_id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'freelancer_id': freelancerId,
      'amount': amount,
      'payout_method': payoutMethod.toString().split('.').last,
      'account_details': accountDetails,
      'status': status.toString().split('.').last,
      'admin_notes': adminNotes,
      'rejection_reason': rejectionReason,
      'created_at': createdAt.toIso8601String(),
      'processed_at': processedAt?.toIso8601String(),
      'processed_by_admin_id': processedByAdminId,
    };
  }

  PayoutRequestModel copyWith({
    String? id,
    String? freelancerId,
    double? amount,
    PayoutMethod? payoutMethod,
    String? accountDetails,
    PayoutRequestStatus? status,
    String? adminNotes,
    String? rejectionReason,
    DateTime? createdAt,
    DateTime? processedAt,
    String? processedByAdminId,
  }) {
    return PayoutRequestModel(
      id: id ?? this.id,
      freelancerId: freelancerId ?? this.freelancerId,
      amount: amount ?? this.amount,
      payoutMethod: payoutMethod ?? this.payoutMethod,
      accountDetails: accountDetails ?? this.accountDetails,
      status: status ?? this.status,
      adminNotes: adminNotes ?? this.adminNotes,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      createdAt: createdAt ?? this.createdAt,
      processedAt: processedAt ?? this.processedAt,
      processedByAdminId: processedByAdminId ?? this.processedByAdminId,
    );
  }

  String get payoutMethodDisplayName {
    switch (payoutMethod) {
      case PayoutMethod.vodafoneCash:
        return 'Vodafone Cash';
      case PayoutMethod.instaPay:
        return 'InstaPay';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case PayoutRequestStatus.pending:
        return 'Pending';
      case PayoutRequestStatus.approved:
        return 'Approved';
      case PayoutRequestStatus.rejected:
        return 'Rejected';
      case PayoutRequestStatus.processing:
        return 'Processing';
      case PayoutRequestStatus.completed:
        return 'Completed';
    }
  }
}

import '../config/supabase_config.dart';
import '../models/service_request_model.dart';
import '../models/offer_model.dart';
import 'offer_chat_service.dart';

class RequestService {
  static Future<List<ServiceRequestModel>> getRequests({String? clientId, RequestStatus? status}) async {
    try {
      var query = SupabaseConfig.client.from('service_requests').select();

      if (clientId != null) {
        query = query.eq('client_id', clientId);
      }

      if (status != null) {
        query = query.eq('status', status.toString().split('.').last);
      }

      final response = await query.order('created_at', ascending: false);

      return response.map<ServiceRequestModel>((json) => ServiceRequestModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to fetch requests: $e');
    }
  }

  static Future<ServiceRequestModel> createRequest(ServiceRequestModel request) async {
    try {
      final response = await SupabaseConfig.client.from('service_requests').insert(request.toJson()).select().single();

      return ServiceRequestModel.fromJson(response);
    } catch (e) {
      throw Exception('Failed to create request: $e');
    }
  }

  static Future<ServiceRequestModel?> getRequestById(String requestId) async {
    try {
      final response = await SupabaseConfig.client.from('service_requests').select().eq('id', requestId).single();

      return ServiceRequestModel.fromJson(response);
    } catch (e) {
      print('Failed to get request by ID: $e');
      return null;
    }
  }

  static Future<ServiceRequestModel> updateRequest(ServiceRequestModel request) async {
    try {
      final response =
          await SupabaseConfig.client
              .from('service_requests')
              .update(request.toJson())
              .eq('id', request.id)
              .select()
              .single();

      return ServiceRequestModel.fromJson(response);
    } catch (e) {
      throw Exception('Failed to update request: $e');
    }
  }

  static Future<void> deleteRequest(String requestId) async {
    try {
      await SupabaseConfig.client.from('service_requests').delete().eq('id', requestId);
    } catch (e) {
      throw Exception('Failed to delete request: $e');
    }
  }

  static Future<List<OfferModel>> getOffersForRequest(String requestId) async {
    try {
      final response = await SupabaseConfig.client
          .from('offers')
          .select()
          .eq('request_id', requestId)
          .order('created_at', ascending: false);

      return response.map<OfferModel>((json) => OfferModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to fetch offers: $e');
    }
  }

  static Future<OfferModel> createOffer(OfferModel offer, {String languageCode = 'en'}) async {
    try {
      final response = await SupabaseConfig.client.from('offers').insert(offer.toJson()).select().single();

      final createdOffer = OfferModel.fromJson(response);

      // Create chat between freelancer and client when offer is sent
      await OfferChatService.createChatForOffer(createdOffer, languageCode: languageCode);

      return createdOffer;
    } catch (e) {
      throw Exception('Failed to create offer: $e');
    }
  }

  static Future<OfferModel> updateOffer(OfferModel offer) async {
    try {
      final response =
          await SupabaseConfig.client.from('offers').update(offer.toJson()).eq('id', offer.id).select().single();

      return OfferModel.fromJson(response);
    } catch (e) {
      throw Exception('Failed to update offer: $e');
    }
  }

  static Future<List<ServiceRequestModel>> getAvailableRequests() async {
    try {
      final response = await SupabaseConfig.client
          .from('service_requests')
          .select()
          .eq('status', 'pending')
          .order('created_at', ascending: false);

      return response.map<ServiceRequestModel>((json) => ServiceRequestModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to fetch available requests: $e');
    }
  }

  static Future<List<OfferModel>> getFreelancerOffers(String freelancerId) async {
    try {
      final response = await SupabaseConfig.client
          .from('offers')
          .select()
          .eq('freelancer_id', freelancerId)
          .order('created_at', ascending: false);

      return response.map<OfferModel>((json) => OfferModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to fetch freelancer offers: $e');
    }
  }
}

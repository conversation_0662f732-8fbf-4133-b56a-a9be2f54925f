import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/language_provider.dart';
import '../../providers/theme_provider.dart';
import '../../models/payout_request_model.dart';
import '../../services/payout_service.dart';

class PayoutHistoryScreen extends StatefulWidget {
  final String freelancerId;

  const PayoutHistoryScreen({super.key, required this.freelancerId});

  @override
  State<PayoutHistoryScreen> createState() => _PayoutHistoryScreenState();
}

class _PayoutHistoryScreenState extends State<PayoutHistoryScreen> {
  List<PayoutRequestModel> _payoutRequests = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPayoutHistory();
  }

  Future<void> _loadPayoutHistory() async {
    try {
      final requests = await PayoutService.getPayoutRequests(widget.freelancerId);
      if (mounted) {
        setState(() {
          _payoutRequests = requests;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<LanguageProvider, ThemeProvider>(
      builder: (context, languageProvider, themeProvider, child) {
        final isArabic = languageProvider.isArabic;
        final isDark = themeProvider.isDarkMode;

        return Directionality(
          textDirection: languageProvider.textDirection,
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : RefreshIndicator(
                  onRefresh: _loadPayoutHistory,
                  child: _payoutRequests.isEmpty
                      ? _buildEmptyState(isArabic, isDark)
                      : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: _payoutRequests.length,
                          itemBuilder: (context, index) {
                            final request = _payoutRequests[index];
                            return _buildPayoutRequestCard(request, isArabic, isDark);
                          },
                        ),
                ),
        );
      },
    );
  }

  Widget _buildEmptyState(bool isArabic, bool isDark) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history,
            size: 64,
            color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            isArabic ? 'لا توجد طلبات سحب' : 'No withdrawal requests',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            isArabic 
                ? 'ستظهر طلبات السحب الخاصة بك هنا'
                : 'Your withdrawal requests will appear here',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPayoutRequestCard(PayoutRequestModel request, bool isArabic, bool isDark) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? ThemeProvider.darkCardBackground : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getStatusColor(request.status).withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with amount and status
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'EGP ${request.amount.toStringAsFixed(2)}',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: _getStatusColor(request.status).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: _getStatusColor(request.status).withValues(alpha: 0.3),
                  ),
                ),
                child: Text(
                  _getStatusText(request.status, isArabic),
                  style: TextStyle(
                    color: _getStatusColor(request.status),
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          // Payout method and account details
          Row(
            children: [
              Icon(
                request.payoutMethod == PayoutMethod.vodafoneCash 
                    ? Icons.phone_android 
                    : Icons.account_balance,
                size: 16,
                color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
              ),
              const SizedBox(width: 8),
              Text(
                request.payoutMethodDisplayName,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[700],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  request.accountDetails,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          // Date and processing info
          Row(
            children: [
              Icon(
                Icons.access_time,
                size: 16,
                color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
              ),
              const SizedBox(width: 8),
              Text(
                _formatDate(request.createdAt, isArabic),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
                ),
              ),
              if (request.processedAt != null) ...[
                const SizedBox(width: 16),
                Icon(
                  Icons.check_circle_outline,
                  size: 16,
                  color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
                ),
                const SizedBox(width: 8),
                Text(
                  '${isArabic ? 'تمت المعالجة:' : 'Processed:'} ${_formatDate(request.processedAt!, isArabic)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
                  ),
                ),
              ],
            ],
          ),
          
          // Admin notes or rejection reason
          if (request.adminNotes != null || request.rejectionReason != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: request.status == PayoutRequestStatus.rejected
                    ? Colors.red.withValues(alpha: 0.1)
                    : Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    request.status == PayoutRequestStatus.rejected
                        ? Icons.error_outline
                        : Icons.info_outline,
                    size: 16,
                    color: request.status == PayoutRequestStatus.rejected
                        ? Colors.red[600]
                        : Colors.blue[600],
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      request.rejectionReason ?? request.adminNotes ?? '',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: request.status == PayoutRequestStatus.rejected
                            ? Colors.red[700]
                            : Colors.blue[700],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getStatusColor(PayoutRequestStatus status) {
    switch (status) {
      case PayoutRequestStatus.pending:
        return Colors.orange;
      case PayoutRequestStatus.approved:
        return Colors.blue;
      case PayoutRequestStatus.processing:
        return Colors.purple;
      case PayoutRequestStatus.completed:
        return Colors.green;
      case PayoutRequestStatus.rejected:
        return Colors.red;
    }
  }

  String _getStatusText(PayoutRequestStatus status, bool isArabic) {
    switch (status) {
      case PayoutRequestStatus.pending:
        return isArabic ? 'قيد المراجعة' : 'Pending';
      case PayoutRequestStatus.approved:
        return isArabic ? 'موافق عليه' : 'Approved';
      case PayoutRequestStatus.processing:
        return isArabic ? 'قيد المعالجة' : 'Processing';
      case PayoutRequestStatus.completed:
        return isArabic ? 'مكتمل' : 'Completed';
      case PayoutRequestStatus.rejected:
        return isArabic ? 'مرفوض' : 'Rejected';
    }
  }

  String _formatDate(DateTime date, bool isArabic) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return isArabic ? 'اليوم' : 'Today';
    } else if (difference.inDays == 1) {
      return isArabic ? 'أمس' : 'Yesterday';
    } else if (difference.inDays < 7) {
      return isArabic ? '${difference.inDays} أيام' : '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}

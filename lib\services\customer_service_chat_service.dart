import 'dart:async';
import '../models/chat_model.dart';
import '../models/message_model.dart';

/// Service to handle customer service chat functionality
class CustomerServiceChatService {
  static const String customerServiceId = 'customer_service_admin';
  static const String customerServiceNameEn = 'Customer Service';
  static const String customerServiceNameAr = 'خدمة العملاء';

  /// Create or get existing customer service chat for a client
  static Future<ChatModel> ensureCustomerServiceChat(String clientId) async {
    try {
      // Check if customer service chat already exists for this client
      final existingChat = await _getExistingCustomerServiceChat(clientId);
      if (existingChat != null) {
        return existingChat;
      }

      // Create new customer service chat
      return await _createCustomerServiceChat(clientId);
    } catch (e) {
      print('Error ensuring customer service chat: $e');
      // Return demo chat for fallback
      return _createDemoCustomerServiceChat(clientId);
    }
  }

  /// Check if customer service chat exists for client
  static Future<ChatModel?> _getExistingCustomerServiceChat(String clientId) async {
    try {
      // In demo mode, we'll create a demo chat
      // In production, this would query the database
      return null; // Force creation of new chat for demo
    } catch (e) {
      return null;
    }
  }

  /// Create new customer service chat
  static Future<ChatModel> _createCustomerServiceChat(String clientId) async {
    try {
      final chatId = 'customer_service_$clientId';

      final chat = ChatModel(
        id: chatId,
        requestId: 'customer_service_request',
        clientId: clientId,
        freelancerId: customerServiceId,
        createdAt: DateTime.now(),
      );

      // In demo mode, we'll store this locally
      // In production, this would save to database
      final createdChat = await _saveChatToDemo(chat);

      // Send welcome messages
      await _sendWelcomeMessages(chatId, clientId);

      return createdChat;
    } catch (e) {
      print('Error creating customer service chat: $e');
      return _createDemoCustomerServiceChat(clientId);
    }
  }

  /// Create demo customer service chat (fallback)
  static ChatModel _createDemoCustomerServiceChat(String clientId) {
    final chatId = 'customer_service_$clientId';

    return ChatModel(
      id: chatId,
      requestId: 'customer_service_request',
      clientId: clientId,
      freelancerId: customerServiceId,
      createdAt: DateTime.now(),
    );
  }

  /// Save chat to demo storage (in production, this would use Supabase)
  static Future<ChatModel> _saveChatToDemo(ChatModel chat) async {
    // For demo purposes, we'll just return the chat
    // In production, this would save to Supabase
    return chat;
  }

  /// Send welcome messages to customer service chat
  static Future<void> _sendWelcomeMessages(String chatId, String clientId) async {
    try {
      // Welcome message in English
      final welcomeMessageEn = MessageModel(
        id: '${chatId}_welcome_en',
        chatId: chatId,
        senderId: customerServiceId,
        content:
            '👋 Welcome to Taskly Customer Service!\n\nI\'m here to help you with:\n• Order questions\n• Payment support\n• Technical issues\n• General inquiries\n\nHow can I assist you today?',
        createdAt: DateTime.now(),
        isRead: false,
        type: MessageType.text,
      );

      // Welcome message in Arabic
      final welcomeMessageAr = MessageModel(
        id: '${chatId}_welcome_ar',
        chatId: chatId,
        senderId: customerServiceId,
        content:
            '👋 مرحباً بك في خدمة عملاء تاسكلي!\n\nأنا هنا لمساعدتك في:\n• أسئلة الطلبات\n• دعم المدفوعات\n• المشاكل التقنية\n• الاستفسارات العامة\n\nكيف يمكنني مساعدتك اليوم؟',
        createdAt: DateTime.now().add(const Duration(seconds: 1)),
        isRead: false,
        type: MessageType.text,
      );

      // In demo mode, we'll store these messages locally
      // In production, these would be saved to database
      await _saveMessageToDemo(welcomeMessageEn);
      await _saveMessageToDemo(welcomeMessageAr);

      print('✅ Customer service welcome messages sent for client: $clientId');
    } catch (e) {
      print('Error sending welcome messages: $e');
    }
  }

  /// Save message to demo storage (in production, this would use Supabase)
  static Future<void> _saveMessageToDemo(MessageModel message) async {
    // For demo purposes, we'll just print
    // In production, this would save to Supabase
    print('Demo message saved: ${message.content.substring(0, 50)}...');
  }

  /// Get customer service chat info for UI display
  static Map<String, dynamic> getCustomerServiceChatInfo(String clientId, bool isArabic) {
    return {
      'id': 'customer_service_$clientId',
      'name': isArabic ? customerServiceNameAr : customerServiceNameEn,
      'avatar': '👨‍💼', // Customer service avatar
      'lastMessage': isArabic ? 'مرحباً! كيف يمكنني مساعدتك؟' : 'Hello! How can I help you?',
      'timestamp': DateTime.now(),
      'unreadCount': 0,
      'isOnline': true,
      'isCustomerService': true,
      'requestTitle': isArabic ? 'خدمة العملاء' : 'Customer Service',
    };
  }

  /// Initialize customer service for all existing clients (migration helper)
  static Future<void> initializeCustomerServiceForAllClients() async {
    try {
      // This would be used for migrating existing clients
      // In production, you'd query all clients and ensure they have customer service chats
      print('🔄 Customer service initialization completed');
    } catch (e) {
      print('Error initializing customer service for all clients: $e');
    }
  }

  /// Check if a chat is a customer service chat
  static bool isCustomerServiceChat(String chatId) {
    return chatId.startsWith('customer_service_');
  }

  /// Get customer service representative info
  static Map<String, dynamic> getCustomerServiceRepInfo(bool isArabic) {
    return {
      'id': customerServiceId,
      'name': isArabic ? customerServiceNameAr : customerServiceNameEn,
      'avatar': '👨‍💼',
      'role': isArabic ? 'ممثل خدمة العملاء' : 'Customer Service Representative',
      'isOnline': true,
      'responseTime': isArabic ? 'يرد عادة خلال دقائق' : 'Usually responds within minutes',
    };
  }
}

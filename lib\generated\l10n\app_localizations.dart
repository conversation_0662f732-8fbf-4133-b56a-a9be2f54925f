import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('en'),
  ];

  /// No description provided for @appTitle.
  ///
  /// In en, this message translates to:
  /// **'FreelanceHub'**
  String get appTitle;

  /// No description provided for @welcomeBack.
  ///
  /// In en, this message translates to:
  /// **'Welcome Back'**
  String get welcomeBack;

  /// No description provided for @signInToContinue.
  ///
  /// In en, this message translates to:
  /// **'Sign in to continue to FreelanceHub'**
  String get signInToContinue;

  /// No description provided for @email.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// No description provided for @password.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// No description provided for @signIn.
  ///
  /// In en, this message translates to:
  /// **'Sign In'**
  String get signIn;

  /// No description provided for @signUp.
  ///
  /// In en, this message translates to:
  /// **'Sign Up'**
  String get signUp;

  /// No description provided for @dontHaveAccount.
  ///
  /// In en, this message translates to:
  /// **'Don\'t have an account?'**
  String get dontHaveAccount;

  /// No description provided for @alreadyHaveAccount.
  ///
  /// In en, this message translates to:
  /// **'Already have an account?'**
  String get alreadyHaveAccount;

  /// No description provided for @createAccount.
  ///
  /// In en, this message translates to:
  /// **'Create Account'**
  String get createAccount;

  /// No description provided for @joinFreelanceHub.
  ///
  /// In en, this message translates to:
  /// **'Join FreelanceHub'**
  String get joinFreelanceHub;

  /// No description provided for @createYourAccount.
  ///
  /// In en, this message translates to:
  /// **'Create your account to get started'**
  String get createYourAccount;

  /// No description provided for @fullName.
  ///
  /// In en, this message translates to:
  /// **'Full Name'**
  String get fullName;

  /// No description provided for @iWantTo.
  ///
  /// In en, this message translates to:
  /// **'I want to'**
  String get iWantTo;

  /// No description provided for @hireFreelancers.
  ///
  /// In en, this message translates to:
  /// **'Hire freelancers (Client)'**
  String get hireFreelancers;

  /// No description provided for @offerServices.
  ///
  /// In en, this message translates to:
  /// **'Offer services (Freelancer)'**
  String get offerServices;

  /// No description provided for @confirmPassword.
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get confirmPassword;

  /// No description provided for @quickDemoLogin.
  ///
  /// In en, this message translates to:
  /// **'Quick Demo Login'**
  String get quickDemoLogin;

  /// No description provided for @loginAsClient.
  ///
  /// In en, this message translates to:
  /// **'Login as Client'**
  String get loginAsClient;

  /// No description provided for @loginAsFreelancer.
  ///
  /// In en, this message translates to:
  /// **'Login as Freelancer'**
  String get loginAsFreelancer;

  /// No description provided for @loginAsAdmin.
  ///
  /// In en, this message translates to:
  /// **'Login as Admin'**
  String get loginAsAdmin;

  /// No description provided for @dashboard.
  ///
  /// In en, this message translates to:
  /// **'Dashboard'**
  String get dashboard;

  /// No description provided for @requests.
  ///
  /// In en, this message translates to:
  /// **'Requests'**
  String get requests;

  /// No description provided for @orders.
  ///
  /// In en, this message translates to:
  /// **'Orders'**
  String get orders;

  /// No description provided for @messages.
  ///
  /// In en, this message translates to:
  /// **'Messages'**
  String get messages;

  /// No description provided for @browse.
  ///
  /// In en, this message translates to:
  /// **'Browse'**
  String get browse;

  /// No description provided for @offers.
  ///
  /// In en, this message translates to:
  /// **'Offers'**
  String get offers;

  /// No description provided for @jobs.
  ///
  /// In en, this message translates to:
  /// **'Jobs'**
  String get jobs;

  /// No description provided for @payments.
  ///
  /// In en, this message translates to:
  /// **'Payments'**
  String get payments;

  /// No description provided for @users.
  ///
  /// In en, this message translates to:
  /// **'Users'**
  String get users;

  /// No description provided for @welcomeBackUser.
  ///
  /// In en, this message translates to:
  /// **'Welcome back, {name}!'**
  String welcomeBackUser(String name);

  /// No description provided for @readyToFindFreelancer.
  ///
  /// In en, this message translates to:
  /// **'Ready to find the perfect freelancer for your project?'**
  String get readyToFindFreelancer;

  /// No description provided for @postNewRequest.
  ///
  /// In en, this message translates to:
  /// **'Post New Request'**
  String get postNewRequest;

  /// No description provided for @activeOrders.
  ///
  /// In en, this message translates to:
  /// **'Active Orders'**
  String get activeOrders;

  /// No description provided for @totalRequests.
  ///
  /// In en, this message translates to:
  /// **'Total Requests'**
  String get totalRequests;

  /// No description provided for @recentRequests.
  ///
  /// In en, this message translates to:
  /// **'Recent Requests'**
  String get recentRequests;

  /// No description provided for @noRequestsYet.
  ///
  /// In en, this message translates to:
  /// **'No requests yet'**
  String get noRequestsYet;

  /// No description provided for @createFirstRequest.
  ///
  /// In en, this message translates to:
  /// **'Create your first service request to get started'**
  String get createFirstRequest;

  /// No description provided for @welcomeFreelancer.
  ///
  /// In en, this message translates to:
  /// **'Welcome, {name}!'**
  String welcomeFreelancer(String name);

  /// No description provided for @findNextOpportunity.
  ///
  /// In en, this message translates to:
  /// **'Find your next opportunity and grow your freelance career.'**
  String get findNextOpportunity;

  /// No description provided for @browseJobs.
  ///
  /// In en, this message translates to:
  /// **'Browse Jobs'**
  String get browseJobs;

  /// No description provided for @profile.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile;

  /// No description provided for @activeJobs.
  ///
  /// In en, this message translates to:
  /// **'Active Jobs'**
  String get activeJobs;

  /// No description provided for @pendingOffers.
  ///
  /// In en, this message translates to:
  /// **'Pending Offers'**
  String get pendingOffers;

  /// No description provided for @latestOpportunities.
  ///
  /// In en, this message translates to:
  /// **'Latest Opportunities'**
  String get latestOpportunities;

  /// No description provided for @noOpportunitiesAvailable.
  ///
  /// In en, this message translates to:
  /// **'No opportunities available'**
  String get noOpportunitiesAvailable;

  /// No description provided for @checkBackLater.
  ///
  /// In en, this message translates to:
  /// **'Check back later for new project requests'**
  String get checkBackLater;

  /// No description provided for @recentOffers.
  ///
  /// In en, this message translates to:
  /// **'Recent Offers'**
  String get recentOffers;

  /// No description provided for @adminControlCenter.
  ///
  /// In en, this message translates to:
  /// **'Admin Control Center'**
  String get adminControlCenter;

  /// No description provided for @managePlatform.
  ///
  /// In en, this message translates to:
  /// **'Manage users, verify payments, and oversee platform operations.'**
  String get managePlatform;

  /// No description provided for @totalUsers.
  ///
  /// In en, this message translates to:
  /// **'Total Users'**
  String get totalUsers;

  /// No description provided for @totalOrders.
  ///
  /// In en, this message translates to:
  /// **'Total Orders'**
  String get totalOrders;

  /// No description provided for @revenue.
  ///
  /// In en, this message translates to:
  /// **'Revenue'**
  String get revenue;

  /// No description provided for @paymentVerificationRequired.
  ///
  /// In en, this message translates to:
  /// **'Payment Verification Required'**
  String get paymentVerificationRequired;

  /// No description provided for @paymentsAwaitingVerification.
  ///
  /// In en, this message translates to:
  /// **'{count} payments awaiting verification'**
  String paymentsAwaitingVerification(int count);

  /// No description provided for @review.
  ///
  /// In en, this message translates to:
  /// **'Review'**
  String get review;

  /// No description provided for @quickActions.
  ///
  /// In en, this message translates to:
  /// **'Quick Actions'**
  String get quickActions;

  /// No description provided for @verifyPayments.
  ///
  /// In en, this message translates to:
  /// **'Verify Payments'**
  String get verifyPayments;

  /// No description provided for @manageUsers.
  ///
  /// In en, this message translates to:
  /// **'Manage Users'**
  String get manageUsers;

  /// No description provided for @orderManagement.
  ///
  /// In en, this message translates to:
  /// **'Order Management'**
  String get orderManagement;

  /// No description provided for @systemMessages.
  ///
  /// In en, this message translates to:
  /// **'System Messages'**
  String get systemMessages;

  /// No description provided for @recentActivity.
  ///
  /// In en, this message translates to:
  /// **'Recent Activity'**
  String get recentActivity;

  /// No description provided for @newUserRegistration.
  ///
  /// In en, this message translates to:
  /// **'New user registration'**
  String get newUserRegistration;

  /// No description provided for @joinedAsFreelancer.
  ///
  /// In en, this message translates to:
  /// **'{email} joined as freelancer'**
  String joinedAsFreelancer(String email);

  /// No description provided for @paymentSubmitted.
  ///
  /// In en, this message translates to:
  /// **'Payment submitted'**
  String get paymentSubmitted;

  /// No description provided for @orderCompleted.
  ///
  /// In en, this message translates to:
  /// **'Order Completed'**
  String get orderCompleted;

  /// No description provided for @orderMarkedCompleted.
  ///
  /// In en, this message translates to:
  /// **'Order #{orderId} marked as completed'**
  String orderMarkedCompleted(String orderId);

  /// No description provided for @minutesAgo.
  ///
  /// In en, this message translates to:
  /// **'{count}m ago'**
  String minutesAgo(int count);

  /// No description provided for @hoursAgo.
  ///
  /// In en, this message translates to:
  /// **'{count}h ago'**
  String hoursAgo(int count);

  /// No description provided for @daysAgo.
  ///
  /// In en, this message translates to:
  /// **'{count}d ago'**
  String daysAgo(int count);

  /// No description provided for @logout.
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logout;

  /// No description provided for @settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// No description provided for @language.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// No description provided for @english.
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get english;

  /// No description provided for @arabic.
  ///
  /// In en, this message translates to:
  /// **'العربية'**
  String get arabic;

  /// No description provided for @darkMode.
  ///
  /// In en, this message translates to:
  /// **'Dark Mode'**
  String get darkMode;

  /// No description provided for @lightMode.
  ///
  /// In en, this message translates to:
  /// **'Light Mode'**
  String get lightMode;

  /// No description provided for @projectTitle.
  ///
  /// In en, this message translates to:
  /// **'Project Title'**
  String get projectTitle;

  /// No description provided for @whatDoYouNeed.
  ///
  /// In en, this message translates to:
  /// **'What do you need done?'**
  String get whatDoYouNeed;

  /// No description provided for @category.
  ///
  /// In en, this message translates to:
  /// **'Category'**
  String get category;

  /// No description provided for @projectDescription.
  ///
  /// In en, this message translates to:
  /// **'Project Description'**
  String get projectDescription;

  /// No description provided for @describeProject.
  ///
  /// In en, this message translates to:
  /// **'Describe your project in detail...'**
  String get describeProject;

  /// No description provided for @budget.
  ///
  /// In en, this message translates to:
  /// **'Budget (SAR)'**
  String get budget;

  /// No description provided for @enterBudget.
  ///
  /// In en, this message translates to:
  /// **'Enter your budget (optional)'**
  String get enterBudget;

  /// No description provided for @deadline.
  ///
  /// In en, this message translates to:
  /// **'Deadline'**
  String get deadline;

  /// No description provided for @selectDeadline.
  ///
  /// In en, this message translates to:
  /// **'Select deadline'**
  String get selectDeadline;

  /// No description provided for @attachments.
  ///
  /// In en, this message translates to:
  /// **'Attachments'**
  String get attachments;

  /// No description provided for @addFiles.
  ///
  /// In en, this message translates to:
  /// **'Add Files'**
  String get addFiles;

  /// No description provided for @canAttachFiles.
  ///
  /// In en, this message translates to:
  /// **'You can attach relevant files, images, or documents'**
  String get canAttachFiles;

  /// No description provided for @post.
  ///
  /// In en, this message translates to:
  /// **'POST'**
  String get post;

  /// No description provided for @webDevelopment.
  ///
  /// In en, this message translates to:
  /// **'Web Development'**
  String get webDevelopment;

  /// No description provided for @mobileDevelopment.
  ///
  /// In en, this message translates to:
  /// **'Mobile Development'**
  String get mobileDevelopment;

  /// No description provided for @graphicDesign.
  ///
  /// In en, this message translates to:
  /// **'Graphic Design'**
  String get graphicDesign;

  /// No description provided for @contentWriting.
  ///
  /// In en, this message translates to:
  /// **'Content Writing'**
  String get contentWriting;

  /// No description provided for @digitalMarketing.
  ///
  /// In en, this message translates to:
  /// **'Digital Marketing'**
  String get digitalMarketing;

  /// No description provided for @dataEntry.
  ///
  /// In en, this message translates to:
  /// **'Data Entry'**
  String get dataEntry;

  /// No description provided for @translation.
  ///
  /// In en, this message translates to:
  /// **'Translation'**
  String get translation;

  /// No description provided for @videoEditing.
  ///
  /// In en, this message translates to:
  /// **'Video Editing'**
  String get videoEditing;

  /// No description provided for @other.
  ///
  /// In en, this message translates to:
  /// **'Other'**
  String get other;

  /// No description provided for @pending.
  ///
  /// In en, this message translates to:
  /// **'PENDING'**
  String get pending;

  /// No description provided for @inProgress.
  ///
  /// In en, this message translates to:
  /// **'In Progress'**
  String get inProgress;

  /// No description provided for @completed.
  ///
  /// In en, this message translates to:
  /// **'Completed'**
  String get completed;

  /// No description provided for @cancelled.
  ///
  /// In en, this message translates to:
  /// **'Cancelled'**
  String get cancelled;

  /// No description provided for @normal.
  ///
  /// In en, this message translates to:
  /// **'Normal'**
  String get normal;

  /// No description provided for @urgent.
  ///
  /// In en, this message translates to:
  /// **'Urgent'**
  String get urgent;

  /// No description provided for @vip.
  ///
  /// In en, this message translates to:
  /// **'VIP'**
  String get vip;

  /// No description provided for @close.
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// No description provided for @viewDetails.
  ///
  /// In en, this message translates to:
  /// **'View Details'**
  String get viewDetails;

  /// No description provided for @edit.
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// No description provided for @postedTimeAgo.
  ///
  /// In en, this message translates to:
  /// **'Posted {time} ago'**
  String postedTimeAgo(String time);

  /// No description provided for @all.
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get all;

  /// No description provided for @clients.
  ///
  /// In en, this message translates to:
  /// **'Clients'**
  String get clients;

  /// No description provided for @freelancers.
  ///
  /// In en, this message translates to:
  /// **'Freelancers'**
  String get freelancers;

  /// No description provided for @addNewUser.
  ///
  /// In en, this message translates to:
  /// **'Add New User'**
  String get addNewUser;

  /// No description provided for @fullNameLabel.
  ///
  /// In en, this message translates to:
  /// **'Full Name'**
  String get fullNameLabel;

  /// No description provided for @role.
  ///
  /// In en, this message translates to:
  /// **'Role'**
  String get role;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @addUser.
  ///
  /// In en, this message translates to:
  /// **'Add User'**
  String get addUser;

  /// No description provided for @userAddedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'User added successfully!'**
  String get userAddedSuccessfully;

  /// No description provided for @noName.
  ///
  /// In en, this message translates to:
  /// **'No Name'**
  String get noName;

  /// No description provided for @joined.
  ///
  /// In en, this message translates to:
  /// **'Joined'**
  String get joined;

  /// No description provided for @status.
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get status;

  /// No description provided for @verified.
  ///
  /// In en, this message translates to:
  /// **'Verified'**
  String get verified;

  /// No description provided for @unverified.
  ///
  /// In en, this message translates to:
  /// **'Unverified'**
  String get unverified;

  /// No description provided for @rating.
  ///
  /// In en, this message translates to:
  /// **'Rating'**
  String get rating;

  /// No description provided for @client.
  ///
  /// In en, this message translates to:
  /// **'Client'**
  String get client;

  /// No description provided for @freelancer.
  ///
  /// In en, this message translates to:
  /// **'FREELANCER'**
  String get freelancer;

  /// No description provided for @admin.
  ///
  /// In en, this message translates to:
  /// **'ADMIN'**
  String get admin;

  /// No description provided for @noPendingPayments.
  ///
  /// In en, this message translates to:
  /// **'No pending payments'**
  String get noPendingPayments;

  /// No description provided for @allPaymentsProcessed.
  ///
  /// In en, this message translates to:
  /// **'All payments have been processed'**
  String get allPaymentsProcessed;

  /// No description provided for @clientLabel.
  ///
  /// In en, this message translates to:
  /// **'Client'**
  String get clientLabel;

  /// No description provided for @freelancerLabel.
  ///
  /// In en, this message translates to:
  /// **'Freelancer'**
  String get freelancerLabel;

  /// No description provided for @amount.
  ///
  /// In en, this message translates to:
  /// **'Amount'**
  String get amount;

  /// No description provided for @paymentProof.
  ///
  /// In en, this message translates to:
  /// **'Payment Proof'**
  String get paymentProof;

  /// No description provided for @submitted.
  ///
  /// In en, this message translates to:
  /// **'Submitted'**
  String get submitted;

  /// No description provided for @view.
  ///
  /// In en, this message translates to:
  /// **'View'**
  String get view;

  /// No description provided for @reject.
  ///
  /// In en, this message translates to:
  /// **'Reject'**
  String get reject;

  /// No description provided for @confirm.
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get confirm;

  /// No description provided for @confirmPayment.
  ///
  /// In en, this message translates to:
  /// **'Confirm Payment'**
  String get confirmPayment;

  /// No description provided for @confirmPaymentMessage.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to confirm this payment of \${amount}?\n\nThis will notify the freelancer to start working.'**
  String confirmPaymentMessage(Object amount);

  /// No description provided for @completedOrders.
  ///
  /// In en, this message translates to:
  /// **'Completed Orders'**
  String get completedOrders;

  /// No description provided for @cancelledOrders.
  ///
  /// In en, this message translates to:
  /// **'Cancelled Orders'**
  String get cancelledOrders;

  /// No description provided for @successRate.
  ///
  /// In en, this message translates to:
  /// **'Success Rate'**
  String get successRate;

  /// No description provided for @freelancerHub.
  ///
  /// In en, this message translates to:
  /// **'Freelancer Hub'**
  String get freelancerHub;

  /// No description provided for @searchRequests.
  ///
  /// In en, this message translates to:
  /// **'Search Requests'**
  String get searchRequests;

  /// No description provided for @enterKeywords.
  ///
  /// In en, this message translates to:
  /// **'Enter keywords...'**
  String get enterKeywords;

  /// No description provided for @search.
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// No description provided for @filterByPriority.
  ///
  /// In en, this message translates to:
  /// **'Filter by Priority'**
  String get filterByPriority;

  /// No description provided for @clear.
  ///
  /// In en, this message translates to:
  /// **'Clear'**
  String get clear;

  /// No description provided for @clearFilters.
  ///
  /// In en, this message translates to:
  /// **'Clear Filters'**
  String get clearFilters;

  /// No description provided for @priority.
  ///
  /// In en, this message translates to:
  /// **'Priority'**
  String get priority;

  /// No description provided for @due.
  ///
  /// In en, this message translates to:
  /// **'Due'**
  String get due;

  /// No description provided for @posted.
  ///
  /// In en, this message translates to:
  /// **'Posted'**
  String get posted;

  /// No description provided for @file.
  ///
  /// In en, this message translates to:
  /// **'file'**
  String get file;

  /// No description provided for @files.
  ///
  /// In en, this message translates to:
  /// **'files'**
  String get files;

  /// No description provided for @sendOffer.
  ///
  /// In en, this message translates to:
  /// **'Send Offer'**
  String get sendOffer;

  /// No description provided for @requestDetails.
  ///
  /// In en, this message translates to:
  /// **'Request Details'**
  String get requestDetails;

  /// No description provided for @clientInfo.
  ///
  /// In en, this message translates to:
  /// **'Client Information'**
  String get clientInfo;

  /// No description provided for @projectRequirements.
  ///
  /// In en, this message translates to:
  /// **'Project Requirements'**
  String get projectRequirements;

  /// No description provided for @attachedFiles.
  ///
  /// In en, this message translates to:
  /// **'Attached Files'**
  String get attachedFiles;

  /// No description provided for @download.
  ///
  /// In en, this message translates to:
  /// **'Download'**
  String get download;

  /// No description provided for @makeOffer.
  ///
  /// In en, this message translates to:
  /// **'Make an Offer'**
  String get makeOffer;

  /// No description provided for @yourOffer.
  ///
  /// In en, this message translates to:
  /// **'Your Offer'**
  String get yourOffer;

  /// No description provided for @offerAmount.
  ///
  /// In en, this message translates to:
  /// **'Offer Amount (SAR)'**
  String get offerAmount;

  /// No description provided for @enterAmount.
  ///
  /// In en, this message translates to:
  /// **'Enter amount'**
  String get enterAmount;

  /// No description provided for @deliveryTime.
  ///
  /// In en, this message translates to:
  /// **'Delivery Time'**
  String get deliveryTime;

  /// No description provided for @days.
  ///
  /// In en, this message translates to:
  /// **'days'**
  String get days;

  /// No description provided for @selectDays.
  ///
  /// In en, this message translates to:
  /// **'Select number of days'**
  String get selectDays;

  /// No description provided for @offerDescription.
  ///
  /// In en, this message translates to:
  /// **'Offer Description'**
  String get offerDescription;

  /// No description provided for @describeOffer.
  ///
  /// In en, this message translates to:
  /// **'Describe your offer and experience in this field...'**
  String get describeOffer;

  /// No description provided for @submitOffer.
  ///
  /// In en, this message translates to:
  /// **'Submit Offer'**
  String get submitOffer;

  /// No description provided for @offerSubmitted.
  ///
  /// In en, this message translates to:
  /// **'Offer Submitted Successfully!'**
  String get offerSubmitted;

  /// No description provided for @offerSubmittedMessage.
  ///
  /// In en, this message translates to:
  /// **'You will be notified when the client responds to your offer.'**
  String get offerSubmittedMessage;

  /// No description provided for @ok.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// No description provided for @myOffers.
  ///
  /// In en, this message translates to:
  /// **'My Offers'**
  String get myOffers;

  /// No description provided for @noOffersYet.
  ///
  /// In en, this message translates to:
  /// **'No offers yet'**
  String get noOffersYet;

  /// No description provided for @startBrowsing.
  ///
  /// In en, this message translates to:
  /// **'Start browsing requests and making offers'**
  String get startBrowsing;

  /// No description provided for @browseRequests.
  ///
  /// In en, this message translates to:
  /// **'Browse Requests'**
  String get browseRequests;

  /// No description provided for @offerFor.
  ///
  /// In en, this message translates to:
  /// **'Offer for'**
  String get offerFor;

  /// No description provided for @deliveryDays.
  ///
  /// In en, this message translates to:
  /// **'delivery days'**
  String get deliveryDays;

  /// No description provided for @offerStatus.
  ///
  /// In en, this message translates to:
  /// **'Offer Status'**
  String get offerStatus;

  /// No description provided for @accepted.
  ///
  /// In en, this message translates to:
  /// **'Accepted'**
  String get accepted;

  /// No description provided for @rejected.
  ///
  /// In en, this message translates to:
  /// **'Rejected'**
  String get rejected;

  /// No description provided for @withdrawn.
  ///
  /// In en, this message translates to:
  /// **'Withdrawn'**
  String get withdrawn;

  /// No description provided for @viewOffer.
  ///
  /// In en, this message translates to:
  /// **'View Offer'**
  String get viewOffer;

  /// No description provided for @withdrawOffer.
  ///
  /// In en, this message translates to:
  /// **'Withdraw Offer'**
  String get withdrawOffer;

  /// No description provided for @confirmWithdraw.
  ///
  /// In en, this message translates to:
  /// **'Confirm Withdrawal'**
  String get confirmWithdraw;

  /// No description provided for @withdrawOfferMessage.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to withdraw this offer? This action cannot be undone.'**
  String get withdrawOfferMessage;

  /// No description provided for @withdraw.
  ///
  /// In en, this message translates to:
  /// **'Withdraw'**
  String get withdraw;

  /// No description provided for @offerWithdrawn.
  ///
  /// In en, this message translates to:
  /// **'Offer Withdrawn'**
  String get offerWithdrawn;

  /// No description provided for @myJobs.
  ///
  /// In en, this message translates to:
  /// **'My Jobs'**
  String get myJobs;

  /// No description provided for @noActiveJobs.
  ///
  /// In en, this message translates to:
  /// **'No active jobs'**
  String get noActiveJobs;

  /// No description provided for @completeProjects.
  ///
  /// In en, this message translates to:
  /// **'Complete projects to build your reputation'**
  String get completeProjects;

  /// No description provided for @jobTitle.
  ///
  /// In en, this message translates to:
  /// **'Job Title'**
  String get jobTitle;

  /// No description provided for @dueDate.
  ///
  /// In en, this message translates to:
  /// **'Due Date'**
  String get dueDate;

  /// No description provided for @progress.
  ///
  /// In en, this message translates to:
  /// **'Progress'**
  String get progress;

  /// No description provided for @startWorking.
  ///
  /// In en, this message translates to:
  /// **'Start Working'**
  String get startWorking;

  /// No description provided for @deliverWork.
  ///
  /// In en, this message translates to:
  /// **'Deliver Work'**
  String get deliverWork;

  /// No description provided for @viewJob.
  ///
  /// In en, this message translates to:
  /// **'View Job'**
  String get viewJob;

  /// No description provided for @jobDetails.
  ///
  /// In en, this message translates to:
  /// **'Job Details'**
  String get jobDetails;

  /// No description provided for @deliveryInstructions.
  ///
  /// In en, this message translates to:
  /// **'Delivery Instructions'**
  String get deliveryInstructions;

  /// No description provided for @uploadDelivery.
  ///
  /// In en, this message translates to:
  /// **'Upload Delivery'**
  String get uploadDelivery;

  /// No description provided for @deliveryFiles.
  ///
  /// In en, this message translates to:
  /// **'Delivery Files'**
  String get deliveryFiles;

  /// No description provided for @addDeliveryFiles.
  ///
  /// In en, this message translates to:
  /// **'Add delivery files'**
  String get addDeliveryFiles;

  /// No description provided for @deliveryNotes.
  ///
  /// In en, this message translates to:
  /// **'Delivery Notes'**
  String get deliveryNotes;

  /// No description provided for @addNotes.
  ///
  /// In en, this message translates to:
  /// **'Add optional notes...'**
  String get addNotes;

  /// No description provided for @submitDelivery.
  ///
  /// In en, this message translates to:
  /// **'Submit Delivery'**
  String get submitDelivery;

  /// No description provided for @workDelivered.
  ///
  /// In en, this message translates to:
  /// **'Work Delivered!'**
  String get workDelivered;

  /// No description provided for @deliverySubmitted.
  ///
  /// In en, this message translates to:
  /// **'Your delivery has been submitted successfully. You will be notified when the client reviews it.'**
  String get deliverySubmitted;

  /// No description provided for @paymentPending.
  ///
  /// In en, this message translates to:
  /// **'Payment Pending'**
  String get paymentPending;

  /// No description provided for @paymentConfirmed.
  ///
  /// In en, this message translates to:
  /// **'Payment Confirmed'**
  String get paymentConfirmed;

  /// No description provided for @delivered.
  ///
  /// In en, this message translates to:
  /// **'Delivered'**
  String get delivered;

  /// No description provided for @chatWithClient.
  ///
  /// In en, this message translates to:
  /// **'Chat with Client'**
  String get chatWithClient;

  /// No description provided for @notifications.
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notifications;

  /// No description provided for @noNotifications.
  ///
  /// In en, this message translates to:
  /// **'No notifications'**
  String get noNotifications;

  /// No description provided for @noUnreadNotifications.
  ///
  /// In en, this message translates to:
  /// **'No unread notifications'**
  String get noUnreadNotifications;

  /// No description provided for @noReadNotifications.
  ///
  /// In en, this message translates to:
  /// **'No read notifications'**
  String get noReadNotifications;

  /// No description provided for @notificationsWillAppear.
  ///
  /// In en, this message translates to:
  /// **'Your notifications will appear here when they arrive'**
  String get notificationsWillAppear;

  /// No description provided for @unreadNotificationsWillAppear.
  ///
  /// In en, this message translates to:
  /// **'New notifications will appear here'**
  String get unreadNotificationsWillAppear;

  /// No description provided for @readNotificationsWillAppear.
  ///
  /// In en, this message translates to:
  /// **'Notifications you\'ve read will appear here'**
  String get readNotificationsWillAppear;

  /// No description provided for @markAllAsRead.
  ///
  /// In en, this message translates to:
  /// **'Mark all as read'**
  String get markAllAsRead;

  /// No description provided for @markAsRead.
  ///
  /// In en, this message translates to:
  /// **'Mark as read'**
  String get markAsRead;

  /// No description provided for @clearAll.
  ///
  /// In en, this message translates to:
  /// **'Clear all'**
  String get clearAll;

  /// No description provided for @clearAllNotifications.
  ///
  /// In en, this message translates to:
  /// **'Clear All Notifications'**
  String get clearAllNotifications;

  /// No description provided for @deleteNotification.
  ///
  /// In en, this message translates to:
  /// **'Delete Notification'**
  String get deleteNotification;

  /// No description provided for @areYouSureDeleteNotification.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this notification?'**
  String get areYouSureDeleteNotification;

  /// No description provided for @areYouSureClearAllNotifications.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to clear all notifications? This action cannot be undone.'**
  String get areYouSureClearAllNotifications;

  /// No description provided for @delete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// No description provided for @refresh.
  ///
  /// In en, this message translates to:
  /// **'Refresh'**
  String get refresh;

  /// No description provided for @searchNotifications.
  ///
  /// In en, this message translates to:
  /// **'Search notifications...'**
  String get searchNotifications;

  /// No description provided for @unread.
  ///
  /// In en, this message translates to:
  /// **'Unread'**
  String get unread;

  /// No description provided for @read.
  ///
  /// In en, this message translates to:
  /// **'Read'**
  String get read;

  /// No description provided for @unreadOnly.
  ///
  /// In en, this message translates to:
  /// **'Unread only'**
  String get unreadOnly;

  /// No description provided for @announcements.
  ///
  /// In en, this message translates to:
  /// **'Announcements'**
  String get announcements;

  /// No description provided for @applications.
  ///
  /// In en, this message translates to:
  /// **'Applications'**
  String get applications;

  /// No description provided for @now.
  ///
  /// In en, this message translates to:
  /// **'Now'**
  String get now;

  /// No description provided for @weeksAgo.
  ///
  /// In en, this message translates to:
  /// **'{count}w ago'**
  String weeksAgo(int count);

  /// No description provided for @orderAccepted.
  ///
  /// In en, this message translates to:
  /// **'Order Accepted'**
  String get orderAccepted;

  /// No description provided for @orderInProgress.
  ///
  /// In en, this message translates to:
  /// **'Order In Progress'**
  String get orderInProgress;

  /// No description provided for @orderDelivered.
  ///
  /// In en, this message translates to:
  /// **'Order Delivered'**
  String get orderDelivered;

  /// No description provided for @orderCancelled.
  ///
  /// In en, this message translates to:
  /// **'Order Cancelled'**
  String get orderCancelled;

  /// No description provided for @newMessage.
  ///
  /// In en, this message translates to:
  /// **'New Message'**
  String get newMessage;

  /// No description provided for @paymentReceived.
  ///
  /// In en, this message translates to:
  /// **'Payment Received'**
  String get paymentReceived;

  /// No description provided for @paymentConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Payment Confirmation'**
  String get paymentConfirmation;

  /// No description provided for @systemMaintenance.
  ///
  /// In en, this message translates to:
  /// **'System Maintenance'**
  String get systemMaintenance;

  /// No description provided for @newOrderAvailable.
  ///
  /// In en, this message translates to:
  /// **'New Order Available'**
  String get newOrderAvailable;

  /// No description provided for @chatMessages.
  ///
  /// In en, this message translates to:
  /// **'Messages'**
  String get chatMessages;

  /// No description provided for @noMessagesYet.
  ///
  /// In en, this message translates to:
  /// **'No messages yet'**
  String get noMessagesYet;

  /// No description provided for @typeMessage.
  ///
  /// In en, this message translates to:
  /// **'Type a message...'**
  String get typeMessage;

  /// No description provided for @sendMessage.
  ///
  /// In en, this message translates to:
  /// **'Send'**
  String get sendMessage;

  /// No description provided for @orderStatus.
  ///
  /// In en, this message translates to:
  /// **'Order Status'**
  String get orderStatus;

  /// No description provided for @payNow.
  ///
  /// In en, this message translates to:
  /// **'Pay Now'**
  String get payNow;

  /// No description provided for @viewDelivery.
  ///
  /// In en, this message translates to:
  /// **'View Delivery'**
  String get viewDelivery;

  /// No description provided for @approveDelivery.
  ///
  /// In en, this message translates to:
  /// **'Approve Delivery'**
  String get approveDelivery;

  /// No description provided for @requestRevision.
  ///
  /// In en, this message translates to:
  /// **'Request Revision'**
  String get requestRevision;

  /// No description provided for @cancelOrder.
  ///
  /// In en, this message translates to:
  /// **'Cancel Order'**
  String get cancelOrder;

  /// No description provided for @markComplete.
  ///
  /// In en, this message translates to:
  /// **'Mark Complete'**
  String get markComplete;

  /// No description provided for @revisionRequested.
  ///
  /// In en, this message translates to:
  /// **'Revision Requested'**
  String get revisionRequested;

  /// No description provided for @revisionNotes.
  ///
  /// In en, this message translates to:
  /// **'Revision Notes'**
  String get revisionNotes;

  /// No description provided for @enterRevisionNotes.
  ///
  /// In en, this message translates to:
  /// **'Please describe what needs to be revised...'**
  String get enterRevisionNotes;

  /// No description provided for @submitRevision.
  ///
  /// In en, this message translates to:
  /// **'Submit Revision Request'**
  String get submitRevision;

  /// No description provided for @revisionRequestSent.
  ///
  /// In en, this message translates to:
  /// **'Revision request sent successfully'**
  String get revisionRequestSent;

  /// No description provided for @editing.
  ///
  /// In en, this message translates to:
  /// **'Editing'**
  String get editing;

  /// No description provided for @orderCreated.
  ///
  /// In en, this message translates to:
  /// **'Created'**
  String get orderCreated;

  /// No description provided for @justNow.
  ///
  /// In en, this message translates to:
  /// **'Just now'**
  String get justNow;

  /// No description provided for @timelineOrderCreated.
  ///
  /// In en, this message translates to:
  /// **'Order #{orderId} created for \"{serviceTitle}\" - \\\${amount}'**
  String timelineOrderCreated(
    String orderId,
    String serviceTitle,
    String amount,
  );

  /// No description provided for @timelineOrderAccepted.
  ///
  /// In en, this message translates to:
  /// **'Order #{orderId} accepted by {freelancerName}'**
  String timelineOrderAccepted(String orderId, String freelancerName);

  /// No description provided for @timelinePaymentConfirmed.
  ///
  /// In en, this message translates to:
  /// **'Payment confirmed for Order #{orderId} - \\\${amount}'**
  String timelinePaymentConfirmed(String orderId, String amount);

  /// No description provided for @timelineWorkDelivered.
  ///
  /// In en, this message translates to:
  /// **'Work delivered for Order #{orderId}'**
  String timelineWorkDelivered(String orderId);

  /// No description provided for @timelineOrderCompleted.
  ///
  /// In en, this message translates to:
  /// **'Order #{orderId} completed successfully'**
  String timelineOrderCompleted(String orderId);

  /// No description provided for @timelineOrderCancelled.
  ///
  /// In en, this message translates to:
  /// **'Order #{orderId} cancelled - {reason}'**
  String timelineOrderCancelled(String orderId, String reason);

  /// No description provided for @timelineRevisionRequested.
  ///
  /// In en, this message translates to:
  /// **'Revision requested for Order #{orderId}'**
  String timelineRevisionRequested(String orderId);

  /// No description provided for @timelineDeadlineExtended.
  ///
  /// In en, this message translates to:
  /// **'Deadline extended for Order #{orderId}'**
  String timelineDeadlineExtended(String orderId);

  /// No description provided for @timelineMilestoneReached.
  ///
  /// In en, this message translates to:
  /// **'Milestone reached: {milestoneName}'**
  String timelineMilestoneReached(String milestoneName);

  /// No description provided for @messageSent.
  ///
  /// In en, this message translates to:
  /// **'Sent'**
  String get messageSent;

  /// No description provided for @messageDelivered.
  ///
  /// In en, this message translates to:
  /// **'Delivered'**
  String get messageDelivered;

  /// No description provided for @messageRead.
  ///
  /// In en, this message translates to:
  /// **'Read'**
  String get messageRead;

  /// No description provided for @attachFile.
  ///
  /// In en, this message translates to:
  /// **'Attach File'**
  String get attachFile;

  /// No description provided for @takePhoto.
  ///
  /// In en, this message translates to:
  /// **'Take Photo'**
  String get takePhoto;

  /// No description provided for @recordVoice.
  ///
  /// In en, this message translates to:
  /// **'Record Voice'**
  String get recordVoice;

  /// No description provided for @chatWith.
  ///
  /// In en, this message translates to:
  /// **'Chat with {name}'**
  String chatWith(String name);
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['ar', 'en'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return AppLocalizationsAr();
    case 'en':
      return AppLocalizationsEn();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}

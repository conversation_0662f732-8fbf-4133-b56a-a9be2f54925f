import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/language_provider.dart';
import '../../models/order_model.dart';

class ChatTopActionBar extends StatelessWidget {
  final OrderModel? order;
  final VoidCallback? onPayNow;
  final VoidCallback? onConfirmSubmission;
  final bool isClient;

  const ChatTopActionBar({super.key, this.order, this.onPayNow, this.onConfirmSubmission, this.isClient = true});

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);

    // Debug information
    print('=== DEBUG ChatTopActionBar ===');
    print('isClient: $isClient');
    print('order: ${order?.id}');
    print('order status: ${order?.status}');
    print('order payment status: ${order?.paymentStatus}');

    // Don't show the bar if not a client or no order
    if (!isClient || order == null) {
      print('Hiding bar: isClient=$isClient, order null=${order == null}');
      return const SizedBox.shrink();
    }

    // Determine which buttons to show based on order status
    final showPayNow = _shouldShowPayNow();
    final showConfirmSubmission = _shouldShowConfirmSubmission();

    print('showPayNow: $showPayNow');
    print('showConfirmSubmission: $showConfirmSubmission');

    // Don't show the bar if no buttons should be displayed
    if (!showPayNow && !showConfirmSubmission) {
      print('Hiding bar: no buttons to show');
      return const SizedBox.shrink();
    }

    print('Showing action bar with buttons');
    print('=== END DEBUG ChatTopActionBar ===');

    // Build the actual action buttons
    final List<Widget> buttons = [];
    final isArabic = languageProvider.isArabic;

    if (showPayNow) {
      buttons.add(
        _buildActionButton(
          label: isArabic ? 'ادفع الآن' : 'Pay Now',
          icon: Icons.payment,
          color: Colors.green,
          onTap: onPayNow,
          isArabic: isArabic,
        ),
      );
    }

    if (showConfirmSubmission) {
      buttons.add(
        _buildActionButton(
          label: isArabic ? 'تأكيد الاستلام' : 'Confirm Receiving',
          icon: Icons.check_circle,
          color: Colors.blue,
          onTap: onConfirmSubmission,
          isArabic: isArabic,
        ),
      );
    }

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(mainAxisAlignment: MainAxisAlignment.spaceEvenly, children: buttons),
      ),
    );
  }

  Widget _buildActionButton({
    required String label,
    required IconData icon,
    required Color color,
    required VoidCallback? onTap,
    required bool isArabic,
  }) {
    return ElevatedButton.icon(
      onPressed: onTap,
      icon: Icon(icon, size: 16),
      label: Text(label, style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600)),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        elevation: 2,
      ),
    );
  }

  bool _shouldShowPayNow() {
    if (order == null) return false;

    return order!.status == OrderStatus.created || order!.status == OrderStatus.paymentPending;
  }

  bool _shouldShowConfirmSubmission() {
    if (order == null) return false;

    return order!.status == OrderStatus.delivered;
  }
}

import '../models/payout_request_model.dart';
import '../models/freelancer_earnings_model.dart';

class PayoutService {
  static const double minimumWithdrawalAmount = 500.0;

  /// Get freelancer earnings and balance information
  static Future<FreelancerEarningsModel> getFreelancerEarnings(String freelancerId) async {
    try {
      // For demo purposes, return mock data
      // In production, this would fetch from the database
      return _getDemoEarnings(freelancerId);
    } catch (e) {
      throw Exception('Failed to fetch freelancer earnings: $e');
    }
  }

  /// Create a new payout request
  static Future<PayoutRequestModel> createPayoutRequest({
    required String freelancerId,
    required double amount,
    required PayoutMethod payoutMethod,
    required String accountDetails,
  }) async {
    try {
      // Validate minimum amount
      if (amount < minimumWithdrawalAmount) {
        throw Exception('Minimum withdrawal amount is SAR ${minimumWithdrawalAmount.toStringAsFixed(0)}');
      }

      // Check available balance
      final earnings = await getFreelancerEarnings(freelancerId);
      if (amount > earnings.availableBalance) {
        throw Exception('Insufficient balance. Available: SAR ${earnings.availableBalance.toStringAsFixed(2)}');
      }

      final payoutRequest = PayoutRequestModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        freelancerId: freelancerId,
        amount: amount,
        payoutMethod: payoutMethod,
        accountDetails: accountDetails,
        createdAt: DateTime.now(),
      );

      // For demo purposes, store in memory or return directly
      // In production, this would save to the database
      return payoutRequest;
    } catch (e) {
      throw Exception('Failed to create payout request: $e');
    }
  }

  /// Get payout requests for a freelancer
  static Future<List<PayoutRequestModel>> getPayoutRequests(String freelancerId) async {
    try {
      // For demo purposes, return mock data
      // In production, this would fetch from the database
      return _getDemoPayoutRequests(freelancerId);
    } catch (e) {
      throw Exception('Failed to fetch payout requests: $e');
    }
  }

  /// Get a specific payout request by ID
  static Future<PayoutRequestModel?> getPayoutRequest(String requestId) async {
    try {
      final requests = await getPayoutRequests('demo_freelancer_1');
      return requests.where((r) => r.id == requestId).firstOrNull;
    } catch (e) {
      return null;
    }
  }

  /// Demo data for freelancer earnings
  static FreelancerEarningsModel _getDemoEarnings(String freelancerId) {
    final now = DateTime.now();
    return FreelancerEarningsModel(
      freelancerId: freelancerId,
      totalEarnings: 12500.0,
      availableBalance: 2750.0,
      pendingBalance: 1200.0,
      withdrawnAmount: 8550.0,
      completedJobs: 25,
      averageJobValue: 500.0,
      lastUpdated: now,
      recentTransactions: [
        EarningTransaction(
          id: 'txn_1',
          freelancerId: freelancerId,
          type: TransactionType.earning,
          amount: 800.0,
          description: 'Payment for Mobile App Development',
          orderId: 'order_123',
          createdAt: now.subtract(const Duration(days: 2)),
        ),
        EarningTransaction(
          id: 'txn_2',
          freelancerId: freelancerId,
          type: TransactionType.withdrawal,
          amount: -1500.0,
          description: 'Withdrawal to Vodafone Cash',
          payoutRequestId: 'payout_456',
          createdAt: now.subtract(const Duration(days: 5)),
        ),
        EarningTransaction(
          id: 'txn_3',
          freelancerId: freelancerId,
          type: TransactionType.earning,
          amount: 1200.0,
          description: 'Payment for Website Design',
          orderId: 'order_789',
          createdAt: now.subtract(const Duration(days: 7)),
        ),
        EarningTransaction(
          id: 'txn_4',
          freelancerId: freelancerId,
          type: TransactionType.bonus,
          amount: 100.0,
          description: 'Performance bonus for excellent rating',
          createdAt: now.subtract(const Duration(days: 10)),
        ),
      ],
    );
  }

  /// Demo data for payout requests
  static List<PayoutRequestModel> _getDemoPayoutRequests(String freelancerId) {
    final now = DateTime.now();
    return [
      PayoutRequestModel(
        id: 'payout_1',
        freelancerId: freelancerId,
        amount: 1500.0,
        payoutMethod: PayoutMethod.vodafoneCash,
        accountDetails: '***********',
        status: PayoutRequestStatus.completed,
        createdAt: now.subtract(const Duration(days: 5)),
        processedAt: now.subtract(const Duration(days: 3)),
        processedByAdminId: 'admin_1',
      ),
      PayoutRequestModel(
        id: 'payout_2',
        freelancerId: freelancerId,
        amount: 800.0,
        payoutMethod: PayoutMethod.instaPay,
        accountDetails: '<EMAIL>',
        status: PayoutRequestStatus.pending,
        createdAt: now.subtract(const Duration(hours: 12)),
      ),
      PayoutRequestModel(
        id: 'payout_3',
        freelancerId: freelancerId,
        amount: 1200.0,
        payoutMethod: PayoutMethod.vodafoneCash,
        accountDetails: '***********',
        status: PayoutRequestStatus.rejected,
        rejectionReason: 'Invalid account details provided',
        createdAt: now.subtract(const Duration(days: 15)),
        processedAt: now.subtract(const Duration(days: 14)),
        processedByAdminId: 'admin_1',
      ),
    ];
  }
}

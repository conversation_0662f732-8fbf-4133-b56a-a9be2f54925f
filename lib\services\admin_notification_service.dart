import '../models/notification_model.dart';
import '../services/smart_notification_service.dart';
import '../config/supabase_config.dart';

/// Service for admin to send notifications and system announcements
class AdminNotificationService {
  /// Get all user IDs from the database
  static Future<List<String>> _getAllUserIds() async {
    try {
      final response = await SupabaseConfig.client.from('profiles').select('id');

      return (response as List).map((user) => user['id'] as String).toList();
    } catch (e) {
      print('Failed to get all user IDs: $e');
      return [];
    }
  }

  /// Get user IDs by role (client or freelancer)
  static Future<List<String>> _getUserIdsByRole(String role) async {
    try {
      final response = await SupabaseConfig.client.from('profiles').select('id').eq('role', role);

      return (response as List).map((user) => user['id'] as String).toList();
    } catch (e) {
      print('Failed to get user IDs by role: $e');
      return [];
    }
  }

  /// Send system announcement to all users
  static Future<void> sendSystemAnnouncement({
    required String titleEn,
    required String titleAr,
    required String messageEn,
    required String messageAr,
    NotificationPriority priority = NotificationPriority.normal,
    String? actionUrl,
  }) async {
    try {
      final userIds = await _getAllUserIds();

      await SmartNotificationService.sendSystemAnnouncement(
        userIds: userIds,
        titleEn: titleEn,
        titleAr: titleAr,
        messageEn: messageEn,
        messageAr: messageAr,
        priority: priority,
      );

      print('✅ System announcement sent to ${userIds.length} users');
    } catch (e) {
      print('❌ Failed to send system announcement: $e');
    }
  }

  /// Send announcement to specific user role (clients or freelancers)
  static Future<void> sendRoleBasedAnnouncement({
    required String role, // 'client' or 'freelancer'
    required String titleEn,
    required String titleAr,
    required String messageEn,
    required String messageAr,
    NotificationPriority priority = NotificationPriority.normal,
    String? actionUrl,
  }) async {
    try {
      final userIds = await _getUserIdsByRole(role);

      await SmartNotificationService.sendSystemAnnouncement(
        userIds: userIds,
        titleEn: titleEn,
        titleAr: titleAr,
        messageEn: messageEn,
        messageAr: messageAr,
        priority: priority,
      );

      print('✅ Role-based announcement sent to ${userIds.length} $role users');
    } catch (e) {
      print('❌ Failed to send role-based announcement: $e');
    }
  }

  /// Send custom message to specific user
  static Future<void> sendCustomMessage({
    required String userId,
    required String titleEn,
    required String titleAr,
    required String messageEn,
    required String messageAr,
    NotificationPriority priority = NotificationPriority.high,
    String? actionUrl,
  }) async {
    try {
      await SmartNotificationService.sendAdminMessage(
        userId: userId,
        titleEn: titleEn,
        titleAr: titleAr,
        messageEn: messageEn,
        messageAr: messageAr,
        priority: priority,
        actionUrl: actionUrl,
      );

      print('✅ Custom admin message sent to user $userId');
    } catch (e) {
      print('❌ Failed to send custom admin message: $e');
    }
  }

  /// Send maintenance notice
  static Future<void> sendMaintenanceNotice({
    required DateTime scheduledTime,
    required int durationMinutes,
    String? additionalInfoEn,
    String? additionalInfoAr,
  }) async {
    try {
      final userIds = await _getAllUserIds();
      final timeStr =
          '${scheduledTime.day}/${scheduledTime.month}/${scheduledTime.year} at ${scheduledTime.hour}:${scheduledTime.minute.toString().padLeft(2, '0')}';

      final additionalText = additionalInfoEn != null ? ' $additionalInfoEn' : '';
      final additionalTextAr = additionalInfoAr != null ? ' $additionalInfoAr' : '';

      for (final userId in userIds) {
        await SmartNotificationService.createSmartNotification(
          userId: userId,
          titleEn: 'Scheduled Maintenance',
          titleAr: 'صيانة مجدولة',
          descriptionEn:
              'System maintenance is scheduled for $timeStr and will last approximately $durationMinutes minutes.$additionalText',
          descriptionAr: 'صيانة النظام مجدولة في $timeStr وستستغرق حوالي $durationMinutes دقيقة.$additionalTextAr',
          type: NotificationType.maintenanceNotice,
          priority: NotificationPriority.high,
          metadata: {'scheduled_time': scheduledTime.toIso8601String(), 'duration_minutes': durationMinutes},
        );
      }

      print('✅ Maintenance notice sent to ${userIds.length} users');
    } catch (e) {
      print('❌ Failed to send maintenance notice: $e');
    }
  }

  /// Send system update notification
  static Future<void> sendSystemUpdateNotification({
    required String version,
    required List<String> newFeaturesEn,
    required List<String> newFeaturesAr,
    List<String>? bugFixesEn,
    List<String>? bugFixesAr,
  }) async {
    try {
      final userIds = await _getAllUserIds();

      final featuresTextEn = newFeaturesEn.map((f) => '• $f').join('\n');
      final featuresTextAr = newFeaturesAr.map((f) => '• $f').join('\n');

      final bugFixesTextEn = bugFixesEn != null ? '\n\nBug Fixes:\n${bugFixesEn.map((f) => '• $f').join('\n')}' : '';
      final bugFixesTextAr =
          bugFixesAr != null ? '\n\nإصلاح الأخطاء:\n${bugFixesAr.map((f) => '• $f').join('\n')}' : '';

      for (final userId in userIds) {
        await SmartNotificationService.createSmartNotification(
          userId: userId,
          titleEn: 'App Updated to v$version',
          titleAr: 'تم تحديث التطبيق إلى الإصدار $version',
          descriptionEn: 'New Features:\n$featuresTextEn$bugFixesTextEn',
          descriptionAr: 'الميزات الجديدة:\n$featuresTextAr$bugFixesTextAr',
          type: NotificationType.systemUpdate,
          priority: NotificationPriority.normal,
          metadata: {'version': version, 'new_features': newFeaturesEn, 'bug_fixes': bugFixesEn},
        );
      }

      print('✅ System update notification sent to ${userIds.length} users');
    } catch (e) {
      print('❌ Failed to send system update notification: $e');
    }
  }

  /// Send account verification reminder to unverified users
  static Future<void> sendVerificationReminder() async {
    try {
      // This would query for unverified users
      final response = await SupabaseConfig.client.from('profiles').select('id').eq('is_verified', false);

      final unverifiedUserIds = (response as List).map((user) => user['id'] as String).toList();

      for (final userId in unverifiedUserIds) {
        await SmartNotificationService.createSmartNotification(
          userId: userId,
          titleEn: 'Verify Your Account',
          titleAr: 'تحقق من حسابك',
          descriptionEn: 'Please verify your account to access all features and build trust with other users.',
          descriptionAr: 'يرجى التحقق من حسابك للوصول إلى جميع الميزات وبناء الثقة مع المستخدمين الآخرين.',
          type: NotificationType.accountVerification,
          priority: NotificationPriority.high,
          actionUrl: '/profile/verify',
        );
      }

      print('✅ Verification reminder sent to ${unverifiedUserIds.length} unverified users');
    } catch (e) {
      print('❌ Failed to send verification reminder: $e');
    }
  }

  /// Send promotional notification
  static Future<void> sendPromotionalNotification({
    required String titleEn,
    required String titleAr,
    required String messageEn,
    required String messageAr,
    String? targetRole, // null for all users, 'client' or 'freelancer' for specific role
    String? actionUrl,
    DateTime? expiryDate,
  }) async {
    try {
      final userIds = targetRole != null ? await _getUserIdsByRole(targetRole) : await _getAllUserIds();

      for (final userId in userIds) {
        await SmartNotificationService.createSmartNotification(
          userId: userId,
          titleEn: titleEn,
          titleAr: titleAr,
          descriptionEn: messageEn,
          descriptionAr: messageAr,
          type: NotificationType.systemAnnouncement,
          priority: NotificationPriority.normal,
          actionUrl: actionUrl,
          metadata: {'is_promotional': true, 'target_role': targetRole, 'expiry_date': expiryDate?.toIso8601String()},
        );
      }

      final targetText = targetRole != null ? '$targetRole users' : 'all users';
      print('✅ Promotional notification sent to ${userIds.length} $targetText');
    } catch (e) {
      print('❌ Failed to send promotional notification: $e');
    }
  }

  /// Send warning to specific user
  static Future<void> sendUserWarning({
    required String userId,
    required String reason,
    required String warningLevel, // 'low', 'medium', 'high'
    String? actionRequired,
  }) async {
    try {
      final priority =
          warningLevel == 'high'
              ? NotificationPriority.urgent
              : warningLevel == 'medium'
              ? NotificationPriority.high
              : NotificationPriority.normal;

      final actionText = actionRequired != null ? ' Action required: $actionRequired' : '';
      final actionTextAr = actionRequired != null ? ' الإجراء المطلوب: $actionRequired' : '';

      await SmartNotificationService.createSmartNotification(
        userId: userId,
        titleEn: 'Account Warning',
        titleAr: 'تحذير الحساب',
        descriptionEn: 'Warning: $reason.$actionText',
        descriptionAr: 'تحذير: $reason.$actionTextAr',
        type: NotificationType.adminMessage,
        priority: priority,
        metadata: {'warning_level': warningLevel, 'reason': reason, 'action_required': actionRequired},
        actionUrl: '/profile/warnings',
      );

      print('✅ Warning sent to user $userId (Level: $warningLevel)');
    } catch (e) {
      print('❌ Failed to send user warning: $e');
    }
  }

  /// Get notification sending statistics
  static Map<String, dynamic> getAdminNotificationStats() {
    return {
      'service_active': true,
      'last_system_announcement': DateTime.now().toIso8601String(),
      'supported_notification_types': [
        'system_announcement',
        'maintenance_notice',
        'system_update',
        'custom_message',
        'verification_reminder',
        'promotional',
        'user_warning',
      ],
    };
  }
}

enum MessageType {
  text,
  image,
  file,
  system,
  timeline,
  deliveryConfirmation,
  receiptConfirmation,
  autoReminder,
  ratingRequest,
}

enum MessageStatus { sent, delivered, read }

class MessageModel {
  final String id;
  final String chatId;
  final String senderId;
  final String content;
  final MessageType type;
  final String? fileUrl;
  final String? fileName;
  final bool isRead;
  final MessageStatus status;
  final DateTime createdAt;
  final DateTime? deliveredAt;
  final DateTime? readAt;
  final Map<String, dynamic>? metadata; // For timeline messages and additional data

  MessageModel({
    required this.id,
    required this.chatId,
    required this.senderId,
    required this.content,
    this.type = MessageType.text,
    this.fileUrl,
    this.fileName,
    this.isRead = false,
    this.status = MessageStatus.sent,
    required this.createdAt,
    this.deliveredAt,
    this.readAt,
    this.metadata,
  });

  factory MessageModel.fromJson(Map<String, dynamic> json) {
    return MessageModel(
      id: json['id'],
      chatId: json['chat_id'],
      senderId: json['sender_id'],
      content: json['content'],
      type: MessageType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => MessageType.text,
      ),
      fileUrl: json['file_url'],
      fileName: json['file_name'],
      isRead: json['is_read'] ?? false,
      status: MessageStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => MessageStatus.sent,
      ),
      createdAt: DateTime.parse(json['created_at']),
      deliveredAt: json['delivered_at'] != null ? DateTime.parse(json['delivered_at']) : null,
      readAt: json['read_at'] != null ? DateTime.parse(json['read_at']) : null,
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'chat_id': chatId,
      'sender_id': senderId,
      'content': content,
      'type': type.toString().split('.').last,
      'file_url': fileUrl,
      'file_name': fileName,
      'is_read': isRead,
      'status': status.toString().split('.').last,
      'created_at': createdAt.toIso8601String(),
      'delivered_at': deliveredAt?.toIso8601String(),
      'read_at': readAt?.toIso8601String(),
      'metadata': metadata,
    };
  }

  MessageModel copyWith({
    String? id,
    String? chatId,
    String? senderId,
    String? content,
    MessageType? type,
    String? fileUrl,
    String? fileName,
    bool? isRead,
    MessageStatus? status,
    DateTime? createdAt,
    DateTime? deliveredAt,
    DateTime? readAt,
    Map<String, dynamic>? metadata,
  }) {
    return MessageModel(
      id: id ?? this.id,
      chatId: chatId ?? this.chatId,
      senderId: senderId ?? this.senderId,
      content: content ?? this.content,
      type: type ?? this.type,
      fileUrl: fileUrl ?? this.fileUrl,
      fileName: fileName ?? this.fileName,
      isRead: isRead ?? this.isRead,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      deliveredAt: deliveredAt ?? this.deliveredAt,
      readAt: readAt ?? this.readAt,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Mark message as read
  MessageModel markAsRead() {
    return copyWith(isRead: true, status: MessageStatus.read, readAt: DateTime.now());
  }

  /// Mark message as delivered
  MessageModel markAsDelivered() {
    return copyWith(status: MessageStatus.delivered, deliveredAt: DateTime.now());
  }

  /// Check if this is a timeline message
  bool get isTimelineMessage => type == MessageType.timeline;

  /// Check if this is a system message
  bool get isSystemMessage => type == MessageType.system || type == MessageType.timeline;
}

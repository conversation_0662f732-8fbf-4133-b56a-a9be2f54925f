// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'منصة العمل الحر';

  @override
  String get welcomeBack => 'مرحباً بعودتك';

  @override
  String get signInToContinue => 'سجل دخولك للمتابعة إلى منصة العمل الحر';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get password => 'كلمة المرور';

  @override
  String get signIn => 'تسجيل الدخول';

  @override
  String get signUp => 'إنشاء حساب';

  @override
  String get dontHaveAccount => 'ليس لديك حساب؟';

  @override
  String get alreadyHaveAccount => 'لديك حساب بالفعل؟';

  @override
  String get createAccount => 'إنشاء حساب';

  @override
  String get joinFreelanceHub => 'انضم إلى منصة العمل الحر';

  @override
  String get createYourAccount => 'أنشئ حسابك للبدء';

  @override
  String get fullName => 'الاسم الكامل';

  @override
  String get iWantTo => 'أريد أن';

  @override
  String get hireFreelancers => 'أوظف مستقلين (عميل)';

  @override
  String get offerServices => 'أقدم خدمات (مستقل)';

  @override
  String get confirmPassword => 'تأكيد كلمة المرور';

  @override
  String get quickDemoLogin => 'تسجيل دخول سريع للتجربة';

  @override
  String get loginAsClient => 'دخول كعميل';

  @override
  String get loginAsFreelancer => 'دخول كمستقل';

  @override
  String get loginAsAdmin => 'دخول كمدير';

  @override
  String get dashboard => 'لوحة التحكم';

  @override
  String get requests => 'الطلبات';

  @override
  String get orders => 'الطلبات';

  @override
  String get messages => 'الرسائل';

  @override
  String get browse => 'تصفح';

  @override
  String get offers => 'العروض';

  @override
  String get jobs => 'المهام';

  @override
  String get payments => 'المدفوعات';

  @override
  String get users => 'المستخدمون';

  @override
  String welcomeBackUser(String name) {
    return 'مرحباً بعودتك، $name!';
  }

  @override
  String get readyToFindFreelancer =>
      'هل أنت مستعد للعثور على المستقل المثالي لمشروعك؟';

  @override
  String get postNewRequest => 'نشر طلب جديد';

  @override
  String get activeOrders => 'الطلبات النشطة';

  @override
  String get totalRequests => 'إجمالي الطلبات';

  @override
  String get recentRequests => 'الطلبات الأخيرة';

  @override
  String get noRequestsYet => 'لا توجد طلبات بعد';

  @override
  String get createFirstRequest => 'أنشئ أول طلب خدمة للبدء';

  @override
  String welcomeFreelancer(String name) {
    return 'مرحباً، $name!';
  }

  @override
  String get findNextOpportunity =>
      'ابحث عن فرصتك التالية وطور مسيرتك المهنية في العمل الحر.';

  @override
  String get browseJobs => 'تصفح الوظائف';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get activeJobs => 'الوظائف النشطة';

  @override
  String get pendingOffers => 'العروض المعلقة';

  @override
  String get latestOpportunities => 'أحدث الفرص';

  @override
  String get noOpportunitiesAvailable => 'لا توجد فرص متاحة';

  @override
  String get checkBackLater =>
      'تحقق مرة أخرى لاحقاً للحصول على طلبات مشاريع جديدة';

  @override
  String get recentOffers => 'العروض الأخيرة';

  @override
  String get adminControlCenter => 'مركز التحكم الإداري';

  @override
  String get managePlatform =>
      'إدارة المستخدمين، التحقق من المدفوعات، والإشراف على عمليات المنصة.';

  @override
  String get totalUsers => 'إجمالي المستخدمين';

  @override
  String get totalOrders => 'إجمالي الطلبات';

  @override
  String get revenue => 'الإيرادات';

  @override
  String get paymentVerificationRequired => 'مطلوب التحقق من الدفع';

  @override
  String paymentsAwaitingVerification(int count) {
    return '$count مدفوعات في انتظار التحقق';
  }

  @override
  String get review => 'مراجعة';

  @override
  String get quickActions => 'إجراءات سريعة';

  @override
  String get verifyPayments => 'التحقق من المدفوعات';

  @override
  String get manageUsers => 'إدارة المستخدمين';

  @override
  String get orderManagement => 'إدارة الطلبات';

  @override
  String get systemMessages => 'رسائل النظام';

  @override
  String get recentActivity => 'النشاط الأخير';

  @override
  String get newUserRegistration => 'تسجيل مستخدم جديد';

  @override
  String joinedAsFreelancer(String email) {
    return '$email انضم كمستقل';
  }

  @override
  String get paymentSubmitted => 'تم إرسال الدفع';

  @override
  String get orderCompleted => 'تم إكمال الطلب';

  @override
  String orderMarkedCompleted(String orderId) {
    return 'تم وضع علامة مكتمل على الطلب #$orderId';
  }

  @override
  String minutesAgo(int count) {
    return 'منذ $count دقيقة';
  }

  @override
  String hoursAgo(int count) {
    return 'منذ $count ساعة';
  }

  @override
  String daysAgo(int count) {
    return 'منذ $count يوم';
  }

  @override
  String get logout => 'تسجيل الخروج';

  @override
  String get settings => 'الإعدادات';

  @override
  String get language => 'اللغة';

  @override
  String get english => 'English';

  @override
  String get arabic => 'العربية';

  @override
  String get darkMode => 'الوضع المظلم';

  @override
  String get lightMode => 'الوضع المضيء';

  @override
  String get projectTitle => 'عنوان المشروع';

  @override
  String get whatDoYouNeed => 'ما الذي تحتاجه؟';

  @override
  String get category => 'الفئة';

  @override
  String get projectDescription => 'وصف المشروع';

  @override
  String get describeProject => 'اوصف مشروعك بالتفصيل...';

  @override
  String get budget => 'الميزانية (ريال سعودي)';

  @override
  String get enterBudget => 'أدخل ميزانيتك (اختياري)';

  @override
  String get deadline => 'الموعد النهائي';

  @override
  String get selectDeadline => 'اختر الموعد النهائي';

  @override
  String get attachments => 'المرفقات';

  @override
  String get addFiles => 'إضافة ملفات';

  @override
  String get canAttachFiles => 'يمكنك إرفاق ملفات أو صور أو مستندات ذات صلة';

  @override
  String get post => 'نشر';

  @override
  String get webDevelopment => 'تطوير المواقع';

  @override
  String get mobileDevelopment => 'تطوير التطبيقات';

  @override
  String get graphicDesign => 'التصميم الجرافيكي';

  @override
  String get contentWriting => 'كتابة المحتوى';

  @override
  String get digitalMarketing => 'التسويق الرقمي';

  @override
  String get dataEntry => 'إدخال البيانات';

  @override
  String get translation => 'الترجمة';

  @override
  String get videoEditing => 'تحرير الفيديو';

  @override
  String get other => 'أخرى';

  @override
  String get pending => 'معلق';

  @override
  String get inProgress => 'قيد التنفيذ';

  @override
  String get completed => 'مكتمل';

  @override
  String get cancelled => 'ملغي';

  @override
  String get normal => 'عادي';

  @override
  String get urgent => 'عاجل';

  @override
  String get vip => 'مميز';

  @override
  String get close => 'إغلاق';

  @override
  String get viewDetails => 'عرض التفاصيل';

  @override
  String get edit => 'تعديل';

  @override
  String postedTimeAgo(String time) {
    return 'تم النشر منذ $time';
  }

  @override
  String get all => 'الكل';

  @override
  String get clients => 'العملاء';

  @override
  String get freelancers => 'المستقلون';

  @override
  String get addNewUser => 'إضافة مستخدم جديد';

  @override
  String get fullNameLabel => 'الاسم الكامل';

  @override
  String get role => 'الدور';

  @override
  String get cancel => 'إلغاء';

  @override
  String get addUser => 'إضافة مستخدم';

  @override
  String get userAddedSuccessfully => 'تم إضافة المستخدم بنجاح!';

  @override
  String get noName => 'بلا اسم';

  @override
  String get joined => 'انضم';

  @override
  String get status => 'الحالة';

  @override
  String get verified => 'موثق';

  @override
  String get unverified => 'غير موثق';

  @override
  String get rating => 'التقييم';

  @override
  String get client => 'العميل';

  @override
  String get freelancer => 'مستقل';

  @override
  String get admin => 'مدير';

  @override
  String get noPendingPayments => 'لا توجد مدفوعات معلقة';

  @override
  String get allPaymentsProcessed => 'تم معالجة جميع المدفوعات';

  @override
  String get clientLabel => 'العميل';

  @override
  String get freelancerLabel => 'المستقل';

  @override
  String get amount => 'المبلغ';

  @override
  String get paymentProof => 'إثبات الدفع';

  @override
  String get submitted => 'تم الإرسال';

  @override
  String get view => 'عرض';

  @override
  String get reject => 'رفض';

  @override
  String get confirm => 'تأكيد';

  @override
  String get confirmPayment => 'تأكيد الدفع';

  @override
  String confirmPaymentMessage(Object amount) {
    return 'هل أنت متأكد من تأكيد هذا الدفع بقيمة \$$amount؟\n\nسيتم إشعار المستقل للبدء في العمل.';
  }

  @override
  String get completedOrders => 'الطلبات المكتملة';

  @override
  String get cancelledOrders => 'الطلبات الملغية';

  @override
  String get successRate => 'معدل النجاح';

  @override
  String get freelancerHub => 'منصة المستقلين';

  @override
  String get searchRequests => 'البحث في الطلبات';

  @override
  String get enterKeywords => 'أدخل الكلمات المفتاحية...';

  @override
  String get search => 'بحث';

  @override
  String get filterByPriority => 'تصفية حسب الأولوية';

  @override
  String get clear => 'مسح';

  @override
  String get clearFilters => 'مسح الفلاتر';

  @override
  String get priority => 'الأولوية';

  @override
  String get due => 'الموعد النهائي';

  @override
  String get posted => 'تم النشر';

  @override
  String get file => 'ملف';

  @override
  String get files => 'ملفات';

  @override
  String get sendOffer => 'إرسال عرض';

  @override
  String get requestDetails => 'تفاصيل الطلب';

  @override
  String get clientInfo => 'معلومات العميل';

  @override
  String get projectRequirements => 'متطلبات المشروع';

  @override
  String get attachedFiles => 'الملفات المرفقة';

  @override
  String get download => 'تحميل';

  @override
  String get makeOffer => 'تقديم عرض';

  @override
  String get yourOffer => 'عرضك';

  @override
  String get offerAmount => 'مبلغ العرض (دولار أمريكي)';

  @override
  String get enterAmount => 'أدخل المبلغ';

  @override
  String get deliveryTime => 'مدة التسليم';

  @override
  String get days => 'أيام';

  @override
  String get selectDays => 'اختر عدد الأيام';

  @override
  String get offerDescription => 'وصف العرض';

  @override
  String get describeOffer => 'اصف عرضك وخبرتك في هذا المجال...';

  @override
  String get submitOffer => 'إرسال العرض';

  @override
  String get offerSubmitted => 'تم إرسال العرضك بنجاح!';

  @override
  String get offerSubmittedMessage => 'سيتم إشعارك عند رد العميل على عرضك.';

  @override
  String get ok => 'موافق';

  @override
  String get myOffers => 'عروضي';

  @override
  String get noOffersYet => 'لا توجد عروض بعد';

  @override
  String get startBrowsing => 'ابدأ بتصفح الطلبات وتقديم عروضك';

  @override
  String get browseRequests => 'تصفح الطلبات';

  @override
  String get offerFor => 'عرض لـ';

  @override
  String get deliveryDays => 'أيام التسليم';

  @override
  String get offerStatus => 'حالة العرض';

  @override
  String get accepted => 'مقبول';

  @override
  String get rejected => 'مرفوض';

  @override
  String get withdrawn => 'مسحوب';

  @override
  String get viewOffer => 'عرض العرض';

  @override
  String get withdrawOffer => 'سحب العرض';

  @override
  String get confirmWithdraw => 'تأكيد السحب';

  @override
  String get withdrawOfferMessage =>
      'هل أنت متأكد من سحب هذا العرض؟ لن تتمكن من التراجع عن هذا الإجراء.';

  @override
  String get withdraw => 'سحب';

  @override
  String get offerWithdrawn => 'تم سحب العرض';

  @override
  String get myJobs => 'وظائفي';

  @override
  String get noActiveJobs => 'لا توجد وظائف نشطة';

  @override
  String get completeProjects => 'أكمل المشاريع لبناء سمعتك';

  @override
  String get jobTitle => 'عنوان الوظيفة';

  @override
  String get dueDate => 'تاريخ الاستحقاق';

  @override
  String get progress => 'التقدم';

  @override
  String get startWorking => 'بدء العمل';

  @override
  String get deliverWork => 'تسليم العمل';

  @override
  String get viewJob => 'عرض الوظيفة';

  @override
  String get jobDetails => 'تفاصيل الوظيفة';

  @override
  String get deliveryInstructions => 'تعليمات التسليم';

  @override
  String get uploadDelivery => 'رفع التسليم';

  @override
  String get deliveryFiles => 'ملفات التسليم';

  @override
  String get addDeliveryFiles => 'إضافة ملفات التسليم';

  @override
  String get deliveryNotes => 'ملاحظات التسليم';

  @override
  String get addNotes => 'أضف ملاحظات اختيارية...';

  @override
  String get submitDelivery => 'إرسال التسليم';

  @override
  String get workDelivered => 'تم تسليم العمل!';

  @override
  String get deliverySubmitted =>
      'تم إرسال تسليمك بنجاح. سيتم إشعارك عند مراجعة العميل.';

  @override
  String get paymentPending => 'في انتظار الدفع';

  @override
  String get paymentConfirmed => 'تم تأكيد الدفع';

  @override
  String get delivered => 'تم التسليم';

  @override
  String get chatWithClient => 'محادثة مع العميل';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get noNotifications => 'لا توجد إشعارات';

  @override
  String get noUnreadNotifications => 'لا توجد إشعارات غير مقروءة';

  @override
  String get noReadNotifications => 'لا توجد إشعارات مقروءة';

  @override
  String get notificationsWillAppear => 'ستظهر إشعاراتك هنا عند وصولها';

  @override
  String get unreadNotificationsWillAppear => 'ستظهر الإشعارات الجديدة هنا';

  @override
  String get readNotificationsWillAppear => 'الإشعارات التي قرأتها ستظهر هنا';

  @override
  String get markAllAsRead => 'تحديد الكل كمقروء';

  @override
  String get markAsRead => 'تحديد كمقروء';

  @override
  String get clearAll => 'مسح الكل';

  @override
  String get clearAllNotifications => 'مسح جميع الإشعارات';

  @override
  String get deleteNotification => 'حذف الإشعار';

  @override
  String get areYouSureDeleteNotification =>
      'هل أنت متأكد من أنك تريد حذف هذا الإشعار؟';

  @override
  String get areYouSureClearAllNotifications =>
      'هل أنت متأكد من أنك تريد مسح جميع الإشعارات؟ لا يمكن التراجع عن هذا الإجراء.';

  @override
  String get delete => 'حذف';

  @override
  String get refresh => 'تحديث';

  @override
  String get searchNotifications => 'البحث في الإشعارات...';

  @override
  String get unread => 'غير مقروء';

  @override
  String get read => 'مقروء';

  @override
  String get unreadOnly => 'غير مقروء فقط';

  @override
  String get announcements => 'الإعلانات';

  @override
  String get applications => 'التطبيقات';

  @override
  String get now => 'الآن';

  @override
  String weeksAgo(int count) {
    return 'منذ $count أسبوع';
  }

  @override
  String get orderAccepted => 'تم قبول الطلب';

  @override
  String get orderInProgress => 'الطلب قيد التنفيذ';

  @override
  String get orderDelivered => 'تم تسليم الطلب';

  @override
  String get orderCancelled => 'تم إلغاء الطلب';

  @override
  String get newMessage => 'رسالة جديدة';

  @override
  String get paymentReceived => 'تم استلام الدفع';

  @override
  String get paymentConfirmation => 'تأكيد الدفع';

  @override
  String get systemMaintenance => 'صيانة النظام';

  @override
  String get newOrderAvailable => 'طلب جديد متاح';

  @override
  String get chatMessages => 'الرسائل';

  @override
  String get noMessagesYet => 'لا توجد رسائل بعد';

  @override
  String get typeMessage => 'اكتب رسالة...';

  @override
  String get sendMessage => 'إرسال';

  @override
  String get orderStatus => 'حالة الطلب';

  @override
  String get payNow => 'ادفع الآن';

  @override
  String get viewDelivery => 'عرض التسليم';

  @override
  String get approveDelivery => 'قبول التسليم';

  @override
  String get requestRevision => 'طلب مراجعة';

  @override
  String get cancelOrder => 'إلغاء الطلب';

  @override
  String get markComplete => 'تحديد كمكتمل';

  @override
  String get revisionRequested => 'تم طلب مراجعة';

  @override
  String get revisionNotes => 'ملاحظات المراجعة';

  @override
  String get enterRevisionNotes => 'يرجى وصف ما يحتاج إلى مراجعة...';

  @override
  String get submitRevision => 'إرسال طلب المراجعة';

  @override
  String get revisionRequestSent => 'تم إرسال طلب المراجعة بنجاح';

  @override
  String get editing => 'قيد التعديل';

  @override
  String get orderCreated => 'تم الإنشاء';

  @override
  String get justNow => 'الآن';

  @override
  String timelineOrderCreated(
    String orderId,
    String serviceTitle,
    String amount,
  ) {
    return 'تم إنشاء الطلب #$orderId لـ \"$serviceTitle\" - \\\$$amount';
  }

  @override
  String timelineOrderAccepted(String orderId, String freelancerName) {
    return 'تم قبول الطلب #$orderId من قبل $freelancerName';
  }

  @override
  String timelinePaymentConfirmed(String orderId, String amount) {
    return 'تم تأكيد الدفع للطلب #$orderId - \\\$$amount';
  }

  @override
  String timelineWorkDelivered(String orderId) {
    return 'تم تسليم العمل للطلب #$orderId';
  }

  @override
  String timelineOrderCompleted(String orderId) {
    return 'تم إكمال الطلب #$orderId بنجاح';
  }

  @override
  String timelineOrderCancelled(String orderId, String reason) {
    return 'تم إلغاء الطلب #$orderId - $reason';
  }

  @override
  String timelineRevisionRequested(String orderId) {
    return 'تم طلب مراجعة للطلب #$orderId';
  }

  @override
  String timelineDeadlineExtended(String orderId) {
    return 'تم تمديد الموعد النهائي للطلب #$orderId';
  }

  @override
  String timelineMilestoneReached(String milestoneName) {
    return 'تم الوصول إلى المعلم: $milestoneName';
  }

  @override
  String get messageSent => 'تم الإرسال';

  @override
  String get messageDelivered => 'تم التسليم';

  @override
  String get messageRead => 'تم القراءة';

  @override
  String get attachFile => 'إرفاق ملف';

  @override
  String get takePhoto => 'التقاط صورة';

  @override
  String get recordVoice => 'تسجيل صوتي';

  @override
  String chatWith(String name) {
    return 'محادثة مع $name';
  }
}

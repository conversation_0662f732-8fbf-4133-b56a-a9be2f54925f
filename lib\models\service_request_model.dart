enum RequestStatus { pending, inProgress, completed, cancelled }
enum Priority { normal, urgent, vip }

class ServiceRequestModel {
  final String id;
  final String clientId;
  final String title;
  final String description;
  final double? budget;
  final List<String>? attachments;
  final RequestStatus status;
  final Priority priority;
  final String? assignedFreelancerId;
  final DateTime deadline;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final List<String>? tags;
  final String? category;

  ServiceRequestModel({
    required this.id,
    required this.clientId,
    required this.title,
    required this.description,
    this.budget,
    this.attachments,
    this.status = RequestStatus.pending,
    this.priority = Priority.normal,
    this.assignedFreelancerId,
    required this.deadline,
    required this.createdAt,
    this.updatedAt,
    this.tags,
    this.category,
  });

  factory ServiceRequestModel.fromJson(Map<String, dynamic> json) {
    return ServiceRequestModel(
      id: json['id'],
      clientId: json['client_id'],
      title: json['title'],
      description: json['description'],
      budget: json['budget']?.toDouble(),
      attachments: json['attachments'] != null ? List<String>.from(json['attachments']) : null,
      status: RequestStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
      ),
      priority: Priority.values.firstWhere(
        (e) => e.toString().split('.').last == json['priority'],
      ),
      assignedFreelancerId: json['assigned_freelancer_id'],
      deadline: DateTime.parse(json['deadline']),
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
      tags: json['tags'] != null ? List<String>.from(json['tags']) : null,
      category: json['category'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'client_id': clientId,
      'title': title,
      'description': description,
      'budget': budget,
      'attachments': attachments,
      'status': status.toString().split('.').last,
      'priority': priority.toString().split('.').last,
      'assigned_freelancer_id': assignedFreelancerId,
      'deadline': deadline.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'tags': tags,
      'category': category,
    };
  }

  ServiceRequestModel copyWith({
    String? id,
    String? clientId,
    String? title,
    String? description,
    double? budget,
    List<String>? attachments,
    RequestStatus? status,
    Priority? priority,
    String? assignedFreelancerId,
    DateTime? deadline,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? tags,
    String? category,
  }) {
    return ServiceRequestModel(
      id: id ?? this.id,
      clientId: clientId ?? this.clientId,
      title: title ?? this.title,
      description: description ?? this.description,
      budget: budget ?? this.budget,
      attachments: attachments ?? this.attachments,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      assignedFreelancerId: assignedFreelancerId ?? this.assignedFreelancerId,
      deadline: deadline ?? this.deadline,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      tags: tags ?? this.tags,
      category: category ?? this.category,
    );
  }
}

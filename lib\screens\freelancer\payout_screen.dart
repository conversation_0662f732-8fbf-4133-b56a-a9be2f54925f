import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../providers/demo_auth_provider.dart';
import '../../providers/language_provider.dart';
import '../../providers/theme_provider.dart';
import '../../models/freelancer_earnings_model.dart';
import '../../models/payout_request_model.dart';
import '../../services/payout_service.dart';
import 'payout_history_screen.dart';

class PayoutScreen extends StatefulWidget {
  final FreelancerEarningsModel earnings;

  const PayoutScreen({super.key, required this.earnings});

  @override
  State<PayoutScreen> createState() => _PayoutScreenState();
}

class _PayoutScreenState extends State<PayoutScreen> with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _accountDetailsController = TextEditingController();
  final _passwordController = TextEditingController();

  PayoutMethod _selectedMethod = PayoutMethod.vodafoneCash;
  bool _isLoading = false;
  bool _showPasswordField = false;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _amountController.text = widget.earnings.availableBalance.toStringAsFixed(2);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _amountController.dispose();
    _accountDetailsController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer3<DemoAuthProvider, LanguageProvider, ThemeProvider>(
      builder: (context, authProvider, languageProvider, themeProvider, child) {
        final isArabic = languageProvider.isArabic;
        final isDark = themeProvider.isDarkMode;

        return Directionality(
          textDirection: languageProvider.textDirection,
          child: Scaffold(
            backgroundColor: isDark ? ThemeProvider.darkBackground : ThemeProvider.lightBackground,
            appBar: AppBar(
              title: Text(isArabic ? 'طلب سحب الأرباح' : 'Request Withdrawal'),
              backgroundColor: isDark ? ThemeProvider.darkCardBackground : Colors.white,
              foregroundColor: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
              elevation: 0,
              bottom: TabBar(
                controller: _tabController,
                labelColor: Theme.of(context).colorScheme.primary,
                unselectedLabelColor: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
                indicatorColor: Theme.of(context).colorScheme.primary,
                tabs: [Tab(text: isArabic ? 'طلب سحب' : 'New Request'), Tab(text: isArabic ? 'السجل' : 'History')],
              ),
            ),
            body: TabBarView(
              controller: _tabController,
              children: [
                _buildWithdrawalForm(isArabic, isDark),
                PayoutHistoryScreen(freelancerId: authProvider.user?.id ?? ''),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildWithdrawalForm(bool isArabic, bool isDark) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildBalanceCard(isArabic, isDark),
            const SizedBox(height: 24),
            _buildWithdrawalAmountSection(isArabic, isDark),
            const SizedBox(height: 24),
            _buildPayoutMethodSection(isArabic, isDark),
            const SizedBox(height: 24),
            _buildAccountDetailsSection(isArabic, isDark),
            if (_showPasswordField) ...[const SizedBox(height: 24), _buildPasswordConfirmation(isArabic, isDark)],
            const SizedBox(height: 32),
            _buildSubmitButton(isArabic, isDark),
          ],
        ),
      ),
    );
  }

  Widget _buildBalanceCard(bool isArabic, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Theme.of(context).colorScheme.primary, Theme.of(context).colorScheme.primary.withValues(alpha: 0.8)],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'الرصيد المتاح' : 'Available Balance',
            style: const TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 8),
          Text(
            '${widget.earnings.availableBalance.toStringAsFixed(2)} ريال',
            style: const TextStyle(color: Colors.white, fontSize: 32, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildBalanceInfo(
                  isArabic ? 'إجمالي الأرباح' : 'Total Earnings',
                  '${widget.earnings.totalEarnings.toStringAsFixed(2)} ريال',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildBalanceInfo(
                  isArabic ? 'تم سحبه' : 'Withdrawn',
                  '${widget.earnings.withdrawnAmount.toStringAsFixed(2)} ريال',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceInfo(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: TextStyle(color: Colors.white.withValues(alpha: 0.8), fontSize: 12)),
        Text(value, style: const TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.w600)),
      ],
    );
  }

  Widget _buildWithdrawalAmountSection(bool isArabic, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark ? ThemeProvider.darkCardBackground : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'مبلغ السحب' : 'Withdrawal Amount',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _amountController,
            keyboardType: TextInputType.number,
            inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'))],
            decoration: InputDecoration(
              labelText: isArabic ? 'المبلغ (ريال سعودي)' : 'Amount (SAR)',
              prefixText: 'SAR ',
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
              filled: true,
              fillColor: isDark ? ThemeProvider.darkBackground : Colors.grey[50],
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return isArabic ? 'يرجى إدخال المبلغ' : 'Please enter amount';
              }
              final amount = double.tryParse(value);
              if (amount == null) {
                return isArabic ? 'مبلغ غير صحيح' : 'Invalid amount';
              }
              if (amount < PayoutService.minimumWithdrawalAmount) {
                return isArabic
                    ? 'الحد الأدنى ${PayoutService.minimumWithdrawalAmount.toStringAsFixed(0)} ريال'
                    : 'Minimum amount is SAR ${PayoutService.minimumWithdrawalAmount.toStringAsFixed(0)}';
              }
              if (amount > widget.earnings.availableBalance) {
                return isArabic ? 'المبلغ أكبر من الرصيد المتاح' : 'Amount exceeds available balance';
              }
              return null;
            },
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    _amountController.text = (widget.earnings.availableBalance * 0.5).toStringAsFixed(2);
                  },
                  child: Text(isArabic ? '50%' : '50%'),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    _amountController.text = widget.earnings.availableBalance.toStringAsFixed(2);
                  },
                  child: Text(isArabic ? 'الكل' : 'All'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPayoutMethodSection(bool isArabic, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark ? ThemeProvider.darkCardBackground : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'طريقة السحب' : 'Payout Method',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          Column(
            children:
                PayoutMethod.values.map((method) {
                  return Container(
                    margin: const EdgeInsets.only(bottom: 12),
                    decoration: BoxDecoration(
                      border: Border.all(
                        color:
                            _selectedMethod == method
                                ? Theme.of(context).colorScheme.primary
                                : Colors.grey.withValues(alpha: 0.3),
                        width: 2,
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: RadioListTile<PayoutMethod>(
                      value: method,
                      groupValue: _selectedMethod,
                      onChanged: (value) {
                        setState(() {
                          _selectedMethod = value!;
                          _accountDetailsController.clear();
                        });
                      },
                      title: Row(
                        children: [
                          Icon(
                            method == PayoutMethod.vodafoneCash ? Icons.phone_android : Icons.account_balance,
                            color: _selectedMethod == method ? Theme.of(context).colorScheme.primary : Colors.grey,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            method == PayoutMethod.vodafoneCash
                                ? (isArabic ? 'فودافون كاش' : 'Vodafone Cash')
                                : (isArabic ? 'إنستا باي' : 'InstaPay'),
                            style: TextStyle(
                              fontWeight: _selectedMethod == method ? FontWeight.w600 : FontWeight.normal,
                              color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                            ),
                          ),
                        ],
                      ),
                      activeColor: Theme.of(context).colorScheme.primary,
                    ),
                  );
                }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountDetailsSection(bool isArabic, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark ? ThemeProvider.darkCardBackground : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'تفاصيل الحساب' : 'Account Details',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _accountDetailsController,
            keyboardType:
                _selectedMethod == PayoutMethod.vodafoneCash ? TextInputType.phone : TextInputType.emailAddress,
            decoration: InputDecoration(
              labelText:
                  _selectedMethod == PayoutMethod.vodafoneCash
                      ? (isArabic ? 'رقم الهاتف' : 'Phone Number')
                      : (isArabic ? 'البريد الإلكتروني' : 'Email Address'),
              prefixIcon: Icon(_selectedMethod == PayoutMethod.vodafoneCash ? Icons.phone : Icons.email),
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
              filled: true,
              fillColor: isDark ? ThemeProvider.darkBackground : Colors.grey[50],
              hintText:
                  _selectedMethod == PayoutMethod.vodafoneCash
                      ? (isArabic ? '01xxxxxxxxx' : '01xxxxxxxxx')
                      : (isArabic ? '<EMAIL>' : '<EMAIL>'),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return isArabic ? 'يرجى إدخال تفاصيل الحساب' : 'Please enter account details';
              }
              if (_selectedMethod == PayoutMethod.vodafoneCash) {
                if (!RegExp(r'^01[0-9]{9}$').hasMatch(value)) {
                  return isArabic ? 'رقم هاتف غير صحيح' : 'Invalid phone number';
                }
              } else {
                if (!RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(value)) {
                  return isArabic ? 'بريد إلكتروني غير صحيح' : 'Invalid email address';
                }
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPasswordConfirmation(bool isArabic, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark ? ThemeProvider.darkCardBackground : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3), width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.security, color: Colors.orange[600], size: 20),
              const SizedBox(width: 8),
              Text(
                isArabic ? 'تأكيد الهوية' : 'Identity Confirmation',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold, color: Colors.orange[600]),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _passwordController,
            obscureText: true,
            decoration: InputDecoration(
              labelText: isArabic ? 'كلمة المرور' : 'Password',
              prefixIcon: const Icon(Icons.lock),
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
              filled: true,
              fillColor: isDark ? ThemeProvider.darkBackground : Colors.grey[50],
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return isArabic ? 'يرجى إدخال كلمة المرور' : 'Please enter password';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton(bool isArabic, bool isDark) {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton.icon(
        onPressed: _isLoading ? null : _submitWithdrawalRequest,
        icon:
            _isLoading
                ? const SizedBox(width: 20, height: 20, child: CircularProgressIndicator(strokeWidth: 2))
                : const Icon(Icons.send),
        label: Text(
          _isLoading
              ? (isArabic ? 'جاري الإرسال...' : 'Submitting...')
              : (isArabic ? 'إرسال طلب السحب' : 'Submit Withdrawal Request'),
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        ),
      ),
    );
  }

  Future<void> _submitWithdrawalRequest() async {
    if (!_formKey.currentState!.validate()) return;

    // Show password confirmation if not already shown
    if (!_showPasswordField) {
      setState(() {
        _showPasswordField = true;
      });
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<DemoAuthProvider>(context, listen: false);
      final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
      final isArabic = languageProvider.isArabic;

      await PayoutService.createPayoutRequest(
        freelancerId: authProvider.user!.id,
        amount: double.parse(_amountController.text),
        payoutMethod: _selectedMethod,
        accountDetails: _accountDetailsController.text,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isArabic
                  ? 'تم إرسال طلب السحب بنجاح! سيتم مراجعته خلال 24-48 ساعة.'
                  : 'Withdrawal request submitted successfully! It will be reviewed within 24-48 hours.',
            ),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
        final isArabic = languageProvider.isArabic;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isArabic ? 'حدث خطأ: ${e.toString()}' : 'Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}

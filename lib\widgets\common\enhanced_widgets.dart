import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../providers/theme_provider.dart';

/// Enhanced Card Widget with improved styling
class EnhancedCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? color;
  final double? elevation;
  final VoidCallback? onTap;
  final BorderRadius? borderRadius;

  const EnhancedCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.color,
    this.elevation,
    this.onTap,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: margin ?? const EdgeInsets.all(8),
      decoration: BoxDecoration(
        borderRadius: borderRadius ?? BorderRadius.circular(16),
        color: color ?? (isDark ? ThemeProvider.darkCardBackground : ThemeProvider.cardBackground),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: borderRadius ?? BorderRadius.circular(16),
          child: Padding(padding: padding ?? const EdgeInsets.all(16), child: child),
        ),
      ),
    );
  }
}

/// Enhanced Button with gradient and improved styling
class EnhancedButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? textColor;
  final IconData? icon;
  final bool isLoading;
  final EdgeInsetsGeometry? padding;
  final double? borderRadius;

  const EnhancedButton({
    super.key,
    required this.text,
    this.onPressed,
    this.backgroundColor,
    this.textColor,
    this.icon,
    this.isLoading = false,
    this.padding,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    final bgColor = backgroundColor ?? ThemeProvider.primaryBlue;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(colors: [bgColor, bgColor.withValues(alpha: 0.8)]),
        borderRadius: BorderRadius.circular(borderRadius ?? 12),
        boxShadow: [BoxShadow(color: bgColor.withValues(alpha: 0.3), blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isLoading ? null : onPressed,
          borderRadius: BorderRadius.circular(borderRadius ?? 12),
          child: Padding(
            padding: padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (isLoading)
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                else ...[
                  if (icon != null) ...[Icon(icon, size: 20), const SizedBox(width: 8)],
                  Flexible(
                    child: Text(
                      text,
                      style: TextStyle(color: textColor ?? Colors.white, fontWeight: FontWeight.w600, fontSize: 16),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Enhanced Service Card for the services screen
class EnhancedServiceCard extends StatelessWidget {
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;
  final bool isArabic;

  const EnhancedServiceCard({
    super.key,
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.onTap,
    this.isArabic = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: Colors.white,
        boxShadow: [
          BoxShadow(color: Colors.black.withValues(alpha: 0.08), blurRadius: 20, offset: const Offset(0, 4)),
          BoxShadow(color: color.withValues(alpha: 0.1), blurRadius: 40, offset: const Offset(0, 8)),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          child: Column(
            children: [
              // Header with gradient background and icon
              Container(
                height: 70,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [color, color.withValues(alpha: 0.8)],
                  ),
                ),
                child: Stack(
                  children: [
                    // Background pattern
                    Positioned(
                      top: -15,
                      right: -15,
                      child: Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(shape: BoxShape.circle, color: Colors.white.withValues(alpha: 0.1)),
                      ),
                    ),
                    // Main icon
                    Center(
                      child: Container(
                        width: 36,
                        height: 36,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(10),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 6,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Icon(icon, size: 20),
                      ),
                    ),
                  ],
                ),
              ),

              // Content section
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(10),
                  child: Column(
                    children: [
                      // Service Title
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w700,
                          color: Theme.of(context).colorScheme.onSurface,
                          height: 1.1,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),

                      // Service Description
                      Flexible(
                        child: Text(
                          description,
                          style: TextStyle(
                            fontSize: 8,
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.65),
                            height: 1.2,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),

                      const SizedBox(height: 6),

                      // Action button with arrow
                      Container(
                        width: double.infinity,
                        height: 28,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(colors: [color, color.withValues(alpha: 0.8)]),
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(color: color.withValues(alpha: 0.3), blurRadius: 6, offset: const Offset(0, 2)),
                          ],
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: onTap,
                            borderRadius: BorderRadius.circular(16),
                            child: Center(
                              child: Icon(
                                isArabic ? Icons.arrow_back : Icons.arrow_forward,
                                color: Colors.white,
                                size: 16,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Enhanced Bottom Navigation Bar with Clean Design (No Animations)
class EnhancedBottomNavigationBar extends StatefulWidget {
  final int currentIndex;
  final Function(int) onTap;
  final List<EnhancedBottomNavItem> items;

  const EnhancedBottomNavigationBar({super.key, required this.currentIndex, required this.onTap, required this.items});

  @override
  State<EnhancedBottomNavigationBar> createState() => _EnhancedBottomNavigationBarState();
}

class _EnhancedBottomNavigationBarState extends State<EnhancedBottomNavigationBar> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(32),
        gradient:
            isDark
                ? LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    ThemeProvider.darkCardBackground.withValues(alpha: 0.95),
                    ThemeProvider.darkCardBackground.withValues(alpha: 0.85),
                    Colors.black.withValues(alpha: 0.9),
                  ],
                )
                : LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withValues(alpha: 0.95),
                    Colors.white.withValues(alpha: 0.9),
                    ThemeProvider.primaryBlue.withValues(alpha: 0.05),
                  ],
                ),
        boxShadow: [
          BoxShadow(
            color: isDark ? Colors.black.withValues(alpha: 0.6) : ThemeProvider.primaryBlue.withValues(alpha: 0.15),
            blurRadius: 32,
            offset: const Offset(0, -12),
            spreadRadius: 0,
          ),
          BoxShadow(
            color:
                isDark
                    ? ThemeProvider.primaryBlue.withValues(alpha: 0.2)
                    : ThemeProvider.primaryBlue.withValues(alpha: 0.1),
            blurRadius: 16,
            offset: const Offset(0, -4),
            spreadRadius: 2,
          ),
          BoxShadow(
            color: isDark ? Colors.white.withValues(alpha: 0.05) : Colors.white.withValues(alpha: 0.8),
            blurRadius: 8,
            offset: const Offset(0, 1),
            spreadRadius: -2,
          ),
        ],
        border: Border.all(
          color: isDark ? Colors.white.withValues(alpha: 0.1) : Colors.white.withValues(alpha: 0.6),
          width: 1.5,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(32),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: SafeArea(
            child: Container(
              constraints: const BoxConstraints(minHeight: 70, maxHeight: 85),
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              child: Stack(
                children: [
                  // Static indicator background
                  _buildStaticIndicator(isDark),
                  // Navigation items
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children:
                        widget.items.asMap().entries.map((entry) {
                          final index = entry.key;
                          final item = entry.value;
                          return _buildSimpleNavItem(context, item, index, isDark);
                        }).toList(),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Static indicator for selected tab
  Widget _buildStaticIndicator(bool isDark) {
    final itemWidth = (MediaQuery.of(context).size.width - 64) / widget.items.length;
    final indicatorPosition = widget.currentIndex * itemWidth;

    return Positioned(
      left: indicatorPosition + (itemWidth * 0.2),
      top: 8,
      child: Container(
        width: itemWidth * 0.6,
        height: 4,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(2),
          gradient: LinearGradient(
            colors: [
              ThemeProvider.primaryBlue.withValues(alpha: 0.8),
              ThemeProvider.primaryBlue,
              ThemeProvider.primaryBlue.withValues(alpha: 0.8),
            ],
          ),
          boxShadow: [
            BoxShadow(color: ThemeProvider.primaryBlue.withValues(alpha: 0.4), blurRadius: 8, spreadRadius: 1),
          ],
        ),
      ),
    );
  }

  // Simple navigation item without animations
  Widget _buildSimpleNavItem(BuildContext context, EnhancedBottomNavItem item, int index, bool isDark) {
    final isSelected = index == widget.currentIndex;

    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _handleTap(index),
          borderRadius: BorderRadius.circular(20),
          splashColor: ThemeProvider.primaryBlue.withValues(alpha: 0.2),
          highlightColor: ThemeProvider.primaryBlue.withValues(alpha: 0.1),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 2),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildSimpleIcon(item.icon, isSelected, isDark),
                const SizedBox(height: 3),
                _buildSimpleLabel(item.label, isSelected, isDark),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Simple icon without animations
  Widget _buildSimpleIcon(IconData icon, bool isSelected, bool isDark) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient:
            isSelected
                ? RadialGradient(
                  center: Alignment.center,
                  radius: 1.0,
                  colors: [
                    ThemeProvider.primaryBlue.withValues(alpha: 0.9),
                    ThemeProvider.primaryBlue.withValues(alpha: 0.7),
                    ThemeProvider.primaryBlue.withValues(alpha: 0.5),
                  ],
                )
                : LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    isDark ? Colors.white.withValues(alpha: 0.05) : Colors.black.withValues(alpha: 0.03),
                    isDark ? Colors.white.withValues(alpha: 0.02) : Colors.black.withValues(alpha: 0.01),
                  ],
                ),
        boxShadow:
            isSelected
                ? [BoxShadow(color: ThemeProvider.primaryBlue.withValues(alpha: 0.4), blurRadius: 16, spreadRadius: 2)]
                : [
                  BoxShadow(
                    color: isDark ? Colors.black.withValues(alpha: 0.3) : Colors.black.withValues(alpha: 0.08),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                    spreadRadius: 0,
                  ),
                ],
        border: isSelected ? Border.all(color: Colors.white.withValues(alpha: 0.2), width: 1) : null,
      ),
      child: Icon(icon, size: 22),
    );
  }

  // Simple label without animations
  Widget _buildSimpleLabel(String label, bool isSelected, bool isDark) {
    return Text(
      label,
      textAlign: TextAlign.center,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        color:
            isSelected
                ? ThemeProvider.primaryBlue
                : isDark
                ? ThemeProvider.darkTextSecondary.withValues(alpha: 0.8)
                : ThemeProvider.neutralGray.withValues(alpha: 0.8),
        fontSize: isSelected ? 10 : 9,
        fontWeight: isSelected ? FontWeight.w700 : FontWeight.w500,
        letterSpacing: isSelected ? 0.3 : 0.1,
        shadows:
            isSelected
                ? [
                  Shadow(
                    color: ThemeProvider.primaryBlue.withValues(alpha: 0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  ),
                ]
                : null,
      ),
    );
  }

  void _handleTap(int index) {
    // Add haptic feedback
    _addHapticFeedback();

    // Call the original onTap
    widget.onTap(index);
  }

  void _addHapticFeedback() {
    try {
      HapticFeedback.lightImpact();
    } catch (e) {
      // Haptic feedback not available on this platform
    }
  }
}

class EnhancedBottomNavItem {
  final IconData icon;
  final String label;

  const EnhancedBottomNavItem({required this.icon, required this.label});
}

/// Enhanced Floating Action Button with gradient and animations
class EnhancedFloatingActionButton extends StatefulWidget {
  final VoidCallback onPressed;
  final IconData icon;
  final String tooltip;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? size;
  final bool mini;

  const EnhancedFloatingActionButton({
    super.key,
    required this.onPressed,
    required this.icon,
    required this.tooltip,
    this.backgroundColor,
    this.foregroundColor,
    this.size,
    this.mini = false,
  });

  @override
  State<EnhancedFloatingActionButton> createState() => _EnhancedFloatingActionButtonState();
}

class _EnhancedFloatingActionButtonState extends State<EnhancedFloatingActionButton>
    with SingleTickerProviderStateMixin {
  late AnimationController animationController;
  late Animation<double> scaleAnimation;
  late Animation<double> rotationAnimation;

  @override
  void initState() {
    super.initState();
    animationController = AnimationController(duration: const Duration(milliseconds: 200), vsync: this);
    scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(parent: animationController, curve: Curves.easeInOut));
    rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.1,
    ).animate(CurvedAnimation(parent: animationController, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    animationController.dispose();
    super.dispose();
  }

  void handleTapDown(TapDownDetails details) {
    animationController.forward();
  }

  void handleTapUp(TapUpDetails details) {
    animationController.reverse();
  }

  void handleTapCancel() {
    animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final backgroundColor = widget.backgroundColor ?? ThemeProvider.primaryBlue;
    final foregroundColor = widget.foregroundColor ?? Colors.white;

    return AnimatedBuilder(
      animation: animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: scaleAnimation.value,
          child: Transform.rotate(
            angle: rotationAnimation.value,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(widget.mini ? 14 : 16),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [backgroundColor, backgroundColor.withValues(alpha: 0.8)],
                ),
                boxShadow: [
                  BoxShadow(color: backgroundColor.withValues(alpha: 0.3), blurRadius: 12, offset: const Offset(0, 4)),
                  BoxShadow(color: Colors.black.withValues(alpha: 0.1), blurRadius: 20, offset: const Offset(0, 8)),
                ],
              ),
              child: GestureDetector(
                onTapDown: handleTapDown,
                onTapUp: handleTapUp,
                onTapCancel: handleTapCancel,
                child: FloatingActionButton(
                  onPressed: widget.onPressed,
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  tooltip: widget.tooltip,
                  mini: widget.mini,
                  child: Icon(widget.icon, color: foregroundColor, size: widget.size ?? (widget.mini ? 20 : 28)),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

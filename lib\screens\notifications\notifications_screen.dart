import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/notification_provider.dart';
import '../../providers/language_provider.dart';
import '../../providers/demo_auth_provider.dart';
import '../../models/notification_model.dart';
import '../../models/user_model.dart';
import '../../widgets/notifications/notification_widgets.dart';
import '../../widgets/common/enhanced_widgets.dart';
import '../client/client_dashboard.dart';
import '../freelancer/freelancer_main_screen.dart';
import '../admin/admin_dashboard.dart';
import '../admin/payment_verification_screen.dart';
import '../chat/chat_screen.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  final int _currentIndex = 0; // Default to notifications (we'll set this based on user role)

  @override
  void initState() {
    super.initState();
    _initializeNotifications();
  }

  Future<void> _initializeNotifications() async {
    final authProvider = Provider.of<DemoAuthProvider>(context, listen: false);
    final notificationProvider = Provider.of<NotificationProvider>(context, listen: false);

    if (authProvider.userProfile?.id != null) {
      await notificationProvider.setUser(authProvider.userProfile!.id);
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            languageProvider.locale.languageCode == 'ar' ? 'الإشعارات' : 'Notifications',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          centerTitle: true,
          actions: [
            Consumer<NotificationProvider>(
              builder: (context, notificationProvider, child) {
                return PopupMenuButton<String>(
                  onSelected: (value) async {
                    switch (value) {
                      case 'mark_all_read':
                        await notificationProvider.markAllAsRead();
                        break;
                      case 'clear_all':
                        await _showClearAllDialog();
                        break;
                      case 'refresh':
                        await notificationProvider.refreshNotifications();
                        break;
                    }
                  },
                  itemBuilder:
                      (context) => [
                        PopupMenuItem(
                          value: 'mark_all_read',
                          child: Row(
                            children: [
                              const Icon(Icons.mark_email_read, size: 16),
                              const SizedBox(width: 8),
                              Text(
                                languageProvider.locale.languageCode == 'ar' ? 'تحديد الكل كمقروء' : 'Mark all as read',
                              ),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'clear_all',
                          child: Row(
                            children: [
                              const Icon(Icons.clear_all, size: 16),
                              const SizedBox(width: 8),
                              Text(languageProvider.locale.languageCode == 'ar' ? 'مسح الكل' : 'Clear all'),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'refresh',
                          child: Row(
                            children: [
                              const Icon(Icons.refresh, size: 16),
                              const SizedBox(width: 8),
                              Text(languageProvider.locale.languageCode == 'ar' ? 'تحديث' : 'Refresh'),
                            ],
                          ),
                        ),
                      ],
                );
              },
            ),
          ],
        ),
        body: _buildNotificationsList(null), // Show all notifications directly
        bottomNavigationBar: _buildBottomNavigationBar(languageProvider),
      ),
    );
  }

  Widget _buildNotificationsList(bool? isRead) {
    return Consumer<NotificationProvider>(
      builder: (context, notificationProvider, child) {
        if (notificationProvider.isLoading) {
          return const NotificationLoadingState();
        }

        if (notificationProvider.error != null) {
          return NotificationErrorState(
            error: notificationProvider.error!,
            onRetry: () => notificationProvider.refreshNotifications(),
          );
        }

        var notifications = notificationProvider.notifications;

        if (notifications.isEmpty) {
          return NotificationEmptyState(
            action: ElevatedButton.icon(
              onPressed: () => notificationProvider.refreshNotifications(),
              icon: const Icon(Icons.refresh),
              label: Text(Provider.of<LanguageProvider>(context).locale.languageCode == 'ar' ? 'تحديث' : 'Refresh'),
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () => notificationProvider.refreshNotifications(),
          child: ListView.builder(
            padding: const EdgeInsets.only(bottom: 16),
            itemCount: notifications.length,
            itemBuilder: (context, index) {
              final notification = notifications[index];
              return NotificationCard(
                notification: notification,
                onTap: () => _handleNotificationTap(notification),
                onMarkAsRead: () => notificationProvider.markAsRead(notification.id),
                onDelete: () => _showDeleteDialog(notification),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildBottomNavigationBar(LanguageProvider languageProvider) {
    final authProvider = Provider.of<DemoAuthProvider>(context);
    final userRole = authProvider.userProfile?.role ?? UserRole.client;
    final isArabic = languageProvider.locale.languageCode == 'ar';

    // Different navigation items based on user role
    List<EnhancedBottomNavItem> navItems;

    if (userRole == UserRole.client) {
      navItems = [
        EnhancedBottomNavItem(icon: Icons.home, label: isArabic ? 'الرئيسية' : 'Home'),
        EnhancedBottomNavItem(icon: Icons.dashboard, label: isArabic ? 'لوحة التحكم' : 'Dashboard'),
        EnhancedBottomNavItem(icon: Icons.work, label: isArabic ? 'طلباتي' : 'My Orders'),
        EnhancedBottomNavItem(icon: Icons.chat, label: isArabic ? 'الرسائل' : 'Messages'),
      ];
    } else if (userRole == UserRole.freelancer) {
      navItems = [
        EnhancedBottomNavItem(icon: Icons.dashboard, label: isArabic ? 'لوحة التحكم' : 'Dashboard'),
        EnhancedBottomNavItem(icon: Icons.search, label: isArabic ? 'تصفح الطلبات' : 'Browse'),
        EnhancedBottomNavItem(icon: Icons.local_offer, label: isArabic ? 'عروضي' : 'My Offers'),
        EnhancedBottomNavItem(icon: Icons.chat, label: isArabic ? 'الرسائل' : 'Messages'),
      ];
    } else {
      // Admin
      navItems = [
        EnhancedBottomNavItem(icon: Icons.dashboard, label: isArabic ? 'لوحة التحكم' : 'Dashboard'),
        EnhancedBottomNavItem(icon: Icons.people, label: isArabic ? 'المستخدمون' : 'Users'),
        EnhancedBottomNavItem(icon: Icons.assignment, label: isArabic ? 'الطلبات' : 'Orders'),
        EnhancedBottomNavItem(icon: Icons.payment, label: isArabic ? 'المدفوعات' : 'Payments'),
        EnhancedBottomNavItem(icon: Icons.chat, label: isArabic ? 'الرسائل' : 'Messages'),
      ];
    }

    return EnhancedBottomNavigationBar(
      currentIndex: _currentIndex,
      onTap: (index) => _handleNavigation(index, userRole),
      items: navItems,
    );
  }

  void _handleNavigation(int index, UserRole userRole) {
    if (userRole == UserRole.client) {
      // Client navigation
      switch (index) {
        case 0: // Home (Services)
          Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const ClientDashboard()));
          break;
        case 1: // Dashboard
          Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const ClientDashboard()));
          break;
        case 2: // My Orders
          Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const ClientDashboard()));
          break;
        case 3: // Messages
          Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const ClientDashboard()));
          break;
      }
    } else if (userRole == UserRole.freelancer) {
      // Freelancer navigation
      Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const FreelancerMainScreen()));
    } else {
      // Admin navigation
      Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const AdminDashboard()));
    }
  }

  void _handleNotificationTap(NotificationModel notification) {
    final notificationProvider = Provider.of<NotificationProvider>(context, listen: false);

    // Mark as read if not already read
    if (!notification.isRead) {
      notificationProvider.markAsRead(notification.id);
    }

    // Navigate to related screen if actionUrl is provided
    if (notification.actionUrl != null) {
      _navigateToActionUrl(notification.actionUrl!, notification);
    }
  }

  void _navigateToActionUrl(String actionUrl, NotificationModel notification) {
    try {
      // Parse the action URL to determine navigation
      final uri = Uri.parse(actionUrl);
      final pathSegments = uri.pathSegments;

      if (pathSegments.isEmpty) return;

      switch (pathSegments[0]) {
        case 'orders':
          _navigateToOrder(pathSegments.length > 1 ? pathSegments[1] : null, notification);
          break;
        case 'chat':
          _navigateToChat(pathSegments.length > 1 ? pathSegments[1] : null, notification);
          break;
        case 'payments':
          _navigateToPayments(pathSegments.length > 1 ? pathSegments[1] : null, notification);
          break;
        default:
          // For unknown paths, show a message
          _showNavigationError();
      }
    } catch (e) {
      // Handle malformed URLs
      _showNavigationError();
    }
  }

  void _navigateToOrder(String? orderId, NotificationModel notification) {
    final authProvider = Provider.of<DemoAuthProvider>(context, listen: false);
    final userRole = authProvider.userProfile?.role;
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    final isArabic = languageProvider.locale.languageCode == 'ar';

    // Close notifications screen first
    Navigator.pop(context);

    if (userRole == UserRole.client) {
      // Navigate to client dashboard - user can manually switch to orders tab
      Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const ClientDashboard()));

      // Show a snackbar to guide user to orders tab
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isArabic
                  ? 'تم فتح لوحة التحكم. انتقل إلى تبويب "طلباتي" لعرض الطلب'
                  : 'Dashboard opened. Go to "My Orders" tab to view the order',
            ),
            action: SnackBarAction(label: isArabic ? 'حسناً' : 'OK', onPressed: () {}),
          ),
        );
      });
    } else if (userRole == UserRole.freelancer) {
      // Navigate to freelancer main screen
      Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const FreelancerMainScreen()));
    } else if (userRole == UserRole.admin) {
      // Navigate to admin dashboard
      Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const AdminDashboard()));
    }
  }

  void _navigateToChat(String? chatId, NotificationModel notification) {
    if (chatId == null) return;

    // Extract chat details from notification metadata
    final senderName = notification.metadata?['sender_name'] ?? notification.metadata?['freelancer_name'] ?? 'User';
    final requestTitle = notification.metadata?['request_title'] ?? notification.titleEn;

    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => ChatScreen(
              chatId: chatId,
              recipientName: senderName,
              requestTitle: requestTitle,
              isAdminChat: senderName.toLowerCase().contains('admin'),
            ),
      ),
    );
  }

  void _navigateToPayments(String? paymentId, NotificationModel notification) {
    final authProvider = Provider.of<DemoAuthProvider>(context, listen: false);
    final userRole = authProvider.userProfile?.role;

    if (userRole == UserRole.admin) {
      // Navigate to admin payment verification screen
      Navigator.push(context, MaterialPageRoute(builder: (context) => const PaymentVerificationScreen()));
    } else {
      // For clients/freelancers, navigate to relevant dashboard
      Navigator.pop(context);
      if (userRole == UserRole.client) {
        Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const ClientDashboard()));
      } else if (userRole == UserRole.freelancer) {
        Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const FreelancerMainScreen()));
      }
    }
  }

  void _showNavigationError() {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    final isArabic = languageProvider.locale.languageCode == 'ar';

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(isArabic ? 'عذراً، لا يمكن فتح هذا الرابط' : 'Sorry, unable to open this link'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  Future<void> _showDeleteDialog(NotificationModel notification) async {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    final isArabic = languageProvider.locale.languageCode == 'ar';

    final result = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(isArabic ? 'حذف الإشعار' : 'Delete Notification'),
            content: Text(
              isArabic
                  ? 'هل أنت متأكد من أنك تريد حذف هذا الإشعار؟'
                  : 'Are you sure you want to delete this notification?',
            ),
            actions: [
              TextButton(onPressed: () => Navigator.of(context).pop(false), child: Text(isArabic ? 'إلغاء' : 'Cancel')),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: Text(isArabic ? 'حذف' : 'Delete', style: const TextStyle(color: Colors.red)),
              ),
            ],
          ),
    );

    if (result == true && mounted) {
      final notificationProvider = Provider.of<NotificationProvider>(context, listen: false);
      await notificationProvider.deleteNotification(notification.id);
    }
  }

  Future<void> _showClearAllDialog() async {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    final isArabic = languageProvider.locale.languageCode == 'ar';

    final result = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(isArabic ? 'مسح جميع الإشعارات' : 'Clear All Notifications'),
            content: Text(
              isArabic
                  ? 'هل أنت متأكد من أنك تريد مسح جميع الإشعارات؟ لا يمكن التراجع عن هذا الإجراء.'
                  : 'Are you sure you want to clear all notifications? This action cannot be undone.',
            ),
            actions: [
              TextButton(onPressed: () => Navigator.of(context).pop(false), child: Text(isArabic ? 'إلغاء' : 'Cancel')),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: Text(isArabic ? 'مسح الكل' : 'Clear All', style: const TextStyle(color: Colors.red)),
              ),
            ],
          ),
    );

    if (result == true && mounted) {
      final notificationProvider = Provider.of<NotificationProvider>(context, listen: false);
      await notificationProvider.clearAllNotifications();
    }
  }
}

import 'dart:async';
import 'package:flutter/material.dart';
import '../models/notification_model.dart';
import '../services/notification_service.dart';

class NotificationProvider with ChangeNotifier {
  List<NotificationModel> _notifications = [];
  bool _isLoading = false;
  String? _error;
  StreamSubscription<List<NotificationModel>>? _notificationSubscription;
  String? _currentUserId;
  bool _disposed = false;

  // Getters
  List<NotificationModel> get notifications => _notifications;
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// Get unread notifications count
  int get unreadCount => _notifications.where((n) => !n.isRead).length;

  /// Get unread notifications
  List<NotificationModel> get unreadNotifications => _notifications.where((n) => !n.isRead).toList();

  /// Get notifications by type
  List<NotificationModel> getNotificationsByType(NotificationType type) =>
      _notifications.where((n) => n.type == type).toList();

  /// Get recent notifications (last 24 hours)
  List<NotificationModel> get recentNotifications {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return _notifications.where((n) => n.createdAt.isAfter(yesterday)).toList();
  }

  bool _isInitialized = false;

  NotificationProvider();

  /// Initialize the provider (call this manually after the widget tree is built)
  Future<void> initialize() async {
    if (_isInitialized) return;
    _isInitialized = true;

    await NotificationService.initialize();
    _subscribeToNotifications();
  }

  /// Set the current user and load their notifications
  Future<void> setUser(String userId) async {
    if (_currentUserId == userId) return;

    _currentUserId = userId;
    await initialize(); // Ensure the provider is initialized
    await loadNotifications();
  }

  /// Subscribe to real-time notification updates
  void _subscribeToNotifications() {
    _notificationSubscription?.cancel();

    // Only subscribe if we have a valid user ID
    if (_currentUserId == null) return;

    _notificationSubscription = NotificationService.notificationsStream.listen(
      (allNotifications) {
        // Check if provider is still active and user ID is valid
        if (_currentUserId != null && !_disposed) {
          _notifications = allNotifications.where((n) => n.userId == _currentUserId).toList();
          _notifications.sort((a, b) => b.createdAt.compareTo(a.createdAt));

          // Only notify listeners if not disposed
          if (!_disposed) {
            notifyListeners();
          }
        }
      },
      onError: (error) {
        if (!_disposed) {
          _error = error.toString();
          notifyListeners();
        }
      },
    );
  }

  /// Load notifications for the current user
  Future<void> loadNotifications({bool showLoading = true}) async {
    if (_currentUserId == null) return;

    if (showLoading) {
      _isLoading = true;
      _error = null;
      notifyListeners();
    }

    try {
      final notifications = await NotificationService.getNotifications(userId: _currentUserId);
      _notifications = notifications;
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Refresh notifications (pull-to-refresh)
  Future<void> refreshNotifications() async {
    await loadNotifications(showLoading: false);
  }

  /// Mark a notification as read
  Future<void> markAsRead(String notificationId) async {
    try {
      await NotificationService.markAsRead(notificationId);

      // Update local state immediately for better UX
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        _notifications[index] = _notifications[index].markAsRead();
        notifyListeners();
      }
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    if (_currentUserId == null) return;

    try {
      await NotificationService.markAllAsRead(_currentUserId!);

      // Update local state immediately
      for (int i = 0; i < _notifications.length; i++) {
        if (!_notifications[i].isRead) {
          _notifications[i] = _notifications[i].markAsRead();
        }
      }
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Delete a notification
  Future<void> deleteNotification(String notificationId) async {
    try {
      await NotificationService.deleteNotification(notificationId);

      // Update local state immediately
      _notifications.removeWhere((n) => n.id == notificationId);
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Clear all notifications
  Future<void> clearAllNotifications() async {
    if (_currentUserId == null) return;

    try {
      await NotificationService.clearAllNotifications(_currentUserId!);

      // Update local state immediately
      _notifications.clear();
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Get unread count for a specific notification type
  int getUnreadCountByType(NotificationType type) {
    return _notifications.where((n) => n.type == type && !n.isRead).length;
  }

  /// Check if there are any high priority unread notifications
  bool get hasHighPriorityUnread {
    return _notifications.any(
      (n) => !n.isRead && (n.priority == NotificationPriority.high || n.priority == NotificationPriority.urgent),
    );
  }

  /// Get the most recent unread notification
  NotificationModel? get mostRecentUnread {
    final unread = unreadNotifications;
    if (unread.isEmpty) return null;
    unread.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return unread.first;
  }

  /// Create a new notification (for testing or manual creation)
  Future<void> createNotification({
    required String titleEn,
    required String titleAr,
    required String descriptionEn,
    required String descriptionAr,
    required NotificationType type,
    String? relatedId,
    NotificationPriority priority = NotificationPriority.normal,
    Map<String, dynamic>? metadata,
    String? actionUrl,
  }) async {
    if (_currentUserId == null) return;

    try {
      final notification = NotificationModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: _currentUserId!,
        titleEn: titleEn,
        titleAr: titleAr,
        descriptionEn: descriptionEn,
        descriptionAr: descriptionAr,
        type: type,
        relatedId: relatedId,
        priority: priority,
        createdAt: DateTime.now(),
        metadata: metadata,
        actionUrl: actionUrl,
      );

      await NotificationService.createNotification(notification);
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Filter notifications by read status
  List<NotificationModel> getNotificationsByReadStatus(bool isRead) {
    return _notifications.where((n) => n.isRead == isRead).toList();
  }

  /// Search notifications by title or description
  List<NotificationModel> searchNotifications(String query, String languageCode) {
    if (query.isEmpty) return _notifications;

    final lowerQuery = query.toLowerCase();
    return _notifications.where((n) {
      final title = n.getTitle(languageCode).toLowerCase();
      final description = n.getDescription(languageCode).toLowerCase();
      return title.contains(lowerQuery) || description.contains(lowerQuery);
    }).toList();
  }

  /// Clear error state
  void clearError() {
    _error = null;
    notifyListeners();
  }

  @override
  void dispose() {
    _disposed = true;
    _notificationSubscription?.cancel();
    NotificationService.dispose();
    super.dispose();
  }
}

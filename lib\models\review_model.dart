class ReviewModel {
  final String id;
  final String orderId;
  final String clientId;
  final String freelancerId;
  final String clientName;
  final String? clientAvatarUrl;
  final double rating;
  final String? reviewText;
  final String? reviewTextAr; // Arabic version of review
  final List<String> tags; // e.g., ['Quality', 'Communication', 'Timeliness']
  final String serviceType; // Type of service reviewed
  final bool isVerified; // Verified purchase
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? freelancerResponse;
  final String? freelancerResponseAr;
  final DateTime? responseDate;

  ReviewModel({
    required this.id,
    required this.orderId,
    required this.clientId,
    required this.freelancerId,
    required this.clientName,
    this.clientAvatarUrl,
    required this.rating,
    this.reviewText,
    this.reviewTextAr,
    this.tags = const [],
    required this.serviceType,
    this.isVerified = true,
    required this.createdAt,
    this.updatedAt,
    this.freelancerResponse,
    this.freelancerResponseAr,
    this.responseDate,
  });

  factory ReviewModel.fromJson(Map<String, dynamic> json) {
    return ReviewModel(
      id: json['id'],
      orderId: json['order_id'],
      clientId: json['client_id'],
      freelancerId: json['freelancer_id'],
      clientName: json['client_name'],
      clientAvatarUrl: json['client_avatar_url'],
      rating: json['rating'].toDouble(),
      reviewText: json['review_text'],
      reviewTextAr: json['review_text_ar'],
      tags: json['tags'] != null ? List<String>.from(json['tags']) : [],
      serviceType: json['service_type'],
      isVerified: json['is_verified'] ?? true,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
      freelancerResponse: json['freelancer_response'],
      freelancerResponseAr: json['freelancer_response_ar'],
      responseDate: json['response_date'] != null ? DateTime.parse(json['response_date']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_id': orderId,
      'client_id': clientId,
      'freelancer_id': freelancerId,
      'client_name': clientName,
      'client_avatar_url': clientAvatarUrl,
      'rating': rating,
      'review_text': reviewText,
      'review_text_ar': reviewTextAr,
      'tags': tags,
      'service_type': serviceType,
      'is_verified': isVerified,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'freelancer_response': freelancerResponse,
      'freelancer_response_ar': freelancerResponseAr,
      'response_date': responseDate?.toIso8601String(),
    };
  }

  ReviewModel copyWith({
    String? id,
    String? orderId,
    String? clientId,
    String? freelancerId,
    String? clientName,
    String? clientAvatarUrl,
    double? rating,
    String? reviewText,
    String? reviewTextAr,
    List<String>? tags,
    String? serviceType,
    bool? isVerified,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? freelancerResponse,
    String? freelancerResponseAr,
    DateTime? responseDate,
  }) {
    return ReviewModel(
      id: id ?? this.id,
      orderId: orderId ?? this.orderId,
      clientId: clientId ?? this.clientId,
      freelancerId: freelancerId ?? this.freelancerId,
      clientName: clientName ?? this.clientName,
      clientAvatarUrl: clientAvatarUrl ?? this.clientAvatarUrl,
      rating: rating ?? this.rating,
      reviewText: reviewText ?? this.reviewText,
      reviewTextAr: reviewTextAr ?? this.reviewTextAr,
      tags: tags ?? this.tags,
      serviceType: serviceType ?? this.serviceType,
      isVerified: isVerified ?? this.isVerified,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      freelancerResponse: freelancerResponse ?? this.freelancerResponse,
      freelancerResponseAr: freelancerResponseAr ?? this.freelancerResponseAr,
      responseDate: responseDate ?? this.responseDate,
    );
  }

  // Helper methods
  String getReviewText(bool isArabic) {
    if (isArabic && reviewTextAr != null && reviewTextAr!.isNotEmpty) {
      return reviewTextAr!;
    }
    return reviewText ?? '';
  }

  String? getFreelancerResponse(bool isArabic) {
    if (isArabic && freelancerResponseAr != null && freelancerResponseAr!.isNotEmpty) {
      return freelancerResponseAr;
    }
    return freelancerResponse;
  }

  bool get hasResponse => freelancerResponse != null || freelancerResponseAr != null;

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()} month${(difference.inDays / 30).floor() > 1 ? 's' : ''} ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else {
      return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    }
  }
}

// Portfolio item model for freelancer profiles
class PortfolioItem {
  final String id;
  final String freelancerId;
  final String title;
  final String titleAr;
  final String description;
  final String descriptionAr;
  final List<String> imageUrls;
  final List<String> tags;
  final String? projectUrl;
  final DateTime createdAt;

  PortfolioItem({
    required this.id,
    required this.freelancerId,
    required this.title,
    required this.titleAr,
    required this.description,
    required this.descriptionAr,
    this.imageUrls = const [],
    this.tags = const [],
    this.projectUrl,
    required this.createdAt,
  });

  factory PortfolioItem.fromJson(Map<String, dynamic> json) {
    return PortfolioItem(
      id: json['id'],
      freelancerId: json['freelancer_id'],
      title: json['title'],
      titleAr: json['title_ar'],
      description: json['description'],
      descriptionAr: json['description_ar'],
      imageUrls: json['image_urls'] != null ? List<String>.from(json['image_urls']) : [],
      tags: json['tags'] != null ? List<String>.from(json['tags']) : [],
      projectUrl: json['project_url'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'freelancer_id': freelancerId,
      'title': title,
      'title_ar': titleAr,
      'description': description,
      'description_ar': descriptionAr,
      'image_urls': imageUrls,
      'tags': tags,
      'project_url': projectUrl,
      'created_at': createdAt.toIso8601String(),
    };
  }

  String getTitle(bool isArabic) {
    return isArabic && titleAr.isNotEmpty ? titleAr : title;
  }

  String getDescription(bool isArabic) {
    return isArabic && descriptionAr.isNotEmpty ? descriptionAr : description;
  }
}

// Freelancer statistics model
class FreelancerStats {
  final String freelancerId;
  final int totalJobs;
  final int completedJobs;
  final int ongoingJobs;
  final double averageRating;
  final int totalReviews;
  final Map<int, int> ratingDistribution; // star -> count
  final double responseRate;
  final Duration averageResponseTime;
  final double onTimeDeliveryRate;
  final int repeatClients;
  final DateTime lastActive;

  FreelancerStats({
    required this.freelancerId,
    this.totalJobs = 0,
    this.completedJobs = 0,
    this.ongoingJobs = 0,
    this.averageRating = 0.0,
    this.totalReviews = 0,
    this.ratingDistribution = const {},
    this.responseRate = 0.0,
    this.averageResponseTime = const Duration(hours: 24),
    this.onTimeDeliveryRate = 0.0,
    this.repeatClients = 0,
    required this.lastActive,
  });

  factory FreelancerStats.fromJson(Map<String, dynamic> json) {
    return FreelancerStats(
      freelancerId: json['freelancer_id'],
      totalJobs: json['total_jobs'] ?? 0,
      completedJobs: json['completed_jobs'] ?? 0,
      ongoingJobs: json['ongoing_jobs'] ?? 0,
      averageRating: (json['average_rating'] ?? 0.0).toDouble(),
      totalReviews: json['total_reviews'] ?? 0,
      ratingDistribution: json['rating_distribution'] != null 
          ? Map<int, int>.from(json['rating_distribution']) 
          : {},
      responseRate: (json['response_rate'] ?? 0.0).toDouble(),
      averageResponseTime: Duration(minutes: json['average_response_time_minutes'] ?? 1440),
      onTimeDeliveryRate: (json['on_time_delivery_rate'] ?? 0.0).toDouble(),
      repeatClients: json['repeat_clients'] ?? 0,
      lastActive: DateTime.parse(json['last_active']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'freelancer_id': freelancerId,
      'total_jobs': totalJobs,
      'completed_jobs': completedJobs,
      'ongoing_jobs': ongoingJobs,
      'average_rating': averageRating,
      'total_reviews': totalReviews,
      'rating_distribution': ratingDistribution,
      'response_rate': responseRate,
      'average_response_time_minutes': averageResponseTime.inMinutes,
      'on_time_delivery_rate': onTimeDeliveryRate,
      'repeat_clients': repeatClients,
      'last_active': lastActive.toIso8601String(),
    };
  }

  double get successRate => totalJobs > 0 ? (completedJobs / totalJobs) * 100 : 0.0;
  
  String get responseTimeText {
    if (averageResponseTime.inHours < 1) {
      return '${averageResponseTime.inMinutes} min';
    } else if (averageResponseTime.inHours < 24) {
      return '${averageResponseTime.inHours} hour${averageResponseTime.inHours > 1 ? 's' : ''}';
    } else {
      return '${averageResponseTime.inDays} day${averageResponseTime.inDays > 1 ? 's' : ''}';
    }
  }
}

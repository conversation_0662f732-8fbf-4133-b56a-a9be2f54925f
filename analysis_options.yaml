# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# The following line activates a set of recommended lints for Flutter apps,
# packages, and plugins designed to encourage good coding practices.
include: package:flutter_lints/flutter.yaml

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "**/generated/**"
    - "**/build/**"
    - "**/.dart_tool/**"

linter:
  rules:
    # Disable some rules for better development experience
    avoid_print: false  # Allow print statements for debugging
    prefer_single_quotes: true  # Use single quotes

    # Additional helpful rules
    always_declare_return_types: true
    avoid_empty_else: true
    avoid_unnecessary_containers: true
    prefer_const_constructors: true
    prefer_const_literals_to_create_immutables: true
    sized_box_for_whitespace: true
    use_key_in_widget_constructors: false  # Disable for easier development

# Additional information about this file can be found at
# https://dart.dev/guides/language/analysis-options

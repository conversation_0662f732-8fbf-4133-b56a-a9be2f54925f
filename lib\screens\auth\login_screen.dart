import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../providers/demo_auth_provider.dart';
import '../../providers/theme_provider.dart';
import '../../providers/language_provider.dart';
import '../../utils/app_localizations.dart';
import '../../models/user_model.dart';
import '../../services/app_initialization_service.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _login() async {
    if (_formKey.currentState!.validate()) {
      final authProvider = Provider.of<DemoAuthProvider>(context, listen: false);

      final success = await authProvider.signIn(
        email: _emailController.text.trim(),
        password: _passwordController.text,
      );

      if (success && mounted) {
        // Initialize user session and navigate based on user role
        final userRole = authProvider.userProfile?.role;
        final userId = authProvider.userProfile?.id;

        if (userRole != null && userId != null) {
          // Initialize user session (including customer service chat for clients)
          await AppInitializationService.initializeUserSession(context, userId, userRole: userRole);

          // Check if still mounted after async operation
          if (mounted) {
            // Navigate based on user role
            switch (userRole) {
              case UserRole.client:
                context.go('/client-dashboard');
                break;
              case UserRole.freelancer:
                context.go('/freelancer-dashboard');
                break;
              case UserRole.admin:
                context.go('/admin-dashboard');
                break;
            }
          }
        }
      } else if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(authProvider.error ?? 'Login failed'), backgroundColor: Colors.red));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final languageProvider = Provider.of<LanguageProvider>(context);

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          actions: [
            // Language Toggle
            Consumer<LanguageProvider>(
              builder: (context, langProvider, child) {
                return PopupMenuButton<String>(
                  icon: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.language),
                      const SizedBox(width: 4),
                      Text(langProvider.isArabic ? 'عربي' : 'EN', style: const TextStyle(fontSize: 12)),
                    ],
                  ),
                  onSelected: (languageCode) {
                    langProvider.setLanguage(languageCode);
                  },
                  itemBuilder:
                      (context) => [
                        PopupMenuItem(
                          value: 'en',
                          child: Row(children: [const Text('🇺🇸'), const SizedBox(width: 8), Text(l10n.english)]),
                        ),
                        PopupMenuItem(
                          value: 'ar',
                          child: Row(children: [const Text('🇸🇦'), const SizedBox(width: 8), Text(l10n.arabic)]),
                        ),
                      ],
                );
              },
            ),
          ],
        ),
        body: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: 60),

                // Logo and Title
                Center(
                  child: Column(
                    children: [
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: const Icon(Icons.work_outline, size: 40, color: Colors.white),
                      ),
                      const SizedBox(height: 20),
                      Text(
                        l10n.appTitle,
                        style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        l10n.welcomeBack,
                        style: Theme.of(context).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        l10n.signInToContinue,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 50),

                // Login Form
                Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      TextFormField(
                        controller: _emailController,
                        keyboardType: TextInputType.emailAddress,
                        decoration: InputDecoration(
                          labelText: l10n.email,
                          prefixIcon: const Icon(Icons.email_outlined),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your email';
                          }
                          if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                            return 'Please enter a valid email';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 20),

                      TextFormField(
                        controller: _passwordController,
                        obscureText: _obscurePassword,
                        decoration: InputDecoration(
                          labelText: l10n.password,
                          prefixIcon: const Icon(Icons.lock_outlined),
                          suffixIcon: IconButton(
                            icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
                            onPressed: () {
                              setState(() {
                                _obscurePassword = !_obscurePassword;
                              });
                            },
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your password';
                          }
                          if (value.length < 6) {
                            return 'Password must be at least 6 characters';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 30),

                      Consumer<DemoAuthProvider>(
                        builder: (context, authProvider, child) {
                          return SizedBox(
                            width: double.infinity,
                            height: 50,
                            child: ElevatedButton(
                              onPressed: authProvider.isLoading ? null : _login,
                              child:
                                  authProvider.isLoading
                                      ? const SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(strokeWidth: 2),
                                      )
                                      : Text(l10n.signIn),
                            ),
                          );
                        },
                      ),

                      const SizedBox(height: 20),

                      // Demo Quick Login Buttons
                      Text(
                        l10n.quickDemoLogin,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 12),

                      Consumer<DemoAuthProvider>(
                        builder: (context, authProvider, child) {
                          return Column(
                            children: [
                              SizedBox(
                                width: double.infinity,
                                child: OutlinedButton.icon(
                                  onPressed:
                                      authProvider.isLoading
                                          ? null
                                          : () async {
                                            authProvider.quickLogin(UserRole.client);

                                            // Initialize customer service chat for client
                                            final userId = authProvider.userProfile?.id;
                                            if (userId != null) {
                                              await AppInitializationService.initializeUserSession(
                                                context,
                                                userId,
                                                userRole: UserRole.client,
                                              );
                                            }

                                            if (mounted) {
                                              if (context.mounted) {
                                                context.go('/client-dashboard');
                                              }
                                            }
                                          },
                                  icon: const Icon(Icons.person),
                                  label: Text(l10n.loginAsClient),
                                ),
                              ),
                              const SizedBox(height: 8),
                              SizedBox(
                                width: double.infinity,
                                child: OutlinedButton.icon(
                                  onPressed:
                                      authProvider.isLoading
                                          ? null
                                          : () {
                                            authProvider.quickLogin(UserRole.freelancer);
                                            context.go('/freelancer-dashboard');
                                          },
                                  icon: const Icon(Icons.work),
                                  label: Text(l10n.loginAsFreelancer),
                                ),
                              ),
                              const SizedBox(height: 8),
                              SizedBox(
                                width: double.infinity,
                                child: OutlinedButton.icon(
                                  onPressed:
                                      authProvider.isLoading
                                          ? null
                                          : () {
                                            authProvider.quickLogin(UserRole.admin);
                                            context.go('/admin-dashboard');
                                          },
                                  icon: const Icon(Icons.admin_panel_settings),
                                  label: Text(l10n.loginAsAdmin),
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 30),

                // Theme Toggle
                Consumer<ThemeProvider>(
                  builder: (context, themeProvider, child) {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.light_mode),
                        Switch(value: themeProvider.isDarkMode, onChanged: (_) => themeProvider.toggleTheme()),
                        const Icon(Icons.dark_mode),
                      ],
                    );
                  },
                ),

                const SizedBox(height: 30),

                // Sign Up Link
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text('${l10n.dontHaveAccount} ', style: Theme.of(context).textTheme.bodyMedium),
                    TextButton(onPressed: () => context.go('/register'), child: Text(l10n.signUp)),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/language_provider.dart';
import 'services_screen.dart';

class WelcomeServicesScreen extends StatelessWidget {
  const WelcomeServicesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isArabic = languageProvider.isArabic;

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        body: SafeArea(
          child: SingleChildScrollView(
            child: Column(
              children: [
                // Header Section
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(32),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [Theme.of(context).colorScheme.primary, Theme.of(context).colorScheme.secondary],
                    ),
                  ),
                  child: Column(
                    children: [
                      const SizedBox(height: 20),
                      const Icon(Icons.school, size: 80, color: Colors.white),
                      const SizedBox(height: 16),
                      Text(
                        isArabic ? 'مرحباً بك في منصة الخدمات الأكاديمية' : 'Welcome to Academic Services Platform',
                        style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.white),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        isArabic
                            ? 'نقدم لك أفضل الخدمات الأكاديمية والتقنية بجودة عالية'
                            : 'We provide the best academic and technical services with high quality',
                        style: const TextStyle(fontSize: 16, color: Colors.white70),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                // Services Overview
                Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isArabic ? 'خدماتنا المميزة' : 'Our Featured Services',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 16),

                      // Service Categories Grid
                      GridView.count(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        crossAxisCount: 2,
                        crossAxisSpacing: 16,
                        mainAxisSpacing: 16,
                        childAspectRatio: 1.2,
                        children: [
                          _buildCategoryCard(
                            context,
                            Icons.school,
                            isArabic ? 'خدمات أكاديمية' : 'Academic Services',
                            isArabic ? '7 خدمات' : '7 Services',
                            Colors.blue,
                          ),
                          _buildCategoryCard(
                            context,
                            Icons.computer,
                            isArabic ? 'خدمات تقنية' : 'Technical Services',
                            isArabic ? '2 خدمات' : '2 Services',
                            Colors.purple,
                          ),
                          _buildCategoryCard(
                            context,
                            Icons.translate,
                            isArabic ? 'خدمات لغوية' : 'Language Services',
                            isArabic ? '2 خدمات' : '2 Services',
                            Colors.orange,
                          ),
                          _buildCategoryCard(
                            context,
                            Icons.design_services,
                            isArabic ? 'خدمات التصميم' : 'Design Services',
                            isArabic ? '2 خدمات' : '2 Services',
                            Colors.green,
                          ),
                        ],
                      ),

                      const SizedBox(height: 32),

                      // Why Choose Us Section
                      Text(
                        isArabic ? 'لماذا تختارنا؟' : 'Why Choose Us?',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 16),

                      _buildFeatureItem(
                        Icons.verified,
                        isArabic ? 'جودة مضمونة' : 'Guaranteed Quality',
                        isArabic
                            ? 'نضمن لك أعلى مستويات الجودة في جميع خدماتنا'
                            : 'We guarantee the highest quality standards in all our services',
                        Colors.green,
                      ),
                      const SizedBox(height: 12),
                      _buildFeatureItem(
                        Icons.schedule,
                        isArabic ? 'تسليم في الوقت المحدد' : 'On-Time Delivery',
                        isArabic
                            ? 'نلتزم بالمواعيد المحددة ونسلم العمل في الوقت المناسب'
                            : 'We stick to deadlines and deliver work on time',
                        Colors.blue,
                      ),
                      const SizedBox(height: 12),
                      _buildFeatureItem(
                        Icons.support_agent,
                        isArabic ? 'دعم على مدار الساعة' : '24/7 Support',
                        isArabic ? 'فريق الدعم متاح لمساعدتك في أي وقت' : 'Support team available to help you anytime',
                        Colors.orange,
                      ),
                      const SizedBox(height: 12),
                      _buildFeatureItem(
                        Icons.price_check,
                        isArabic ? 'أسعار تنافسية' : 'Competitive Prices',
                        isArabic
                            ? 'أسعار مناسبة لجميع الميزانيات مع جودة عالية'
                            : 'Affordable prices for all budgets with high quality',
                        Colors.purple,
                      ),

                      const SizedBox(height: 32),

                      // Call to Action
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: () => _navigateToServices(context),
                          icon: const Icon(Icons.explore),
                          label: Text(
                            isArabic ? 'استكشف جميع الخدمات' : 'Explore All Services',
                            style: const TextStyle(fontSize: 16),
                          ),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            backgroundColor: Theme.of(context).colorScheme.primary,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Secondary Action
                      SizedBox(
                        width: double.infinity,
                        child: OutlinedButton.icon(
                          onPressed: () => Navigator.pop(context),
                          icon: const Icon(Icons.arrow_back),
                          label: Text(
                            isArabic ? 'العودة إلى لوحة التحكم' : 'Back to Dashboard',
                            style: const TextStyle(fontSize: 16),
                          ),
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryCard(BuildContext context, IconData icon, String title, String subtitle, Color color) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [color.withValues(alpha: 0.1), color.withValues(alpha: 0.05)],
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 40, color: color),
            const SizedBox(height: 8),
            Text(title, style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold), textAlign: TextAlign.center),
            const SizedBox(height: 4),
            Text(subtitle, style: TextStyle(fontSize: 12, color: color, fontWeight: FontWeight.w500)),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(IconData icon, String title, String description, Color color) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(color: color.withValues(alpha: 0.1), borderRadius: BorderRadius.circular(12)),
          child: Icon(icon, color: color, size: 24),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(title, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
              const SizedBox(height: 4),
              Text(description, style: TextStyle(fontSize: 14, color: Colors.grey[600])),
            ],
          ),
        ),
      ],
    );
  }

  void _navigateToServices(BuildContext context) {
    Navigator.push(context, MaterialPageRoute(builder: (context) => const ServicesScreen()));
  }
}

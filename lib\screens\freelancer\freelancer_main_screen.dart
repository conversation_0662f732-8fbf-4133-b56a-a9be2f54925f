import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/demo_auth_provider.dart';
import '../../providers/language_provider.dart';

import '../../utils/app_localizations.dart';
import '../../widgets/common/enhanced_widgets.dart';
import '../../widgets/notifications/notification_widgets.dart';
import '../notifications/notifications_screen.dart';
import '../demo/offer_to_chat_demo.dart';
import '../chat/chat_list_screen.dart';
import 'browse_requests_screen.dart';
import 'my_offers_screen.dart';
import 'freelancer_profile_screen.dart';

class FreelancerMainScreen extends StatefulWidget {
  const FreelancerMainScreen({super.key});

  @override
  State<FreelancerMainScreen> createState() => _FreelancerMainScreenState();
}

class _FreelancerMainScreenState extends State<FreelancerMainScreen> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<DemoAuthProvider>(context);
    final languageProvider = Provider.of<LanguageProvider>(context);
    final l10n = AppLocalizations.of(context);

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        appBar: AppBar(
          title: Text(_getAppBarTitle(l10n)),
          actions: [
            // Demo Button
            IconButton(
              onPressed: () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => const OfferToChatDemo()));
              },
              icon: const Icon(Icons.science),
              tooltip: languageProvider.isArabic ? 'عرض الميزات' : 'Feature Demo',
            ),

            NotificationBell(
              onTap: () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => const NotificationsScreen()));
              },
            ),

            PopupMenuButton(
              itemBuilder:
                  (context) => [
                    PopupMenuItem(
                      value: 'profile',
                      child: Row(children: [const Icon(Icons.person), const SizedBox(width: 8), Text(l10n.profile)]),
                    ),
                    PopupMenuItem(
                      value: 'logout',
                      child: Row(children: [const Icon(Icons.logout), const SizedBox(width: 8), Text(l10n.logout)]),
                    ),
                  ],
              onSelected: (value) {
                if (value == 'logout') {
                  authProvider.signOut();
                }
              },
            ),
          ],
        ),
        body: _buildCurrentScreen(),
        bottomNavigationBar: EnhancedBottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) {
            if (mounted) {
              setState(() {
                _currentIndex = index;
              });
            }
          },
          items: [
            EnhancedBottomNavItem(icon: Icons.search, label: l10n.browse),
            EnhancedBottomNavItem(icon: Icons.send, label: l10n.offers),
            EnhancedBottomNavItem(icon: Icons.chat, label: l10n.messages),
            EnhancedBottomNavItem(icon: Icons.person, label: l10n.profile),
          ],
        ),
      ),
    );
  }

  String _getAppBarTitle(AppLocalizations l10n) {
    switch (_currentIndex) {
      case 0:
        return l10n.browseJobs;
      case 1:
        return Provider.of<LanguageProvider>(context, listen: false).isArabic ? 'عروضي' : 'My Offers';
      case 2:
        return l10n.messages;
      case 3:
        return l10n.profile;
      default:
        return l10n.browseJobs;
    }
  }

  Widget _buildCurrentScreen() {
    switch (_currentIndex) {
      case 0:
        return const BrowseRequestsScreen();
      case 1:
        return const MyOffersScreen();
      case 2:
        return const ChatListScreen();
      case 3:
        return const FreelancerProfileScreen();
      default:
        return const BrowseRequestsScreen();
    }
  }
}

enum OrderStatus {
  created,
  paymentPending,
  paymentConfirmed,
  inProgress,
  submitted,
  delivered,
  editing,
  completed,
  cancelled,
}

enum PaymentStatus { pending, confirmed, rejected }

class OrderModel {
  final String id;
  final String requestId;
  final String clientId;
  final String freelancerId;
  final String offerId;
  final double amount;
  final OrderStatus status;
  final PaymentStatus paymentStatus;
  final String? paymentProofUrl;
  final String? deliveryUrl;
  final String? deliveryNotes;
  final DateTime? deliveryDate;
  final double? clientRating;
  final String? clientReview;
  final DateTime? reviewDate;
  final String? revisionNotes;
  final DateTime? revisionRequestedAt;
  final int revisionCount;
  final DateTime createdAt;
  final DateTime? updatedAt;

  OrderModel({
    required this.id,
    required this.requestId,
    required this.clientId,
    required this.freelancerId,
    required this.offerId,
    required this.amount,
    this.status = OrderStatus.created,
    this.paymentStatus = PaymentStatus.pending,
    this.paymentProofUrl,
    this.deliveryUrl,
    this.deliveryNotes,
    this.deliveryDate,
    this.clientRating,
    this.clientReview,
    this.reviewDate,
    this.revisionNotes,
    this.revisionRequestedAt,
    this.revisionCount = 0,
    required this.createdAt,
    this.updatedAt,
  });

  factory OrderModel.fromJson(Map<String, dynamic> json) {
    return OrderModel(
      id: json['id'],
      requestId: json['request_id'],
      clientId: json['client_id'],
      freelancerId: json['freelancer_id'],
      offerId: json['offer_id'],
      amount: json['amount'].toDouble(),
      status: OrderStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => OrderStatus.created,
      ),
      paymentStatus: PaymentStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['payment_status'],
        orElse: () => PaymentStatus.pending,
      ),
      paymentProofUrl: json['payment_proof_url'],
      deliveryUrl: json['delivery_url'],
      deliveryNotes: json['delivery_notes'],
      deliveryDate: json['delivery_date'] != null ? DateTime.parse(json['delivery_date']) : null,
      clientRating: json['client_rating']?.toDouble(),
      clientReview: json['client_review'],
      reviewDate: json['review_date'] != null ? DateTime.parse(json['review_date']) : null,
      revisionNotes: json['revision_notes'],
      revisionRequestedAt: json['revision_requested_at'] != null ? DateTime.parse(json['revision_requested_at']) : null,
      revisionCount: json['revision_count'] ?? 0,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'request_id': requestId,
      'client_id': clientId,
      'freelancer_id': freelancerId,
      'offer_id': offerId,
      'amount': amount,
      'status': status.toString().split('.').last,
      'payment_status': paymentStatus.toString().split('.').last,
      'payment_proof_url': paymentProofUrl,
      'delivery_url': deliveryUrl,
      'delivery_notes': deliveryNotes,
      'delivery_date': deliveryDate?.toIso8601String(),
      'client_rating': clientRating,
      'client_review': clientReview,
      'review_date': reviewDate?.toIso8601String(),
      'revision_notes': revisionNotes,
      'revision_requested_at': revisionRequestedAt?.toIso8601String(),
      'revision_count': revisionCount,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  OrderModel copyWith({
    String? id,
    String? requestId,
    String? clientId,
    String? freelancerId,
    String? offerId,
    double? amount,
    OrderStatus? status,
    PaymentStatus? paymentStatus,
    String? paymentProofUrl,
    String? deliveryUrl,
    String? deliveryNotes,
    DateTime? deliveryDate,
    double? clientRating,
    String? clientReview,
    DateTime? reviewDate,
    String? revisionNotes,
    DateTime? revisionRequestedAt,
    int? revisionCount,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return OrderModel(
      id: id ?? this.id,
      requestId: requestId ?? this.requestId,
      clientId: clientId ?? this.clientId,
      freelancerId: freelancerId ?? this.freelancerId,
      offerId: offerId ?? this.offerId,
      amount: amount ?? this.amount,
      status: status ?? this.status,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      paymentProofUrl: paymentProofUrl ?? this.paymentProofUrl,
      deliveryUrl: deliveryUrl ?? this.deliveryUrl,
      deliveryNotes: deliveryNotes ?? this.deliveryNotes,
      deliveryDate: deliveryDate ?? this.deliveryDate,
      clientRating: clientRating ?? this.clientRating,
      clientReview: clientReview ?? this.clientReview,
      reviewDate: reviewDate ?? this.reviewDate,
      revisionNotes: revisionNotes ?? this.revisionNotes,
      revisionRequestedAt: revisionRequestedAt ?? this.revisionRequestedAt,
      revisionCount: revisionCount ?? this.revisionCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

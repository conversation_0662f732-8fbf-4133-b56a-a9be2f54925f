enum OfferStatus { pending, accepted, rejected, withdrawn, completed }

class OfferModel {
  final String id;
  final String requestId;
  final String freelancerId;
  final double price;
  final String description;
  final int deliveryDays;
  final OfferStatus status;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? rejectionReason;
  final DateTime? submissionDate;
  final double? clientRating;
  final String? clientReview;

  OfferModel({
    required this.id,
    required this.requestId,
    required this.freelancerId,
    required this.price,
    required this.description,
    required this.deliveryDays,
    this.status = OfferStatus.pending,
    required this.createdAt,
    this.updatedAt,
    this.rejectionReason,
    this.submissionDate,
    this.clientRating,
    this.clientReview,
  });

  factory OfferModel.fromJson(Map<String, dynamic> json) {
    return OfferModel(
      id: json['id'],
      requestId: json['request_id'],
      freelancerId: json['freelancer_id'],
      price: json['price'].toDouble(),
      description: json['description'],
      deliveryDays: json['delivery_days'],
      status: OfferStatus.values.firstWhere((e) => e.toString().split('.').last == json['status']),
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
      rejectionReason: json['rejection_reason'],
      submissionDate: json['submission_date'] != null ? DateTime.parse(json['submission_date']) : null,
      clientRating: json['client_rating']?.toDouble(),
      clientReview: json['client_review'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'request_id': requestId,
      'freelancer_id': freelancerId,
      'price': price,
      'description': description,
      'delivery_days': deliveryDays,
      'status': status.toString().split('.').last,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'rejection_reason': rejectionReason,
      'submission_date': submissionDate?.toIso8601String(),
      'client_rating': clientRating,
      'client_review': clientReview,
    };
  }

  OfferModel copyWith({
    String? id,
    String? requestId,
    String? freelancerId,
    double? price,
    String? description,
    int? deliveryDays,
    OfferStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? rejectionReason,
    DateTime? submissionDate,
    double? clientRating,
    String? clientReview,
  }) {
    return OfferModel(
      id: id ?? this.id,
      requestId: requestId ?? this.requestId,
      freelancerId: freelancerId ?? this.freelancerId,
      price: price ?? this.price,
      description: description ?? this.description,
      deliveryDays: deliveryDays ?? this.deliveryDays,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      submissionDate: submissionDate ?? this.submissionDate,
      clientRating: clientRating ?? this.clientRating,
      clientReview: clientReview ?? this.clientReview,
    );
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/demo_auth_provider.dart';
import '../../providers/theme_provider.dart';
import '../../providers/language_provider.dart';
import '../../utils/app_localizations.dart';
import '../../models/order_model.dart';
import '../../services/order_service.dart';
import 'payment_verification_screen.dart';
import 'user_management_screen.dart';
import '../chat/chat_list_screen.dart';
import '../../widgets/common/enhanced_widgets.dart';
import '../../widgets/notifications/notification_widgets.dart';
import '../notifications/notifications_screen.dart';

class AdminDashboard extends StatefulWidget {
  const AdminDashboard({super.key});

  @override
  State<AdminDashboard> createState() => _AdminDashboardState();
}

class _AdminDashboardState extends State<AdminDashboard> {
  int _currentIndex = 0;
  List<OrderModel> _pendingPayments = [];
  bool _isLoading = true;

  // Mock stats - in real app, these would come from API
  int _totalUsers = 0;
  int _totalOrders = 0;
  int _activeOrders = 0;
  double _totalRevenue = 0.0;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    try {
      final pendingPayments = await OrderService.getPendingPayments();

      setState(() {
        _pendingPayments = pendingPayments;
        _totalUsers = 156; // Mock data
        _totalOrders = 89;
        _activeOrders = 23;
        _totalRevenue = 15420.50;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<DemoAuthProvider>(context);
    final languageProvider = Provider.of<LanguageProvider>(context);
    final l10n = AppLocalizations.of(context);
    final isArabic = languageProvider.isArabic;

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        appBar: AppBar(
          title: Text(l10n.adminDashboard),
          actions: [
            NotificationBell(
              onTap: () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => const NotificationsScreen()));
              },
            ),
            Consumer<ThemeProvider>(
              builder: (context, themeProvider, child) {
                return IconButton(
                  icon: Icon(themeProvider.isDarkMode ? Icons.light_mode : Icons.dark_mode),
                  onPressed: () => themeProvider.toggleTheme(),
                );
              },
            ),
            PopupMenuButton(
              itemBuilder:
                  (context) => [
                    PopupMenuItem(
                      value: 'settings',
                      child: Row(children: [const Icon(Icons.settings), const SizedBox(width: 8), Text(l10n.settings)]),
                    ),
                    PopupMenuItem(
                      value: 'logout',
                      child: Row(children: [const Icon(Icons.logout), const SizedBox(width: 8), Text(l10n.logout)]),
                    ),
                  ],
              onSelected: (value) {
                if (value == 'logout') {
                  authProvider.signOut();
                }
              },
            ),
          ],
        ),
        body: _buildCurrentPage(authProvider, isArabic, l10n),
        bottomNavigationBar: EnhancedBottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          items: [
            EnhancedBottomNavItem(icon: Icons.dashboard, label: isArabic ? 'لوحة التحكم' : 'Dashboard'),
            EnhancedBottomNavItem(icon: Icons.payment, label: isArabic ? 'المدفوعات' : 'Payments'),
            EnhancedBottomNavItem(icon: Icons.people, label: isArabic ? 'المستخدمون' : 'Users'),
            EnhancedBottomNavItem(icon: Icons.chat, label: isArabic ? 'الرسائل' : 'Messages'),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentPage(DemoAuthProvider authProvider, bool isArabic, AppLocalizations l10n) {
    if (_currentIndex == 1) {
      return const PaymentVerificationScreen();
    } else if (_currentIndex == 2) {
      return const UserManagementScreen();
    } else if (_currentIndex == 3) {
      return const ChatListScreen();
    }

    // Dashboard content
    return _isLoading
        ? const Center(child: CircularProgressIndicator())
        : RefreshIndicator(
          onRefresh: () async {
            setState(() {
              _isLoading = true;
            });
            await _loadDashboardData();
          },
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Welcome Section
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          l10n.adminControlCenter,
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          l10n.managePlatform,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // Stats Grid
                GridView.count(
                  crossAxisCount: 2,
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  children: [
                    _buildStatCard(l10n.totalUsers, _totalUsers.toString(), Icons.people_outline, Colors.blue),
                    _buildStatCard(
                      l10n.totalOrders,
                      _totalOrders.toString(),
                      Icons.shopping_bag_outlined,
                      Colors.green,
                    ),
                    _buildStatCard(l10n.activeOrders, _activeOrders.toString(), Icons.work_outline, Colors.orange),
                    _buildStatCard(
                      l10n.revenue,
                      '\$${_totalRevenue.toStringAsFixed(0)}',
                      Icons.attach_money,
                      Colors.purple,
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Pending Payments Alert
                if (_pendingPayments.isNotEmpty)
                  Card(
                    color: Colors.orange.withValues(alpha: 0.1),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          const Icon(Icons.warning_amber_outlined, color: Colors.orange, size: 32),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Payment Verification Required',
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.orange[800],
                                  ),
                                ),
                                Text(
                                  '${_pendingPayments.length} payments awaiting verification',
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Colors.orange[700]),
                                ),
                              ],
                            ),
                          ),
                          ElevatedButton(
                            onPressed: () {
                              setState(() {
                                _currentIndex = 1;
                              });
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('Review'),
                          ),
                        ],
                      ),
                    ),
                  ),

                const SizedBox(height: 24),

                // Quick Actions
                Text(
                  'Quick Actions',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),

                GridView.count(
                  crossAxisCount: 2,
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  childAspectRatio: 1.5,
                  children: [
                    _buildActionCard(l10n.verifyPayments, Icons.payment, Colors.orange, () {
                      setState(() {
                        _currentIndex = 1;
                      });
                    }),
                    _buildActionCard(l10n.manageUsers, Icons.people, Colors.blue, () {
                      setState(() {
                        _currentIndex = 2;
                      });
                    }),
                    _buildActionCard(l10n.orderManagement, Icons.assignment, Colors.green, () {
                      setState(() {
                        _currentIndex = 3;
                      });
                    }),
                    _buildActionCard(l10n.systemMessages, Icons.message, Colors.purple, () {
                      setState(() {
                        _currentIndex = 4;
                      });
                    }),
                  ],
                ),

                const SizedBox(height: 24),

                // Recent Activity
                Text(
                  l10n.recentActivity,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),

                Card(
                  child: Column(
                    children: [
                      _buildActivityItem(
                        l10n.newUserRegistration,
                        '<EMAIL> ${l10n.joinedAsFreelancer}',
                        Icons.person_add,
                        Colors.green,
                        isArabic ? 'منذ دقيقتين' : '2 minutes ago',
                      ),
                      const Divider(height: 1),
                      _buildActivityItem(
                        l10n.paymentSubmitted,
                        'Order #12345 - \$250.00',
                        Icons.payment,
                        Colors.orange,
                        isArabic ? 'منذ 15 دقيقة' : '15 minutes ago',
                      ),
                      const Divider(height: 1),
                      _buildActivityItem(
                        l10n.orderCompleted,
                        '${l10n.orderMarkedCompleted} #12340',
                        Icons.check_circle,
                        Colors.green,
                        isArabic ? 'منذ ساعة' : '1 hour ago',
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold, color: color),
            ),
            Text(title, style: Theme.of(context).textTheme.bodySmall, textAlign: TextAlign.center),
          ],
        ),
      ),
    );
  }

  Widget _buildActionCard(String title, IconData icon, Color color, VoidCallback onTap) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 32, color: color),
              const SizedBox(height: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActivityItem(String title, String subtitle, IconData icon, Color color, String time) {
    return ListTile(
      leading: CircleAvatar(backgroundColor: color.withValues(alpha: 0.1), child: Icon(icon, color: color, size: 20)),
      title: Text(title, style: const TextStyle(fontWeight: FontWeight.w600)),
      subtitle: Text(subtitle),
      trailing: Text(
        time,
        style: Theme.of(
          context,
        ).textTheme.bodySmall?.copyWith(color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6)),
      ),
    );
  }
}

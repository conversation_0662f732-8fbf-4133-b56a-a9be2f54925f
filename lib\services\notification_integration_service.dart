import 'package:flutter/material.dart';
import '../models/notification_model.dart';
import '../services/smart_notification_service.dart';
import '../services/offer_notification_service.dart';
import '../services/reminder_service.dart';
import '../services/admin_notification_service.dart';
import '../services/push_notification_service.dart';

/// Integration service that coordinates all notification systems
class NotificationIntegrationService {
  static bool _isInitialized = false;

  /// Initialize all notification services
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize core services
      await SmartNotificationService.initialize();
      await PushNotificationService.initialize();

      // Initialize reminder service
      ReminderService.initialize();

      _isInitialized = true;
      print('✅ Notification Integration Service initialized successfully');
    } catch (e) {
      print('❌ Failed to initialize Notification Integration Service: $e');
    }
  }

  /// Test all notification flows
  static Future<void> runNotificationTests({required String testUserId}) async {
    print('🧪 Starting comprehensive notification tests...');

    try {
      // Test 1: Order lifecycle notifications
      await _testOrderLifecycleNotifications(testUserId);

      // Test 2: Offer notifications
      await _testOfferNotifications(testUserId);

      // Test 3: Reminder notifications
      await _testReminderNotifications(testUserId);

      // Test 4: Admin notifications
      await _testAdminNotifications(testUserId);

      // Test 5: Arabic/English support
      await _testMultiLanguageSupport(testUserId);

      // Test 6: Notification creation (without UI dependency)
      await _testNotificationCreation(testUserId);

      print('✅ All notification tests completed successfully');
    } catch (e) {
      print('❌ Notification tests failed: $e');
    }
  }

  /// Test order lifecycle notifications
  static Future<void> _testOrderLifecycleNotifications(String userId) async {
    print('📦 Testing order lifecycle notifications...');

    // Test payment confirmation
    await SmartNotificationService.notifyPaymentConfirmed(
      clientId: userId,
      freelancerId: 'test_freelancer',
      orderId: 'test_order_1',
      amount: 150.0,
    );

    await Future.delayed(const Duration(milliseconds: 500));

    // Test work delivery
    await SmartNotificationService.notifyWorkDelivered(
      clientId: userId,
      orderId: 'test_order_1',
      freelancerName: 'Test Freelancer',
      deliveryNotes: 'Work completed as requested',
      fileUrls: ['https://example.com/file1.zip'],
    );

    await Future.delayed(const Duration(milliseconds: 500));

    // Test order completion
    await SmartNotificationService.createSmartNotification(
      userId: userId,
      titleEn: 'Order Completed',
      titleAr: 'تم إكمال الطلب',
      descriptionEn: 'Your order has been completed successfully.',
      descriptionAr: 'تم إكمال طلبك بنجاح.',
      type: NotificationType.orderCompleted,
      relatedId: 'test_order_1',
      priority: NotificationPriority.normal,
    );

    print('✅ Order lifecycle notifications test completed');
  }

  /// Test offer notifications
  static Future<void> _testOfferNotifications(String userId) async {
    print('💼 Testing offer notifications...');

    // Test offer received
    await OfferNotificationService.notifyOfferReceived(
      clientId: userId,
      offerId: 'test_offer_1',
      freelancerId: 'test_freelancer',
      offerAmount: 200.0,
      serviceTitle: 'Mobile App Development',
      deliveryDays: 7,
    );

    await Future.delayed(const Duration(milliseconds: 500));

    // Test offer accepted
    await OfferNotificationService.notifyOfferAccepted(
      freelancerId: 'test_freelancer',
      offerId: 'test_offer_1',
      clientId: userId,
      offerAmount: 200.0,
      serviceTitle: 'Mobile App Development',
    );

    print('✅ Offer notifications test completed');
  }

  /// Test reminder notifications
  static Future<void> _testReminderNotifications(String userId) async {
    print('⏰ Testing reminder notifications...');

    // Test payment reminder
    await SmartNotificationService.createPaymentReminder(
      clientId: userId,
      orderId: 'test_order_2',
      amount: 300.0,
      daysPending: 2,
    );

    await Future.delayed(const Duration(milliseconds: 500));

    // Test delivery review reminder
    await SmartNotificationService.createDeliveryReviewReminder(
      clientId: userId,
      orderId: 'test_order_3',
      hoursWaiting: 24,
    );

    await Future.delayed(const Duration(milliseconds: 500));

    // Test custom reminder
    await ReminderService.createCustomReminder(
      userId: userId,
      titleEn: 'Custom Reminder',
      titleAr: 'تذكير مخصص',
      messageEn: 'This is a test custom reminder.',
      messageAr: 'هذا تذكير مخصص للاختبار.',
      priority: NotificationPriority.normal,
    );

    print('✅ Reminder notifications test completed');
  }

  /// Test admin notifications
  static Future<void> _testAdminNotifications(String userId) async {
    print('👨‍💼 Testing admin notifications...');

    // Test system announcement
    await AdminNotificationService.sendSystemAnnouncement(
      titleEn: 'System Maintenance',
      titleAr: 'صيانة النظام',
      messageEn: 'System will be under maintenance tonight.',
      messageAr: 'سيكون النظام تحت الصيانة الليلة.',
      priority: NotificationPriority.high,
    );

    await Future.delayed(const Duration(milliseconds: 500));

    // Test custom admin message
    await AdminNotificationService.sendCustomMessage(
      userId: userId,
      titleEn: 'Welcome to Taskly',
      titleAr: 'مرحباً بك في تاسكلي',
      messageEn: 'Thank you for joining our platform!',
      messageAr: 'شكراً لانضمامك إلى منصتنا!',
      priority: NotificationPriority.normal,
    );

    print('✅ Admin notifications test completed');
  }

  /// Test multi-language support
  static Future<void> _testMultiLanguageSupport(String userId) async {
    print('🌐 Testing multi-language support...');

    // Create notification with both languages
    await SmartNotificationService.createSmartNotification(
      userId: userId,
      titleEn: 'Language Test',
      titleAr: 'اختبار اللغة',
      descriptionEn: 'This notification tests Arabic and English support.',
      descriptionAr: 'هذا الإشعار يختبر دعم اللغة العربية والإنجليزية.',
      type: NotificationType.systemAnnouncement,
      priority: NotificationPriority.normal,
    );

    print('✅ Multi-language support test completed');
  }

  /// Test notification creation without UI dependencies
  static Future<void> _testNotificationCreation(String userId) async {
    print('🔔 Testing notification creation...');

    // Test creating various notification types
    await SmartNotificationService.createSmartNotification(
      userId: userId,
      titleEn: 'Test Notification',
      titleAr: 'إشعار اختبار',
      descriptionEn: 'This is a test notification for system validation.',
      descriptionAr: 'هذا إشعار اختبار للتحقق من النظام.',
      type: NotificationType.systemAnnouncement,
      priority: NotificationPriority.normal,
    );

    await Future.delayed(const Duration(milliseconds: 500));

    // Test high priority notification
    await SmartNotificationService.createSmartNotification(
      userId: userId,
      titleEn: 'High Priority Test',
      titleAr: 'اختبار أولوية عالية',
      descriptionEn: 'This is a high priority test notification.',
      descriptionAr: 'هذا إشعار اختبار بأولوية عالية.',
      type: NotificationType.systemAnnouncement,
      priority: NotificationPriority.high,
    );

    print('✅ Notification creation test completed');
  }

  /// Simulate real-world notification scenarios
  static Future<void> simulateRealWorldScenarios({required String clientId, required String freelancerId}) async {
    print('🌍 Simulating real-world notification scenarios...');

    // Scenario 1: Complete order flow
    await _simulateCompleteOrderFlow(clientId, freelancerId);

    // Scenario 2: Multiple offers scenario
    await _simulateMultipleOffersScenario(clientId);

    // Scenario 3: Reminder escalation
    await _simulateReminderEscalation(clientId);

    print('✅ Real-world scenarios simulation completed');
  }

  /// Simulate complete order flow
  static Future<void> _simulateCompleteOrderFlow(String clientId, String freelancerId) async {
    print('📋 Simulating complete order flow...');

    final orderId = 'sim_order_${DateTime.now().millisecondsSinceEpoch}';

    // Step 1: Offer received
    await OfferNotificationService.notifyOfferReceived(
      clientId: clientId,
      offerId: 'sim_offer_1',
      freelancerId: freelancerId,
      offerAmount: 500.0,
      serviceTitle: 'E-commerce Website',
      deliveryDays: 14,
    );

    await Future.delayed(const Duration(seconds: 2));

    // Step 2: Payment confirmed
    await SmartNotificationService.notifyPaymentConfirmed(
      clientId: clientId,
      freelancerId: freelancerId,
      orderId: orderId,
      amount: 500.0,
    );

    await Future.delayed(const Duration(seconds: 2));

    // Step 3: Work delivered
    await SmartNotificationService.notifyWorkDelivered(
      clientId: clientId,
      orderId: orderId,
      freelancerName: 'Expert Developer',
      deliveryNotes: 'E-commerce website completed with all requested features.',
      fileUrls: ['https://example.com/website.zip', 'https://example.com/documentation.pdf'],
    );

    print('✅ Complete order flow simulation completed');
  }

  /// Simulate multiple offers scenario
  static Future<void> _simulateMultipleOffersScenario(String clientId) async {
    print('🎯 Simulating multiple offers scenario...');

    // Multiple freelancers sending offers
    for (int i = 1; i <= 3; i++) {
      await OfferNotificationService.notifyOfferReceived(
        clientId: clientId,
        offerId: 'multi_offer_$i',
        freelancerId: 'freelancer_$i',
        offerAmount: 100.0 + (i * 50),
        serviceTitle: 'Logo Design',
        deliveryDays: 3 + i,
      );

      await Future.delayed(const Duration(milliseconds: 800));
    }

    print('✅ Multiple offers scenario simulation completed');
  }

  /// Simulate reminder escalation
  static Future<void> _simulateReminderEscalation(String clientId) async {
    print('⚠️ Simulating reminder escalation...');

    // Day 1 reminder
    await SmartNotificationService.createPaymentReminder(
      clientId: clientId,
      orderId: 'escalation_order',
      amount: 250.0,
      daysPending: 1,
    );

    await Future.delayed(const Duration(milliseconds: 500));

    // Day 3 reminder (more urgent)
    await SmartNotificationService.createPaymentReminder(
      clientId: clientId,
      orderId: 'escalation_order',
      amount: 250.0,
      daysPending: 3,
    );

    await Future.delayed(const Duration(milliseconds: 500));

    // Day 7 reminder (final warning)
    await SmartNotificationService.createPaymentReminder(
      clientId: clientId,
      orderId: 'escalation_order',
      amount: 250.0,
      daysPending: 7,
    );

    print('✅ Reminder escalation simulation completed');
  }

  /// Get comprehensive notification statistics
  static Map<String, dynamic> getNotificationStats() {
    return {
      'smart_notification_service': SmartNotificationService.getNotificationStats(),
      'reminder_service': ReminderService.getReminderStats(),
      'admin_notification_service': AdminNotificationService.getAdminNotificationStats(),
      'push_notification_service': {'fcm_token': PushNotificationService.fcmToken, 'service_active': true},
      'integration_service': {'initialized': _isInitialized, 'last_test_run': DateTime.now().toIso8601String()},
    };
  }

  /// Cleanup all notification services
  static void dispose() {
    ReminderService.stop();
    PushNotificationService.dispose();
    print('🧹 Notification Integration Service disposed');
  }

  /// Quick test for specific notification type
  static Future<void> quickTest({required String userId, required NotificationType type, BuildContext? context}) async {
    switch (type) {
      case NotificationType.offerReceived:
        await OfferNotificationService.notifyOfferReceived(
          clientId: userId,
          offerId: 'quick_test_offer',
          freelancerId: 'test_freelancer',
          offerAmount: 100.0,
          serviceTitle: 'Quick Test Service',
          deliveryDays: 3,
        );
        break;
      case NotificationType.paymentConfirmed:
        await SmartNotificationService.notifyPaymentConfirmed(
          clientId: userId,
          freelancerId: 'test_freelancer',
          orderId: 'quick_test_order',
          amount: 100.0,
        );
        break;
      case NotificationType.workDelivered:
        await SmartNotificationService.notifyWorkDelivered(
          clientId: userId,
          orderId: 'quick_test_order',
          freelancerName: 'Test Freelancer',
          deliveryNotes: 'Quick test delivery',
        );
        break;
      case NotificationType.reminderPaymentDue:
        await SmartNotificationService.createPaymentReminder(
          clientId: userId,
          orderId: 'quick_test_order',
          amount: 100.0,
          daysPending: 1,
        );
        break;
      case NotificationType.systemAnnouncement:
        await AdminNotificationService.sendSystemAnnouncement(
          titleEn: 'Quick Test Announcement',
          titleAr: 'إعلان اختبار سريع',
          messageEn: 'This is a quick test system announcement.',
          messageAr: 'هذا إعلان نظام للاختبار السريع.',
        );
        break;
      default:
        await SmartNotificationService.createSmartNotification(
          userId: userId,
          titleEn: 'Quick Test',
          titleAr: 'اختبار سريع',
          descriptionEn: 'Quick test notification',
          descriptionAr: 'إشعار اختبار سريع',
          type: type,
          priority: NotificationPriority.normal,
        );
    }

    print('✅ Quick test completed for ${type.name}');
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/language_provider.dart';
import '../../providers/demo_auth_provider.dart';
import '../../models/offer_model.dart';
import '../../models/user_model.dart';
import '../../services/request_service.dart';
import '../chat/chat_screen.dart';
import '../freelancer/my_offers_screen.dart';

class OfferToChatDemo extends StatefulWidget {
  const OfferToChatDemo({super.key});

  @override
  State<OfferToChatDemo> createState() => _OfferToChatDemoState();
}

class _OfferToChatDemoState extends State<OfferToChatDemo> {
  bool _isLoading = false;
  String? _lastCreatedChatId;
  OfferModel? _lastOffer;

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isArabic = languageProvider.isArabic;

    return Scaffold(
      appBar: AppBar(
        title: Text(isArabic ? 'عرض: إنشاء محادثة تلقائية' : 'Demo: Auto Chat Creation'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Demo Title
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.auto_awesome, color: Theme.of(context).colorScheme.primary),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            isArabic
                                ? 'إنشاء محادثة تلقائية عند إرسال العرض'
                                : 'Auto Chat Creation on Offer Submission',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      isArabic
                          ? 'عندما يرسل المستقل عرضاً للعميل، يتم إنشاء محادثة تلقائياً بينهما مع رسائل ترحيبية.'
                          : 'When a freelancer sends an offer to a client, a chat is automatically created between them with welcome messages.',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Demo Steps
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isArabic ? 'خطوات العرض:' : 'Demo Steps:',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 12),
                    _buildStep(1, isArabic ? 'إرسال عرض جديد' : 'Send New Offer', isArabic),
                    _buildStep(2, isArabic ? 'إنشاء محادثة تلقائياً' : 'Auto Create Chat', isArabic),
                    _buildStep(3, isArabic ? 'إرسال رسائل ترحيبية' : 'Send Welcome Messages', isArabic),
                    _buildStep(4, isArabic ? 'فتح المحادثة' : 'Open Chat', isArabic),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Demo Actions
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isArabic ? 'جرب الميزة:' : 'Try the Feature:',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16),

                    // Step 1: Create Demo Offer
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _isLoading ? null : _createDemoOffer,
                        icon:
                            _isLoading
                                ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                )
                                : const Icon(Icons.send),
                        label: Text(isArabic ? '1. إرسال عرض تجريبي' : '1. Send Demo Offer'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).colorScheme.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),

                    const SizedBox(height: 12),

                    // Step 2: Open Created Chat
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _lastCreatedChatId != null ? _openCreatedChat : null,
                        icon: const Icon(Icons.chat),
                        label: Text(isArabic ? '2. فتح المحادثة المُنشأة' : '2. Open Created Chat'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: _lastCreatedChatId != null ? Colors.green : Colors.grey,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),

                    const SizedBox(height: 12),

                    // Step 3: Go to My Offers
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton.icon(
                        onPressed: _goToMyOffers,
                        icon: const Icon(Icons.work),
                        label: Text(isArabic ? '3. عرض عروضي' : '3. View My Offers'),
                        style: OutlinedButton.styleFrom(padding: const EdgeInsets.symmetric(vertical: 12)),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Demo Results
            if (_lastOffer != null) ...[
              Card(
                color: Colors.green.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.check_circle, color: Colors.green.shade700),
                          const SizedBox(width: 8),
                          Text(
                            isArabic ? 'تم إنشاء العرض والمحادثة!' : 'Offer & Chat Created!',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.green.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      _buildResultItem(isArabic ? 'معرف العرض:' : 'Offer ID:', _lastOffer!.id, isArabic),
                      _buildResultItem(isArabic ? 'السعر:' : 'Price:', '${_lastOffer!.price} SAR', isArabic),
                      _buildResultItem(
                        isArabic ? 'مدة التسليم:' : 'Delivery:',
                        '${_lastOffer!.deliveryDays} ${isArabic ? "أيام" : "days"}',
                        isArabic,
                      ),
                      _buildResultItem(isArabic ? 'معرف المحادثة:' : 'Chat ID:', _lastCreatedChatId ?? 'N/A', isArabic),
                    ],
                  ),
                ),
              ),
            ],

            const SizedBox(height: 20),

            // Feature Explanation
            Card(
              color: Colors.blue.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info, color: Colors.blue.shade700),
                        const SizedBox(width: 8),
                        Text(
                          isArabic ? 'كيف تعمل الميزة:' : 'How It Works:',
                          style: Theme.of(
                            context,
                          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold, color: Colors.blue.shade700),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    _buildFeaturePoint(
                      '🚀',
                      isArabic
                          ? 'عند إرسال العرض، يتم استدعاء RequestService.createOffer()'
                          : 'When offer is sent, RequestService.createOffer() is called',
                      isArabic,
                    ),
                    _buildFeaturePoint(
                      '💬',
                      isArabic
                          ? 'يتم إنشاء محادثة تلقائياً بين المستقل والعميل'
                          : 'Chat is automatically created between freelancer and client',
                      isArabic,
                    ),
                    _buildFeaturePoint(
                      '📝',
                      isArabic
                          ? 'يتم إرسال رسالة ترحيبية تحتوي على تفاصيل العرض'
                          : 'Welcome message is sent with offer details',
                      isArabic,
                    ),
                    _buildFeaturePoint(
                      '🔔',
                      isArabic
                          ? 'يتم إشعار العميل بالعرض الجديد والمحادثة'
                          : 'Client is notified about new offer and chat',
                      isArabic,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStep(int number, String title, bool isArabic) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          CircleAvatar(
            radius: 12,
            backgroundColor: Theme.of(context).colorScheme.primary,
            child: Text('$number', style: const TextStyle(color: Colors.white, fontSize: 12)),
          ),
          const SizedBox(width: 12),
          Text(title),
        ],
      ),
    );
  }

  Widget _buildResultItem(String label, String value, bool isArabic) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(width: 100, child: Text(label, style: const TextStyle(fontWeight: FontWeight.w500))),
          Expanded(child: Text(value, style: const TextStyle(fontFamily: 'monospace'))),
        ],
      ),
    );
  }

  Widget _buildFeaturePoint(String emoji, String text, bool isArabic) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(emoji, style: const TextStyle(fontSize: 16)),
          const SizedBox(width: 8),
          Expanded(child: Text(text)),
        ],
      ),
    );
  }

  Future<void> _createDemoOffer() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<DemoAuthProvider>(context, listen: false);
      final languageProvider = Provider.of<LanguageProvider>(context, listen: false);

      // Ensure user is logged in as freelancer
      if (authProvider.userProfile?.role != UserRole.freelancer) {
        authProvider.quickLogin(UserRole.freelancer);
      }

      // Create demo offer
      final demoOffer = OfferModel(
        id: 'demo_${DateTime.now().millisecondsSinceEpoch}',
        requestId: 'demo_request_001',
        freelancerId: authProvider.user!.id,
        price: 1500.0,
        deliveryDays: 7,
        description:
            languageProvider.isArabic
                ? 'أستطيع تطوير تطبيق الجوال المطلوب باستخدام Flutter مع جميع الميزات المطلوبة.'
                : 'I can develop the required mobile app using Flutter with all requested features.',
        status: OfferStatus.pending,
        createdAt: DateTime.now(),
      );

      // Send offer (this will create chat automatically)
      await RequestService.createOffer(demoOffer, languageCode: languageProvider.locale.languageCode);

      setState(() {
        _lastOffer = demoOffer;
        _lastCreatedChatId = 'chat_${demoOffer.requestId}_${DateTime.now().millisecondsSinceEpoch}';
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Text(languageProvider.isArabic ? 'تم إرسال العرض وإنشاء المحادثة!' : 'Offer sent and chat created!'),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red));
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _openCreatedChat() {
    if (_lastCreatedChatId != null && _lastOffer != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder:
              (context) => ChatScreen(
                chatId: _lastCreatedChatId!,
                recipientName: 'Demo Client',
                requestTitle: 'Mobile App Development',
                orderId: _lastOffer!.requestId,
              ),
        ),
      );
    }
  }

  void _goToMyOffers() {
    Navigator.push(context, MaterialPageRoute(builder: (context) => const MyOffersScreen()));
  }
}

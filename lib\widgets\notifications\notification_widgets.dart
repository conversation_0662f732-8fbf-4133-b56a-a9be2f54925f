import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/notification_model.dart';
import '../../providers/notification_provider.dart';
import '../../providers/language_provider.dart';
import '../../providers/theme_provider.dart';
import '../common/enhanced_widgets.dart';

/// Notification bell icon with unread count badge
class NotificationBell extends StatelessWidget {
  final VoidCallback? onTap;
  final double size;
  final Color? color;

  const NotificationBell({super.key, this.onTap, this.size = 24.0, this.color});

  @override
  Widget build(BuildContext context) {
    return Consumer<NotificationProvider>(
      builder: (context, notificationProvider, child) {
        final unreadCount = notificationProvider.unreadCount;
        final hasHighPriority = notificationProvider.hasHighPriorityUnread;

        return Stack(
          children: [
            IconButton(
              onPressed: onTap,
              icon: Icon(Icons.notifications_outlined, size: size),
              tooltip: 'Notifications',
            ),
            if (unreadCount > 0)
              Positioned(
                right: 8,
                top: 8,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: hasHighPriority ? Colors.red : ThemeProvider.primaryBlue,
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: Theme.of(context).scaffoldBackgroundColor, width: 1),
                  ),
                  constraints: const BoxConstraints(minWidth: 16, minHeight: 16),
                  child: Text(
                    unreadCount > 99 ? '99+' : unreadCount.toString(),
                    style: const TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}

/// Individual notification card widget
class NotificationCard extends StatelessWidget {
  final NotificationModel notification;
  final VoidCallback? onTap;
  final VoidCallback? onMarkAsRead;
  final VoidCallback? onDelete;
  final bool showActions;

  const NotificationCard({
    super.key,
    required this.notification,
    this.onTap,
    this.onMarkAsRead,
    this.onDelete,
    this.showActions = true,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return EnhancedCard(
      onTap: onTap,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      color:
          notification.isRead
              ? null
              : (isDark
                  ? ThemeProvider.darkCardBackground.withValues(alpha: 0.8)
                  : Colors.blue.withValues(alpha: 0.05)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Notification icon
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: _getTypeColor(notification.type).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(_getTypeIcon(notification.type), size: 20),
              ),
              const SizedBox(width: 12),

              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            notification.getTitle(languageProvider.locale.languageCode),
                            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold,
                            ),
                          ),
                        ),
                        if (!notification.isRead)
                          Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: _getPriorityColor(notification.priority),
                              shape: BoxShape.circle,
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      notification.getDescription(languageProvider.locale.languageCode),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).textTheme.bodySmall?.color?.withValues(alpha: 0.8),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      notification.getTimeAgo(languageProvider.locale.languageCode),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).textTheme.bodySmall?.color?.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
              ),

              // Actions
              if (showActions)
                PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'mark_read':
                        onMarkAsRead?.call();
                        break;
                      case 'delete':
                        onDelete?.call();
                        break;
                    }
                  },
                  itemBuilder:
                      (context) => [
                        if (!notification.isRead)
                          PopupMenuItem(
                            value: 'mark_read',
                            child: Row(
                              children: [
                                const Icon(Icons.mark_email_read, size: 16),
                                const SizedBox(width: 8),
                                Text(languageProvider.locale.languageCode == 'ar' ? 'تحديد كمقروء' : 'Mark as read'),
                              ],
                            ),
                          ),
                        PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              const Icon(Icons.delete, size: 16, color: Colors.red),
                              const SizedBox(width: 8),
                              Text(
                                languageProvider.locale.languageCode == 'ar' ? 'حذف' : 'Delete',
                                style: const TextStyle(color: Colors.red),
                              ),
                            ],
                          ),
                        ),
                      ],
                  child: const Icon(Icons.more_vert, size: 16),
                ),
            ],
          ),
        ],
      ),
    );
  }

  IconData _getTypeIcon(NotificationType type) {
    switch (type) {
      case NotificationType.orderStatus:
      case NotificationType.orderNew:
      case NotificationType.orderAccepted:
      case NotificationType.orderDelivered:
      case NotificationType.orderCompleted:
      case NotificationType.orderCancelled:
      case NotificationType.jobAssigned:
        return Icons.assignment;
      case NotificationType.newMessage:
      case NotificationType.messageReceived:
        return Icons.message;
      case NotificationType.paymentConfirmation:
      case NotificationType.paymentRequest:
      case NotificationType.paymentConfirmed:
      case NotificationType.paymentPending:
        return Icons.payment;
      case NotificationType.systemAnnouncement:
      case NotificationType.adminMessage:
      case NotificationType.systemUpdate:
      case NotificationType.maintenanceNotice:
        return Icons.announcement;
      case NotificationType.freelancerApplication:
        return Icons.person_add;
      case NotificationType.offerReceived:
      case NotificationType.offerAccepted:
      case NotificationType.offerRejected:
        return Icons.local_offer;
      case NotificationType.reminderPaymentDue:
      case NotificationType.reminderDeliveryPending:
      case NotificationType.reminderReviewPending:
      case NotificationType.reminderResponseNeeded:
      case NotificationType.reminderGeneral:
        return Icons.schedule;
      case NotificationType.workDelivered:
        return Icons.file_download;
      case NotificationType.revisionRequested:
        return Icons.edit;
      case NotificationType.profileUpdate:
      case NotificationType.accountVerification:
        return Icons.person;
    }
  }

  Color _getTypeColor(NotificationType type) {
    switch (type) {
      case NotificationType.orderNew:
        return Colors.blue;
      case NotificationType.orderAccepted:
        return Colors.green;
      case NotificationType.orderDelivered:
        return Colors.orange;
      case NotificationType.orderCompleted:
        return Colors.green;
      case NotificationType.orderCancelled:
        return Colors.red;
      case NotificationType.newMessage:
      case NotificationType.messageReceived:
        return Colors.blue;
      case NotificationType.paymentConfirmation:
      case NotificationType.paymentConfirmed:
        return Colors.green;
      case NotificationType.paymentRequest:
      case NotificationType.paymentPending:
        return Colors.orange;
      case NotificationType.systemAnnouncement:
      case NotificationType.adminMessage:
      case NotificationType.systemUpdate:
      case NotificationType.maintenanceNotice:
        return Colors.purple;
      case NotificationType.freelancerApplication:
        return Colors.blue;
      case NotificationType.orderStatus:
      case NotificationType.jobAssigned:
        return Colors.indigo;
      case NotificationType.offerReceived:
        return Colors.teal;
      case NotificationType.offerAccepted:
        return Colors.green;
      case NotificationType.offerRejected:
        return Colors.red;
      case NotificationType.reminderPaymentDue:
      case NotificationType.reminderDeliveryPending:
      case NotificationType.reminderReviewPending:
      case NotificationType.reminderResponseNeeded:
      case NotificationType.reminderGeneral:
        return Colors.amber;
      case NotificationType.workDelivered:
        return Colors.blue;
      case NotificationType.revisionRequested:
        return Colors.orange;
      case NotificationType.profileUpdate:
      case NotificationType.accountVerification:
        return Colors.indigo;
    }
  }

  Color _getPriorityColor(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return Colors.grey;
      case NotificationPriority.normal:
        return Colors.blue;
      case NotificationPriority.high:
        return Colors.orange;
      case NotificationPriority.urgent:
        return Colors.red;
    }
  }
}

/// Compact notification item for lists
class NotificationListItem extends StatelessWidget {
  final NotificationModel notification;
  final VoidCallback? onTap;

  const NotificationListItem({super.key, required this.notification, this.onTap});

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);

    return ListTile(
      onTap: onTap,
      leading: CircleAvatar(
        backgroundColor: _getTypeColor(notification.type).withValues(alpha: 0.1),
        child: Icon(_getTypeIcon(notification.type), color: _getTypeColor(notification.type), size: 20),
      ),
      title: Text(
        notification.getTitle(languageProvider.locale.languageCode),
        style: TextStyle(fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Text(
        notification.getDescription(languageProvider.locale.languageCode),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            notification.getTimeAgo(languageProvider.locale.languageCode),
            style: Theme.of(context).textTheme.bodySmall,
          ),
          if (!notification.isRead)
            Container(
              margin: const EdgeInsets.only(top: 4),
              width: 8,
              height: 8,
              decoration: BoxDecoration(color: _getPriorityColor(notification.priority), shape: BoxShape.circle),
            ),
        ],
      ),
    );
  }

  IconData _getTypeIcon(NotificationType type) {
    // Same implementation as NotificationCard
    switch (type) {
      case NotificationType.orderStatus:
      case NotificationType.orderNew:
      case NotificationType.orderAccepted:
      case NotificationType.orderDelivered:
      case NotificationType.orderCompleted:
      case NotificationType.orderCancelled:
      case NotificationType.jobAssigned:
        return Icons.assignment;
      case NotificationType.newMessage:
      case NotificationType.messageReceived:
        return Icons.message;
      case NotificationType.paymentConfirmation:
      case NotificationType.paymentRequest:
      case NotificationType.paymentConfirmed:
      case NotificationType.paymentPending:
        return Icons.payment;
      case NotificationType.systemAnnouncement:
      case NotificationType.adminMessage:
      case NotificationType.systemUpdate:
      case NotificationType.maintenanceNotice:
        return Icons.announcement;
      case NotificationType.freelancerApplication:
        return Icons.person_add;
      case NotificationType.offerReceived:
      case NotificationType.offerAccepted:
      case NotificationType.offerRejected:
        return Icons.local_offer;
      case NotificationType.reminderPaymentDue:
      case NotificationType.reminderDeliveryPending:
      case NotificationType.reminderReviewPending:
      case NotificationType.reminderResponseNeeded:
      case NotificationType.reminderGeneral:
        return Icons.schedule;
      case NotificationType.workDelivered:
        return Icons.file_download;
      case NotificationType.revisionRequested:
        return Icons.edit;
      case NotificationType.profileUpdate:
      case NotificationType.accountVerification:
        return Icons.person;
    }
  }

  Color _getTypeColor(NotificationType type) {
    // Same implementation as NotificationCard
    switch (type) {
      case NotificationType.orderNew:
        return Colors.blue;
      case NotificationType.orderAccepted:
        return Colors.green;
      case NotificationType.orderDelivered:
        return Colors.orange;
      case NotificationType.orderCompleted:
        return Colors.green;
      case NotificationType.orderCancelled:
        return Colors.red;
      case NotificationType.newMessage:
      case NotificationType.messageReceived:
        return Colors.blue;
      case NotificationType.paymentConfirmation:
      case NotificationType.paymentConfirmed:
        return Colors.green;
      case NotificationType.paymentRequest:
      case NotificationType.paymentPending:
        return Colors.orange;
      case NotificationType.systemAnnouncement:
      case NotificationType.adminMessage:
      case NotificationType.systemUpdate:
      case NotificationType.maintenanceNotice:
        return Colors.purple;
      case NotificationType.freelancerApplication:
        return Colors.blue;
      case NotificationType.orderStatus:
      case NotificationType.jobAssigned:
        return Colors.indigo;
      case NotificationType.offerReceived:
        return Colors.teal;
      case NotificationType.offerAccepted:
        return Colors.green;
      case NotificationType.offerRejected:
        return Colors.red;
      case NotificationType.reminderPaymentDue:
      case NotificationType.reminderDeliveryPending:
      case NotificationType.reminderReviewPending:
      case NotificationType.reminderResponseNeeded:
      case NotificationType.reminderGeneral:
        return Colors.amber;
      case NotificationType.workDelivered:
        return Colors.blue;
      case NotificationType.revisionRequested:
        return Colors.orange;
      case NotificationType.profileUpdate:
      case NotificationType.accountVerification:
        return Colors.indigo;
    }
  }

  Color _getPriorityColor(NotificationPriority priority) {
    // Same implementation as NotificationCard
    switch (priority) {
      case NotificationPriority.low:
        return Colors.grey;
      case NotificationPriority.normal:
        return Colors.blue;
      case NotificationPriority.high:
        return Colors.orange;
      case NotificationPriority.urgent:
        return Colors.red;
    }
  }
}

/// Empty state widget for notifications
class NotificationEmptyState extends StatelessWidget {
  final String? title;
  final String? description;
  final Widget? action;

  const NotificationEmptyState({super.key, this.title, this.description, this.action});

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.notifications_none, size: 64, color: Theme.of(context).disabledColor),
            const SizedBox(height: 16),
            Text(
              title ?? (languageProvider.locale.languageCode == 'ar' ? 'لا توجد إشعارات' : 'No notifications'),
              style: Theme.of(context).textTheme.titleLarge?.copyWith(color: Theme.of(context).disabledColor),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              description ??
                  (languageProvider.locale.languageCode == 'ar'
                      ? 'ستظهر إشعاراتك هنا عند وصولها'
                      : 'Your notifications will appear here when they arrive'),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Theme.of(context).disabledColor),
              textAlign: TextAlign.center,
            ),
            if (action != null) ...[const SizedBox(height: 24), action!],
          ],
        ),
      ),
    );
  }
}

/// Loading state widget for notifications
class NotificationLoadingState extends StatelessWidget {
  const NotificationLoadingState({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [CircularProgressIndicator(), SizedBox(height: 16), Text('Loading notifications...')],
        ),
      ),
    );
  }
}

/// Error state widget for notifications
class NotificationErrorState extends StatelessWidget {
  final String error;
  final VoidCallback? onRetry;

  const NotificationErrorState({super.key, required this.error, this.onRetry});

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              languageProvider.locale.languageCode == 'ar' ? 'خطأ في تحميل الإشعارات' : 'Error loading notifications',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Theme.of(context).disabledColor),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: Text(languageProvider.locale.languageCode == 'ar' ? 'إعادة المحاولة' : 'Retry'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Notification filter chips
class NotificationFilterChips extends StatelessWidget {
  final NotificationType? selectedType;
  final Function(NotificationType?) onTypeChanged;
  final bool? showUnreadOnly;
  final Function(bool?) onUnreadOnlyChanged;

  const NotificationFilterChips({
    super.key,
    this.selectedType,
    required this.onTypeChanged,
    this.showUnreadOnly,
    required this.onUnreadOnlyChanged,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          // Unread filter
          FilterChip(
            label: Text(languageProvider.locale.languageCode == 'ar' ? 'غير مقروء فقط' : 'Unread only'),
            selected: showUnreadOnly ?? false,
            onSelected: onUnreadOnlyChanged,
          ),
          const SizedBox(width: 8),

          // Type filters
          ...NotificationType.values
              .take(6)
              .map(
                (type) => Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(_getTypeLabel(type, languageProvider.locale.languageCode)),
                    selected: selectedType == type,
                    onSelected: (selected) {
                      onTypeChanged(selected ? type : null);
                    },
                  ),
                ),
              ),
        ],
      ),
    );
  }

  String _getTypeLabel(NotificationType type, String languageCode) {
    if (languageCode == 'ar') {
      switch (type) {
        case NotificationType.orderStatus:
        case NotificationType.orderNew:
        case NotificationType.orderAccepted:
        case NotificationType.orderDelivered:
        case NotificationType.orderCompleted:
        case NotificationType.orderCancelled:
          return 'الطلبات';
        case NotificationType.newMessage:
        case NotificationType.messageReceived:
          return 'الرسائل';
        case NotificationType.paymentConfirmation:
        case NotificationType.paymentRequest:
        case NotificationType.paymentConfirmed:
          return 'المدفوعات';
        case NotificationType.systemAnnouncement:
          return 'الإعلانات';
        case NotificationType.freelancerApplication:
          return 'التطبيقات';
        case NotificationType.jobAssigned:
          return 'المهام';
        case NotificationType.offerReceived:
          return 'العروض';
        default:
          return 'الإشعارات';
      }
    } else {
      switch (type) {
        case NotificationType.orderStatus:
        case NotificationType.orderNew:
        case NotificationType.orderAccepted:
        case NotificationType.orderDelivered:
        case NotificationType.orderCompleted:
        case NotificationType.orderCancelled:
          return 'Orders';
        case NotificationType.newMessage:
        case NotificationType.messageReceived:
          return 'Messages';
        case NotificationType.paymentConfirmation:
        case NotificationType.paymentRequest:
        case NotificationType.paymentConfirmed:
          return 'Payments';
        case NotificationType.systemAnnouncement:
          return 'Announcements';
        case NotificationType.freelancerApplication:
          return 'Applications';
        case NotificationType.jobAssigned:
          return 'Jobs';
        case NotificationType.offerReceived:
          return 'Offers';
        default:
          return 'Notifications';
      }
    }
  }
}

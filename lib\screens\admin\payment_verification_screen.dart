import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../utils/app_localizations.dart';
import '../../providers/language_provider.dart';

class PaymentVerificationScreen extends StatefulWidget {
  const PaymentVerificationScreen({super.key});

  @override
  State<PaymentVerificationScreen> createState() => _PaymentVerificationScreenState();
}

class _PaymentVerificationScreenState extends State<PaymentVerificationScreen> {
  final List<Map<String, dynamic>> _pendingPayments = [
    {
      'id': '1',
      'clientName': 'John Client',
      'freelancerName': '<PERSON>per',
      'projectTitle': 'Mobile App Development',
      'amount': 2500.0,
      'paymentProofUrl': 'https://example.com/proof1.jpg',
      'submittedAt': DateTime.now().subtract(const Duration(hours: 2)),
      'status': 'pending',
    },
    {
      'id': '2',
      'clientName': 'Alice Business',
      'freelancerName': 'Mike <PERSON>',
      'projectTitle': 'Logo Design',
      'amount': 300.0,
      'paymentProofUrl': 'https://example.com/proof2.jpg',
      'submittedAt': DateTime.now().subtract(const Duration(hours: 5)),
      'status': 'pending',
    },
    {
      'id': '3',
      'clientName': 'Tech Startup',
      'freelancerName': 'Emma Writer',
      'projectTitle': 'Content Writing',
      'amount': 800.0,
      'paymentProofUrl': 'https://example.com/proof3.jpg',
      'submittedAt': DateTime.now().subtract(const Duration(days: 1)),
      'status': 'pending',
    },
  ];

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final languageProvider = Provider.of<LanguageProvider>(context);

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(languageProvider.isArabic ? Icons.arrow_forward : Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(l10n.verifyPayments),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              setState(() {
                // Refresh payments
              });
            },
          ),
        ],
      ),
      body:
          _pendingPayments.isEmpty
              ? _buildEmptyState(l10n)
              : RefreshIndicator(
                onRefresh: () async {
                  setState(() {
                    // Refresh payments
                  });
                },
                child: ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _pendingPayments.length,
                  itemBuilder: (context, index) {
                    final payment = _pendingPayments[index];
                    return _buildPaymentCard(payment, l10n);
                  },
                ),
              ),
    );
  }

  Widget _buildEmptyState(AppLocalizations l10n) {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.payment_outlined, size: 64, color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5)),
          const SizedBox(height: 16),
          Text(
            languageProvider.isArabic ? 'لا توجد مدفوعات معلقة' : 'No pending payments',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            languageProvider.isArabic ? 'تم معالجة جميع المدفوعات' : 'All payments have been processed',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7)),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentCard(Map<String, dynamic> payment, AppLocalizations l10n) {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(payment['projectTitle'], style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                      const SizedBox(height: 4),
                      Text(
                        languageProvider.isArabic
                            ? 'العميل: ${payment['clientName']}'
                            : 'Client: ${payment['clientName']}',
                        style: TextStyle(color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7)),
                      ),
                      Text(
                        languageProvider.isArabic
                            ? 'المستقل: ${payment['freelancerName']}'
                            : 'Freelancer: ${payment['freelancerName']}',
                        style: TextStyle(color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7)),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    languageProvider.isArabic ? 'معلق' : 'PENDING',
                    style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold, color: Colors.orange[700]),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Amount
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.attach_money, color: Colors.green[700]),
                  const SizedBox(width: 8),
                  Text(
                    languageProvider.isArabic
                        ? 'المبلغ: \$${payment['amount'].toStringAsFixed(2)}'
                        : 'Amount: \$${payment['amount'].toStringAsFixed(2)}',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.green[700]),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Payment Proof
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.image, color: Colors.blue[700]),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          languageProvider.isArabic ? 'إثبات الدفع' : 'Payment Proof',
                          style: TextStyle(fontWeight: FontWeight.w600, color: Colors.blue[700]),
                        ),
                        Text(
                          languageProvider.isArabic
                              ? 'تم الإرسال ${_getTimeAgo(payment['submittedAt'])}'
                              : 'Submitted ${_getTimeAgo(payment['submittedAt'])}',
                          style: TextStyle(fontSize: 12, color: Colors.blue[600]),
                        ),
                      ],
                    ),
                  ),
                  TextButton(
                    onPressed: () => _viewPaymentProof(payment),
                    child: Text(languageProvider.isArabic ? 'عرض' : 'View'),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _rejectPayment(payment),
                    icon: const Icon(Icons.close),
                    label: Text(languageProvider.isArabic ? 'رفض' : 'Reject'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.red,
                      side: const BorderSide(color: Colors.red),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _confirmPayment(payment),
                    icon: const Icon(Icons.check),
                    label: Text(languageProvider.isArabic ? 'تأكيد' : 'Confirm'),
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.green, foregroundColor: Colors.white),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _viewPaymentProof(Map<String, dynamic> payment) {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    languageProvider.isArabic ? 'إثبات الدفع' : 'Payment Proof',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  Container(
                    width: 300,
                    height: 400,
                    decoration: BoxDecoration(color: Colors.grey[200], borderRadius: BorderRadius.circular(8)),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(Icons.image, size: 64, color: Colors.grey),
                          const SizedBox(height: 8),
                          Text(languageProvider.isArabic ? 'لقطة شاشة الدفع' : 'Payment Screenshot'),
                          Text(
                            languageProvider.isArabic ? '(صورة تجريبية)' : '(Demo Image)',
                            style: const TextStyle(fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text(languageProvider.isArabic ? 'إغلاق' : 'Close'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
    );
  }

  void _confirmPayment(Map<String, dynamic> payment) {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(languageProvider.isArabic ? 'تأكيد الدفع' : 'Confirm Payment'),
            content: Text(
              languageProvider.isArabic
                  ? 'هل أنت متأكد من تأكيد هذا الدفع بقيمة \$${payment['amount'].toStringAsFixed(2)}؟\n\nسيتم إشعار المستقل للبدء في العمل.'
                  : 'Are you sure you want to confirm this payment of \$${payment['amount'].toStringAsFixed(2)}?\n\nThis will notify the freelancer to start working.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(languageProvider.isArabic ? 'إلغاء' : 'Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _processPaymentConfirmation(payment);
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.green, foregroundColor: Colors.white),
                child: Text(languageProvider.isArabic ? 'تأكيد' : 'Confirm'),
              ),
            ],
          ),
    );
  }

  void _rejectPayment(Map<String, dynamic> payment) {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    final reasonController = TextEditingController();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(languageProvider.isArabic ? 'رفض الدفع' : 'Reject Payment'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  languageProvider.isArabic
                      ? 'لماذا ترفض هذا الدفع بقيمة \$${payment['amount'].toStringAsFixed(2)}؟'
                      : 'Why are you rejecting this payment of \$${payment['amount'].toStringAsFixed(2)}?',
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: reasonController,
                  decoration: InputDecoration(
                    labelText: languageProvider.isArabic ? 'سبب الرفض' : 'Reason for rejection',
                    border: const OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(languageProvider.isArabic ? 'إلغاء' : 'Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _processPaymentRejection(payment, reasonController.text);
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red, foregroundColor: Colors.white),
                child: Text(languageProvider.isArabic ? 'رفض' : 'Reject'),
              ),
            ],
          ),
    );
  }

  void _processPaymentConfirmation(Map<String, dynamic> payment) {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    setState(() {
      _pendingPayments.removeWhere((p) => p['id'] == payment['id']);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          languageProvider.isArabic
              ? 'تم تأكيد الدفع لـ ${payment['projectTitle']}'
              : 'Payment confirmed for ${payment['projectTitle']}',
        ),
        backgroundColor: Colors.green,
        action: SnackBarAction(
          label: languageProvider.isArabic ? 'تراجع' : 'Undo',
          onPressed: () {
            setState(() {
              _pendingPayments.add(payment);
            });
          },
        ),
      ),
    );
  }

  void _processPaymentRejection(Map<String, dynamic> payment, String reason) {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    setState(() {
      _pendingPayments.removeWhere((p) => p['id'] == payment['id']);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          languageProvider.isArabic
              ? 'تم رفض الدفع لـ ${payment['projectTitle']}'
              : 'Payment rejected for ${payment['projectTitle']}',
        ),
        backgroundColor: Colors.red,
        action: SnackBarAction(
          label: languageProvider.isArabic ? 'تراجع' : 'Undo',
          onPressed: () {
            setState(() {
              _pendingPayments.add(payment);
            });
          },
        ),
      ),
    );
  }

  String _getTimeAgo(DateTime dateTime) {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      if (languageProvider.isArabic) {
        return difference.inDays == 1 ? 'منذ يوم واحد' : 'منذ ${difference.inDays} أيام';
      } else {
        return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
      }
    } else if (difference.inHours > 0) {
      if (languageProvider.isArabic) {
        return difference.inHours == 1 ? 'منذ ساعة واحدة' : 'منذ ${difference.inHours} ساعات';
      } else {
        return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
      }
    } else {
      if (languageProvider.isArabic) {
        return difference.inMinutes <= 1 ? 'منذ دقيقة واحدة' : 'منذ ${difference.inMinutes} دقائق';
      } else {
        return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
      }
    }
  }
}

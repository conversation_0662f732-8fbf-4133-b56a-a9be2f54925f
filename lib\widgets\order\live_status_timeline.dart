import 'package:flutter/material.dart';
import '../../models/order_model.dart';
import '../../providers/theme_provider.dart';

class LiveStatusTimeline extends StatelessWidget {
  final OrderModel order;
  final bool isArabic;
  final bool isDark;
  final bool isVertical;
  final bool showLabels;
  final double stepSize;

  const LiveStatusTimeline({
    super.key,
    required this.order,
    required this.isArabic,
    required this.isDark,
    this.isVertical = false,
    this.showLabels = true,
    this.stepSize = 40,
  });

  @override
  Widget build(BuildContext context) {
    final steps = _getTimelineSteps();
    final currentStepIndex = _getCurrentStepIndex();

    if (isVertical) {
      return _buildVerticalTimeline(steps, currentStepIndex);
    } else {
      return _buildHorizontalTimeline(steps, currentStepIndex);
    }
  }

  Widget _buildVerticalTimeline(List<TimelineStep> steps, int currentStepIndex) {
    return Column(
      children: List.generate(steps.length, (index) {
        final step = steps[index];
        final isCompleted = index <= currentStepIndex;
        final isCurrent = index == currentStepIndex;
        final isLast = index == steps.length - 1;

        return Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Timeline indicator
            Column(
              children: [
                _buildStepIndicator(step, isCompleted, isCurrent),
                if (!isLast)
                  Container(
                    width: 2,
                    height: 60,
                    color: isCompleted ? step.color : (isDark ? Colors.grey[600] : Colors.grey[300]),
                  ),
              ],
            ),
            const SizedBox(width: 16),
            // Step content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(bottom: 40),
                child: _buildStepContent(step, isCompleted, isCurrent),
              ),
            ),
          ],
        );
      }),
    );
  }

  Widget _buildHorizontalTimeline(List<TimelineStep> steps, int currentStepIndex) {
    return Column(
      children: [
        // Timeline indicators
        Row(
          children: List.generate(steps.length, (index) {
            final step = steps[index];
            final isCompleted = index <= currentStepIndex;
            final isCurrent = index == currentStepIndex;
            final isLast = index == steps.length - 1;

            return Expanded(
              child: Row(
                children: [
                  _buildStepIndicator(step, isCompleted, isCurrent),
                  if (!isLast)
                    Expanded(
                      child: Container(
                        height: 2,
                        color: isCompleted ? step.color : (isDark ? Colors.grey[600] : Colors.grey[300]),
                      ),
                    ),
                ],
              ),
            );
          }),
        ),

        if (showLabels) ...[
          const SizedBox(height: 12),
          // Step labels
          Row(
            children: List.generate(steps.length, (index) {
              final step = steps[index];
              final isCompleted = index <= currentStepIndex;
              final isCurrent = index == currentStepIndex;

              return Expanded(
                child: Text(
                  step.title,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: isCurrent ? FontWeight.bold : FontWeight.normal,
                    color: isCompleted ? step.color : (isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
                  ),
                ),
              );
            }),
          ),
        ],
      ],
    );
  }

  Widget _buildStepIndicator(TimelineStep step, bool isCompleted, bool isCurrent) {
    return Container(
      width: stepSize,
      height: stepSize,
      decoration: BoxDecoration(
        color: isCompleted ? step.color : (isDark ? Colors.grey[700] : Colors.grey[300]),
        shape: BoxShape.circle,
        border: isCurrent ? Border.all(color: step.color, width: 3) : null,
        boxShadow:
            isCurrent ? [BoxShadow(color: step.color.withValues(alpha: 0.3), blurRadius: 8, spreadRadius: 2)] : null,
      ),
      child: Icon(
        isCompleted ? step.completedIcon : step.icon,
        color: isCompleted ? Colors.white : (isDark ? Colors.grey[400] : Colors.grey[600]),
        size: stepSize * 0.5,
      ),
    );
  }

  Widget _buildStepContent(TimelineStep step, bool isCompleted, bool isCurrent) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          step.title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: isCurrent ? FontWeight.bold : FontWeight.w600,
            color: isCompleted ? step.color : (isDark ? ThemeProvider.darkTextPrimary : Colors.black87),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          step.description,
          style: TextStyle(fontSize: 14, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
        ),
        if (step.timestamp != null) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.access_time, size: 14, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[500]),
              const SizedBox(width: 4),
              Text(
                _formatTimestamp(step.timestamp!),
                style: TextStyle(fontSize: 12, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[500]),
              ),
            ],
          ),
        ],
      ],
    );
  }

  List<TimelineStep> _getTimelineSteps() {
    if (isArabic) {
      return [
        TimelineStep(
          title: 'تم إنشاء الطلب',
          description: 'تم إنشاء الطلب بنجاح',
          icon: Icons.assignment,
          completedIcon: Icons.check,
          color: ThemeProvider.primaryBlue,
          timestamp: order.createdAt,
        ),
        TimelineStep(
          title: 'تعيين المستقل',
          description: 'تم تعيين مستقل للعمل على الطلب',
          icon: Icons.person_add,
          completedIcon: Icons.check,
          color: ThemeProvider.primaryBlue,
          timestamp: order.createdAt, // In real app, this would be assignment date
        ),
        TimelineStep(
          title: 'في انتظار الدفع',
          description: 'في انتظار تأكيد الدفع',
          icon: Icons.payment,
          completedIcon: Icons.check,
          color: ThemeProvider.warningOrange,
          timestamp: order.paymentStatus == PaymentStatus.confirmed ? order.createdAt : null,
        ),
        TimelineStep(
          title: 'تأكيد الدفع',
          description: 'تم تأكيد الدفع بنجاح',
          icon: Icons.verified,
          completedIcon: Icons.check,
          color: ThemeProvider.successGreen,
          timestamp: order.paymentStatus == PaymentStatus.confirmed ? order.createdAt : null,
        ),
        TimelineStep(
          title: 'قيد التنفيذ',
          description: 'المستقل يعمل على الطلب',
          icon: Icons.work,
          completedIcon: Icons.check,
          color: ThemeProvider.primaryBlue,
          timestamp:
              order.status == OrderStatus.inProgress ||
                      order.status == OrderStatus.delivered ||
                      order.status == OrderStatus.completed
                  ? order.createdAt
                  : null,
        ),
        TimelineStep(
          title: 'تم التسليم',
          description: 'تم تسليم العمل للمراجعة',
          icon: Icons.local_shipping,
          completedIcon: Icons.check,
          color: ThemeProvider.successGreen,
          timestamp: order.deliveryDate,
        ),
        TimelineStep(
          title: 'قيد المراجعة',
          description: 'العميل يراجع العمل المسلم',
          icon: Icons.rate_review,
          completedIcon: Icons.check,
          color: Colors.purple,
          timestamp: order.status == OrderStatus.delivered ? order.deliveryDate : null,
        ),
        TimelineStep(
          title: 'مكتمل',
          description: 'تم إكمال الطلب بنجاح',
          icon: Icons.done_all,
          completedIcon: Icons.check,
          color: ThemeProvider.successGreen,
          timestamp: order.status == OrderStatus.completed ? order.updatedAt : null,
        ),
      ];
    } else {
      return [
        TimelineStep(
          title: 'Order Created',
          description: 'Order has been created successfully',
          icon: Icons.assignment,
          completedIcon: Icons.check,
          color: ThemeProvider.primaryBlue,
          timestamp: order.createdAt,
        ),
        TimelineStep(
          title: 'Freelancer Assigned',
          description: 'A freelancer has been assigned to work on your order',
          icon: Icons.person_add,
          completedIcon: Icons.check,
          color: ThemeProvider.primaryBlue,
          timestamp: order.createdAt, // In real app, this would be assignment date
        ),
        TimelineStep(
          title: 'Waiting for Payment',
          description: 'Waiting for payment confirmation',
          icon: Icons.payment,
          completedIcon: Icons.check,
          color: ThemeProvider.warningOrange,
          timestamp: order.paymentStatus == PaymentStatus.confirmed ? order.createdAt : null,
        ),
        TimelineStep(
          title: 'Payment Confirmed',
          description: 'Payment has been confirmed successfully',
          icon: Icons.verified,
          completedIcon: Icons.check,
          color: ThemeProvider.successGreen,
          timestamp: order.paymentStatus == PaymentStatus.confirmed ? order.createdAt : null,
        ),
        TimelineStep(
          title: 'In Progress',
          description: 'Freelancer is working on your order',
          icon: Icons.work,
          completedIcon: Icons.check,
          color: ThemeProvider.primaryBlue,
          timestamp:
              order.status == OrderStatus.inProgress ||
                      order.status == OrderStatus.delivered ||
                      order.status == OrderStatus.completed
                  ? order.createdAt
                  : null,
        ),
        TimelineStep(
          title: 'Delivered',
          description: 'Work has been delivered for review',
          icon: Icons.local_shipping,
          completedIcon: Icons.check,
          color: ThemeProvider.successGreen,
          timestamp: order.deliveryDate,
        ),
        TimelineStep(
          title: 'In Review',
          description: 'Client is reviewing the delivered work',
          icon: Icons.rate_review,
          completedIcon: Icons.check,
          color: Colors.purple,
          timestamp: order.status == OrderStatus.delivered ? order.deliveryDate : null,
        ),
        TimelineStep(
          title: 'Completed',
          description: 'Order has been completed successfully',
          icon: Icons.done_all,
          completedIcon: Icons.check,
          color: ThemeProvider.successGreen,
          timestamp: order.status == OrderStatus.completed ? order.updatedAt : null,
        ),
      ];
    }
  }

  int _getCurrentStepIndex() {
    switch (order.status) {
      case OrderStatus.created:
        return 0;
      case OrderStatus.paymentPending:
        return order.paymentStatus == PaymentStatus.confirmed ? 3 : 2;
      case OrderStatus.paymentConfirmed:
        return 3;
      case OrderStatus.inProgress:
        return 4;
      case OrderStatus.submitted:
        return 5; // Work submitted for review
      case OrderStatus.delivered:
        return 6;
      case OrderStatus.editing:
        return 5; // Back to in progress for revisions
      case OrderStatus.completed:
        return 7;
      case OrderStatus.cancelled:
        return 0;
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    return '${timestamp.day}/${timestamp.month}/${timestamp.year} ${timestamp.hour}:${timestamp.minute.toString().padLeft(2, '0')}';
  }
}

class TimelineStep {
  final String title;
  final String description;
  final IconData icon;
  final IconData completedIcon;
  final Color color;
  final DateTime? timestamp;

  TimelineStep({
    required this.title,
    required this.description,
    required this.icon,
    required this.completedIcon,
    required this.color,
    this.timestamp,
  });
}

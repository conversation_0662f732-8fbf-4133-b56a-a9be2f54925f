# صفحة الخدمات الرئيسية - Services Screen

## نظرة عامة
تم إنشاء صفحة خدمات شاملة تعرض جميع الخدمات المقدمة في المنصة مع دعم كامل للغتين العربية والإنجليزية.

## الخدمات المتوفرة

### 🎓 الخدمات الأكاديمية
1. **توفير المصادر والمراجع الأكاديمية** - Academic Sources and References
2. **التقارير العلمية** - Scientific Reports  
3. **تلخيص كتب - مقالات - محاضرات** - Summarizing Books, Articles, and Lectures
4. **مشاريع علمية** - Scientific Projects
5. **الشرح والدورات** - Tutorials and Private Lessons

### 💻 الخدمات التقنية
1. **البرمجة وتصميم المواقع** - Programming and Web Design
2. **تحليل إحصائي SPSS** - Statistical Analysis (SPSS)

### 🌐 الخدمات اللغوية
1. **اللغات والترجمة** - Languages and Translation
2. **تدقيق لغوي** - Proofreading and Language Editing

### 🎨 خدمات التصميم
1. **الخرائط الذهنية** - Mind Maps
2. **عروض تقديمية (PowerPoint)** - Presentations (PowerPoint)

## الملفات المنشأة

### 1. `services_screen.dart`
- الصفحة الرئيسية لعرض جميع الخدمات
- شريط بحث للبحث في الخدمات
- فلترة حسب الفئات
- عرض شبكي للخدمات مع الأيقونات والألوان
- دعم كامل للغتين العربية والإنجليزية

### 2. `service_detail_screen.dart`
- صفحة تفاصيل الخدمة الفردية
- عرض مفصل لكل خدمة مع الميزات
- معلومات التسليم والأسعار
- أزرار الطلب والعودة

### 3. `welcome_services_screen.dart`
- صفحة ترحيبية تعرض نظرة عامة على الخدمات
- عرض فئات الخدمات
- ميزات المنصة
- دعوة للعمل للاستكشاف

## المميزات

### 🔍 البحث والفلترة
- بحث نصي في أسماء ووصف الخدمات
- فلترة حسب الفئات (أكاديمي، تقني، لغوي، تصميم)
- عرض النتائج الفارغة مع رسائل توضيحية

### 🎨 التصميم
- تصميم حديث مع Material 3
- ألوان مميزة لكل خدمة
- أيقونات معبرة
- تدرجات لونية جذابة
- تخطيط شبكي متجاوب

### 🌍 دعم اللغات
- دعم كامل للعربية والإنجليزية
- تخطيط RTL للعربية
- ترجمة شاملة لجميع النصوص

### 📱 تجربة المستخدم
- تنقل سلس بين الصفحات
- رسائل تأكيد عند اختيار الخدمات
- أزرار واضحة للطلب
- تحديث فوري للواجهة

## كيفية الاستخدام

### للعملاء:
1. الدخول إلى لوحة تحكم العميل
2. النقر على تبويب "Services" أو "الخدمات"
3. تصفح الخدمات أو البحث عن خدمة محددة
4. النقر على الخدمة لعرض التفاصيل
5. النقر على "اطلب الآن" لبدء طلب الخدمة

### للمطورين:
```dart
// للانتقال إلى صفحة الخدمات
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const ServicesScreen(),
  ),
);

// للانتقال إلى تفاصيل خدمة محددة
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => ServiceDetailScreen(service: serviceItem),
  ),
);
```

## التكامل مع النظام

### في `client_dashboard.dart`:
- تم إضافة تبويب جديد للخدمات
- زر سريع للوصول إلى الخدمات من الصفحة الرئيسية
- تحديث شريط التنقل السفلي

### في `pubspec.yaml`:
- تم إضافة مسار الصور: `assets/images/`

## الصور المطلوبة
يجب إضافة الصور التالية في مجلد `assets/images/`:
- academic_sources.png
- scientific_reports.png
- mind_maps.png
- translation.png
- summarization.png
- scientific_projects.png
- presentations.png
- spss_analysis.png
- proofreading.png
- programming.png
- tutorials.png

## التطوير المستقبلي
- إضافة تقييمات للخدمات
- نظام المفضلة
- مقارنة الخدمات
- فلترة متقدمة حسب السعر والوقت
- إضافة خدمات جديدة بسهولة

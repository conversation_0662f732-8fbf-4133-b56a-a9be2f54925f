import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:anotherchance/screens/client/my_orders_screen.dart';
import 'package:anotherchance/providers/language_provider.dart';
import 'package:anotherchance/utils/app_localizations.dart';

void main() {
  group('MyOrdersScreen AppBar Tests', () {
    testWidgets('AppBar displays correctly with title and tabs', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [ChangeNotifierProvider(create: (_) => LanguageProvider())],
          child: const MaterialApp(
            localizationsDelegates: [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: [Locale('en'), Locale('ar')],
            home: MyOrdersScreen(),
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Test AppBar title
      expect(find.text('My Orders'), findsOneWidget);

      // Test TabBar tabs
      expect(find.text('Active'), findsOneWidget);
      expect(find.text('Delivered'), findsOneWidget);
      expect(find.text('Completed'), findsOneWidget);
      expect(find.text('Cancelled'), findsOneWidget);

      // Test tab icons (there might be multiple icons, so we check for at least one)
      expect(find.byIcon(Icons.assignment), findsWidgets);
      expect(find.byIcon(Icons.upload), findsWidgets);
      expect(find.byIcon(Icons.check_circle), findsWidgets);
      expect(find.byIcon(Icons.cancel), findsWidgets);
    });

    testWidgets('AppBar works in Arabic language', (WidgetTester tester) async {
      final languageProvider = LanguageProvider();
      languageProvider.setLanguage('ar');

      await tester.pumpWidget(
        MultiProvider(
          providers: [ChangeNotifierProvider.value(value: languageProvider)],
          child: const MaterialApp(
            localizationsDelegates: [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: [Locale('en'), Locale('ar')],
            locale: Locale('ar'),
            home: MyOrdersScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Test Arabic title
      expect(find.text('طلباتي'), findsOneWidget);

      // Test Arabic tab labels
      expect(find.text('نشطة'), findsOneWidget);
      expect(find.text('مُسلَّمة'), findsOneWidget);
      expect(find.text('مكتملة'), findsOneWidget);
      expect(find.text('ملغية'), findsOneWidget);
    });

    testWidgets('TabBar responds to taps', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [ChangeNotifierProvider(create: (_) => LanguageProvider())],
          child: const MaterialApp(
            localizationsDelegates: [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: [Locale('en'), Locale('ar')],
            home: MyOrdersScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Tap on different tabs
      await tester.tap(find.text('Delivered'));
      await tester.pumpAndSettle();

      await tester.tap(find.text('Completed'));
      await tester.pumpAndSettle();

      await tester.tap(find.text('Cancelled'));
      await tester.pumpAndSettle();

      // Should not throw any errors
      expect(tester.takeException(), isNull);
    });

    testWidgets('AppBar gradient and styling work correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [ChangeNotifierProvider(create: (_) => LanguageProvider())],
          child: const MaterialApp(
            localizationsDelegates: [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: [Locale('en'), Locale('ar')],
            home: MyOrdersScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find TabBar (we're using custom header, not AppBar)
      final tabBarFinder = find.byType(TabBar);
      expect(tabBarFinder, findsOneWidget);

      // Find DefaultTabController
      final tabControllerFinder = find.byType(DefaultTabController);
      expect(tabControllerFinder, findsOneWidget);

      // Verify no overflow or rendering issues
      expect(tester.takeException(), isNull);
    });
  });
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import '../../providers/language_provider.dart';
import '../../providers/theme_provider.dart';
import '../../models/order_model.dart';
import '../../services/order_service.dart';

class DeliveryConfirmationWidget extends StatefulWidget {
  final OrderModel order;
  final VoidCallback? onDeliveryConfirmed;

  const DeliveryConfirmationWidget({super.key, required this.order, this.onDeliveryConfirmed});

  @override
  State<DeliveryConfirmationWidget> createState() => _DeliveryConfirmationWidgetState();
}

class _DeliveryConfirmationWidgetState extends State<DeliveryConfirmationWidget> {
  final TextEditingController _notesController = TextEditingController();
  final List<PlatformFile> _selectedFiles = [];
  final List<String> _uploadedFileUrls = [];
  bool _isUploading = false;
  bool _isConfirming = false;

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _pickFiles() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        allowMultiple: true,
        type: FileType.any,
        allowedExtensions: null,
      );

      if (result != null) {
        setState(() {
          _selectedFiles.addAll(result.files);
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error picking files: $e')));
      }
    }
  }

  Future<void> _uploadFiles() async {
    if (_selectedFiles.isEmpty) return;

    setState(() {
      _isUploading = true;
    });

    try {
      for (final file in _selectedFiles) {
        // In a real app, this would upload to cloud storage
        // For demo purposes, we'll simulate the upload
        await Future.delayed(const Duration(seconds: 1));
        final mockUrl = 'https://example.com/uploads/${file.name}';
        _uploadedFileUrls.add(mockUrl);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error uploading files: $e')));
      }
    } finally {
      setState(() {
        _isUploading = false;
      });
    }
  }

  Future<void> _confirmDelivery() async {
    print('=== DEBUG: Delivery confirmation started ===');
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    final isArabic = languageProvider.isArabic;

    if (_selectedFiles.isNotEmpty && _uploadedFileUrls.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(isArabic ? 'يرجى رفع الملفات أولاً' : 'Please upload files first')));
      return;
    }

    setState(() {
      _isConfirming = true;
    });

    try {
      // Upload files if any are selected
      if (_selectedFiles.isNotEmpty && _uploadedFileUrls.isEmpty) {
        await _uploadFiles();
      }

      // Update order status to delivered
      final deliveryUrl = _uploadedFileUrls.isNotEmpty ? _uploadedFileUrls.first : null;
      print('=== DEBUG: Calling OrderService.deliverOrder for order: ${widget.order.id} ===');
      final updatedOrder = await OrderService.deliverOrder(
        widget.order.id,
        deliveryUrl ?? '',
        _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
      );
      print('=== DEBUG: Order delivered successfully, new status: ${updatedOrder.status} ===');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isArabic ? 'تم تأكيد التسليم بنجاح' : 'Delivery confirmed successfully'),
            backgroundColor: Colors.green,
          ),
        );
        print('=== DEBUG: Calling onDeliveryConfirmed callback ===');
        widget.onDeliveryConfirmed?.call();
      }
    } catch (e) {
      print('=== DEBUG: Error in delivery confirmation: $e ===');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error confirming delivery: $e')));
      }
    } finally {
      setState(() {
        _isConfirming = false;
      });
    }
  }

  void _removeFile(int index) {
    setState(() {
      _selectedFiles.removeAt(index);
      if (index < _uploadedFileUrls.length) {
        _uploadedFileUrls.removeAt(index);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isArabic = languageProvider.isArabic;

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: themeProvider.isDarkMode ? Colors.grey[800] : Colors.green[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.green.withValues(alpha: 0.3), width: 1),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(Icons.local_shipping, color: Colors.green[600], size: 24),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    isArabic ? 'تأكيد التسليم' : 'Confirm Delivery',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.green[700]),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Description
            Text(
              isArabic
                  ? 'يمكنك الآن تسليم العمل المكتمل للعميل. أرفق الملفات النهائية وأضف أي ملاحظات.'
                  : 'You can now deliver the completed work to the client. Attach final files and add any notes.',
              style: TextStyle(fontSize: 14, color: themeProvider.isDarkMode ? Colors.grey[300] : Colors.grey[600]),
            ),
            const SizedBox(height: 16),

            // File Upload Section
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: themeProvider.isDarkMode ? Colors.grey[700] : Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.attach_file,
                        size: 20,
                        color: themeProvider.isDarkMode ? Colors.grey[300] : Colors.grey[600],
                      ),
                      const SizedBox(width: 8),
                      Text(
                        isArabic ? 'الملفات المرفقة' : 'Attached Files',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: themeProvider.isDarkMode ? Colors.grey[300] : Colors.grey[700],
                        ),
                      ),
                      const Spacer(),
                      TextButton.icon(
                        onPressed: _isUploading ? null : _pickFiles,
                        icon: const Icon(Icons.add, size: 16),
                        label: Text(isArabic ? 'إضافة ملف' : 'Add File', style: const TextStyle(fontSize: 12)),
                      ),
                    ],
                  ),
                  if (_selectedFiles.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    ...List.generate(_selectedFiles.length, (index) {
                      final file = _selectedFiles[index];
                      final isUploaded = index < _uploadedFileUrls.length;
                      return Container(
                        margin: const EdgeInsets.only(bottom: 4),
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: isUploaded ? Colors.green[100] : Colors.grey[100],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              isUploaded ? Icons.check_circle : Icons.insert_drive_file,
                              size: 16,
                              color: isUploaded ? Colors.green : Colors.grey[600],
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                file.name,
                                style: const TextStyle(fontSize: 12),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            IconButton(
                              onPressed: () => _removeFile(index),
                              icon: const Icon(Icons.close, size: 16),
                              constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
                              padding: EdgeInsets.zero,
                            ),
                          ],
                        ),
                      );
                    }),
                  ],
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Notes Section
            TextField(
              controller: _notesController,
              maxLines: 3,
              decoration: InputDecoration(
                labelText: isArabic ? 'ملاحظات التسليم (اختياري)' : 'Delivery Notes (Optional)',
                hintText:
                    isArabic
                        ? 'أضف أي ملاحظات أو تعليمات للعميل...'
                        : 'Add any notes or instructions for the client...',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                filled: true,
                fillColor: themeProvider.isDarkMode ? Colors.grey[700] : Colors.white,
              ),
            ),
            const SizedBox(height: 16),

            // Action Buttons
            Row(
              children: [
                if (_selectedFiles.isNotEmpty && _uploadedFileUrls.length < _selectedFiles.length)
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _isUploading ? null : _uploadFiles,
                      icon:
                          _isUploading
                              ? const SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2))
                              : const Icon(Icons.cloud_upload, size: 16),
                      label: Text(
                        _isUploading
                            ? (isArabic ? 'جاري الرفع...' : 'Uploading...')
                            : (isArabic ? 'رفع الملفات' : 'Upload Files'),
                      ),
                      style: ElevatedButton.styleFrom(backgroundColor: Colors.blue, foregroundColor: Colors.white),
                    ),
                  ),
                if (_selectedFiles.isNotEmpty && _uploadedFileUrls.length < _selectedFiles.length)
                  const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isConfirming ? null : _confirmDelivery,
                    icon:
                        _isConfirming
                            ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                            )
                            : const Icon(Icons.check, size: 16),
                    label: Text(
                      _isConfirming
                          ? (isArabic ? 'جاري التأكيد...' : 'Confirming...')
                          : (isArabic ? 'تأكيد التسليم' : 'Confirm Delivery'),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import '../../models/order_model.dart';
import '../../providers/theme_provider.dart';
import '../../widgets/common/enhanced_widgets.dart';

class OrderNotifications extends StatelessWidget {
  final OrderModel order;
  final bool isArabic;
  final bool isDark;
  final List<OrderNotificationItem> notifications;
  final VoidCallback? onMarkAllRead;
  final Function(String)? onNotificationTap;

  const OrderNotifications({
    super.key,
    required this.order,
    required this.isArabic,
    required this.isDark,
    required this.notifications,
    this.onMarkAllRead,
    this.onNotificationTap,
  });

  @override
  Widget build(BuildContext context) {
    final unreadNotifications = notifications.where((n) => !n.isRead).toList();
    
    if (unreadNotifications.isEmpty) {
      return const SizedBox.shrink();
    }

    return EnhancedCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    isArabic ? 'تنبيهات جديدة' : 'New Notifications',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${unreadNotifications.length}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              if (onMarkAllRead != null)
                TextButton(
                  onPressed: onMarkAllRead,
                  child: Text(
                    isArabic ? 'تحديد الكل كمقروء' : 'Mark All Read',
                    style: const TextStyle(
                      color: ThemeProvider.primaryBlue,
                      fontSize: 12,
                    ),
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Notifications List
          ...unreadNotifications.take(3).map((notification) => 
            _buildNotificationItem(notification)
          ),
          
          // Show more button
          if (unreadNotifications.length > 3) ...[
            const SizedBox(height: 12),
            Center(
              child: TextButton(
                onPressed: () => _showAllNotifications(context),
                child: Text(
                  isArabic 
                      ? 'عرض جميع التنبيهات (${unreadNotifications.length - 3} أخرى)'
                      : 'View All Notifications (${unreadNotifications.length - 3} more)',
                  style: const TextStyle(color: ThemeProvider.primaryBlue),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildNotificationItem(OrderNotificationItem notification) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => onNotificationTap?.call(notification.id),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: _getNotificationBackgroundColor(notification.type),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _getNotificationColor(notification.type).withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            children: [
              // Icon
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: _getNotificationColor(notification.type).withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _getNotificationIcon(notification.type),
                  color: _getNotificationColor(notification.type),
                  size: 20,
                ),
              ),
              
              const SizedBox(width: 12),
              
              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isArabic ? notification.titleAr : notification.title,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      isArabic ? notification.messageAr : notification.message,
                      style: TextStyle(
                        fontSize: 14,
                        color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[700],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _formatNotificationTime(notification.timestamp),
                      style: TextStyle(
                        fontSize: 12,
                        color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[500],
                      ),
                    ),
                  ],
                ),
              ),
              
              // Priority indicator
              if (notification.priority == NotificationPriority.high)
                Container(
                  width: 8,
                  height: 8,
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getNotificationColor(OrderNotificationType type) {
    switch (type) {
      case OrderNotificationType.delivery:
        return ThemeProvider.successGreen;
      case OrderNotificationType.message:
        return ThemeProvider.primaryBlue;
      case OrderNotificationType.payment:
        return ThemeProvider.warningOrange;
      case OrderNotificationType.revision:
        return Colors.purple;
      case OrderNotificationType.completion:
        return ThemeProvider.successGreen;
      case OrderNotificationType.cancellation:
        return Colors.red;
      case OrderNotificationType.reminder:
        return Colors.amber;
    }
  }

  Color _getNotificationBackgroundColor(OrderNotificationType type) {
    return _getNotificationColor(type).withValues(alpha: 0.05);
  }

  IconData _getNotificationIcon(OrderNotificationType type) {
    switch (type) {
      case OrderNotificationType.delivery:
        return Icons.local_shipping;
      case OrderNotificationType.message:
        return Icons.message;
      case OrderNotificationType.payment:
        return Icons.payment;
      case OrderNotificationType.revision:
        return Icons.edit;
      case OrderNotificationType.completion:
        return Icons.check_circle;
      case OrderNotificationType.cancellation:
        return Icons.cancel;
      case OrderNotificationType.reminder:
        return Icons.notifications;
    }
  }

  String _formatNotificationTime(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inMinutes < 60) {
      return isArabic ? 'منذ ${difference.inMinutes} دقيقة' : '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return isArabic ? 'منذ ${difference.inHours} ساعة' : '${difference.inHours}h ago';
    } else {
      return isArabic ? 'منذ ${difference.inDays} يوم' : '${difference.inDays}d ago';
    }
  }

  void _showAllNotifications(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: isDark ? ThemeProvider.darkCardBackground : Colors.white,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // Handle
              Container(
                margin: const EdgeInsets.symmetric(vertical: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              
              // Header
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      isArabic ? 'جميع التنبيهات' : 'All Notifications',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),
              
              // Notifications List
              Expanded(
                child: ListView.builder(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  itemCount: notifications.length,
                  itemBuilder: (context, index) {
                    return _buildNotificationItem(notifications[index]);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class OrderNotificationItem {
  final String id;
  final String orderId;
  final OrderNotificationType type;
  final String title;
  final String titleAr;
  final String message;
  final String messageAr;
  final DateTime timestamp;
  final bool isRead;
  final NotificationPriority priority;
  final Map<String, dynamic>? data;

  OrderNotificationItem({
    required this.id,
    required this.orderId,
    required this.type,
    required this.title,
    required this.titleAr,
    required this.message,
    required this.messageAr,
    required this.timestamp,
    this.isRead = false,
    this.priority = NotificationPriority.normal,
    this.data,
  });
}

enum OrderNotificationType {
  delivery,
  message,
  payment,
  revision,
  completion,
  cancellation,
  reminder,
}

enum NotificationPriority {
  low,
  normal,
  high,
}

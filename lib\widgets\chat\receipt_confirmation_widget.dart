import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/language_provider.dart';
import '../../providers/theme_provider.dart';
import '../../models/order_model.dart';
import '../../services/order_service.dart';

class ReceiptConfirmationWidget extends StatefulWidget {
  final OrderModel order;
  final VoidCallback? onReceiptConfirmed;

  const ReceiptConfirmationWidget({super.key, required this.order, this.onReceiptConfirmed});

  @override
  State<ReceiptConfirmationWidget> createState() => _ReceiptConfirmationWidgetState();
}

class _ReceiptConfirmationWidgetState extends State<ReceiptConfirmationWidget> {
  bool _isConfirming = false;

  Future<void> _confirmReceipt() async {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    final isArabic = languageProvider.isArabic;

    setState(() {
      _isConfirming = true;
    });

    try {
      // Show rating dialog first
      final result = await _showRatingDialog();

      if (result != null) {
        // Complete the order with rating and review
        await OrderService.completeOrder(widget.order.id, result['rating'] as double, result['review'] as String?);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(isArabic ? 'تم تأكيد الاستلام بنجاح' : 'Receipt confirmed successfully'),
              backgroundColor: Colors.green,
            ),
          );
          widget.onReceiptConfirmed?.call();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error confirming receipt: $e')));
      }
    } finally {
      setState(() {
        _isConfirming = false;
      });
    }
  }

  Future<Map<String, dynamic>?> _showRatingDialog() async {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    final isArabic = languageProvider.isArabic;

    double rating = 5.0;
    final reviewController = TextEditingController();

    return showDialog<Map<String, dynamic>>(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => Directionality(
            textDirection: languageProvider.textDirection,
            child: AlertDialog(
              title: Text(isArabic ? 'تقييم العمل' : 'Rate the Work'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isArabic
                          ? 'كيف تقيم جودة العمل المسلم؟'
                          : 'How would you rate the quality of the delivered work?',
                      style: const TextStyle(fontSize: 14),
                    ),
                    const SizedBox(height: 16),

                    // Star Rating
                    StatefulBuilder(
                      builder:
                          (context, setState) => Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: List.generate(5, (index) {
                              return GestureDetector(
                                onTap: () => setState(() => rating = index + 1.0),
                                child: Icon(
                                  index < rating ? Icons.star : Icons.star_border,
                                  color: Colors.amber,
                                  size: 32,
                                ),
                              );
                            }),
                          ),
                    ),
                    const SizedBox(height: 16),

                    // Review Text Field
                    TextField(
                      controller: reviewController,
                      maxLines: 3,
                      decoration: InputDecoration(
                        labelText: isArabic ? 'تعليق (اختياري)' : 'Review (Optional)',
                        hintText:
                            isArabic
                                ? 'شاركنا رأيك في العمل المسلم...'
                                : 'Share your thoughts about the delivered work...',
                        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(onPressed: () => Navigator.of(context).pop(), child: Text(isArabic ? 'إلغاء' : 'Cancel')),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop({
                      'rating': rating,
                      'review': reviewController.text.trim().isEmpty ? null : reviewController.text.trim(),
                    });
                  },
                  child: Text(isArabic ? 'تأكيد' : 'Confirm'),
                ),
              ],
            ),
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isArabic = languageProvider.isArabic;

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: themeProvider.isDarkMode ? Colors.grey[800] : Colors.green[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.green.withValues(alpha: 0.3), width: 1),
        ),
        child: SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _isConfirming ? null : _confirmReceipt,
            icon:
                _isConfirming
                    ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                    )
                    : const Icon(Icons.check_circle, size: 18),
            label: Text(
              _isConfirming
                  ? (isArabic ? 'جاري التأكيد...' : 'Confirming...')
                  : (isArabic ? 'تأكيد التسليم' : 'Confirm Delivery'),
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green[600],
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 14),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            ),
          ),
        ),
      ),
    );
  }
}

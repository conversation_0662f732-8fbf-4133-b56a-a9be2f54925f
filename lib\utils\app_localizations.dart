import 'package:flutter/material.dart';

class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  static const List<Locale> supportedLocales = [Locale('en'), Locale('ar')];

  // English translations
  static const Map<String, String> _englishTexts = {
    'appTitle': 'Taskly',
    'welcomeBack': 'Welcome Back',
    'signInToContinue': 'Sign in to continue to Taskly',
    'home': 'Home',
    'email': 'Email',
    'password': 'Password',
    'signIn': 'Sign In',
    'signUp': 'Sign Up',
    'dontHaveAccount': "Don't have an account?",
    'alreadyHaveAccount': 'Already have an account?',
    'createAccount': 'Create Account',
    'joinTaskly': 'Join Taskly',
    'createYourAccount': 'Create your account to get started',
    'fullName': 'Full Name',
    'iWantTo': 'I want to',
    'hireFreelancers': 'Hire freelancers (Client)',
    'offerServices': 'Offer services (Freelancer)',
    'confirmPassword': 'Confirm Password',
    'quickDemoLogin': 'Quick Demo Login',
    'loginAsClient': 'Login as Client',
    'loginAsFreelancer': 'Login as Freelancer',
    'loginAsAdmin': 'Login as Admin',
    'dashboard': 'Dashboard',
    'requests': 'Requests',
    'orders': 'Orders',
    'messages': 'Messages',
    'browse': 'Browse',
    'offers': 'Offers',
    'jobs': 'Jobs',
    'payments': 'Payments',
    'users': 'Users',
    'readyToFindFreelancer': 'Ready to find the perfect freelancer for your project?',
    'postNewRequest': 'Post New Request',
    'activeOrders': 'Active Orders',
    'totalRequests': 'Total Requests',
    'recentRequests': 'Recent Requests',
    'noRequestsYet': 'No requests yet',
    'createFirstRequest': 'Create your first service request to get started',
    'findNextOpportunity': 'Find your next opportunity and grow your freelance career.',
    'browseJobs': 'Browse Jobs',
    'profile': 'Profile',
    'activeJobs': 'Active Jobs',
    'pendingOffers': 'Pending Offers',
    'latestOpportunities': 'Latest Opportunities',
    'noOpportunitiesAvailable': 'No opportunities available',
    'checkBackLater': 'Check back later for new project requests',
    'recentOffers': 'Recent Offers',
    'adminControlCenter': 'Admin Control Center',
    'managePlatform': 'Manage users, verify payments, and oversee platform operations.',
    'totalUsers': 'Total Users',
    'totalOrders': 'Total Orders',
    'revenue': 'Revenue',
    'paymentVerificationRequired': 'Payment Verification Required',
    'review': 'Review',
    'quickActions': 'Quick Actions',
    'verifyPayments': 'Verify Payments',
    'manageUsers': 'Manage Users',
    'orderManagement': 'Order Management',
    'systemMessages': 'System Messages',
    'recentActivity': 'Recent Activity',
    'logout': 'Logout',
    'settings': 'Settings',
    'language': 'Language',
    'english': 'English',
    'arabic': 'العربية',
    'darkMode': 'Dark Mode',
    'lightMode': 'Light Mode',
    'payNow': 'Pay Now',
    'paymentRequired': 'Payment Required',
    'paymentProcessing': 'Payment Processing',
    'paymentConfirmed': 'Payment Confirmed',
    'uploadPaymentProof': 'Upload Payment Proof',
    'bankTransferDetails': 'Bank Transfer Details',
    'paymentAmount': 'Payment Amount',
    'adminSupport': 'Admin Support',
    'readyToProceed': 'Ready to proceed?',
    'clickPayNowToStart': 'Click Pay Now to start the payment process',
    'paymentProofUploaded': 'Payment proof uploaded',
    'paymentUnderReview': 'Payment Under Review',
    'freelancerNotified': 'Freelancer has been notified to start work',
    'uploadProof': 'Upload Proof',
    'uploading': 'Uploading...',
    'typeMessage': 'Type a message...',
    'noMessagesYet': 'No messages yet',
    'startConversation': 'Start a conversation with a freelancer or client',
    'mobileAppDevelopment': 'Mobile App Development',
    'logoDesign': 'Logo Design',
    'canYouStartProject': 'Can you start the project?',
    'sendDesignsToday': 'I\'ll send the designs today',
    'johnClient': 'John Client',
    'sarahDesigner': 'Sarah Designer',
    'minAgo': 'min ago',
    'hourAgo': 'hour ago',
    'hoursAgo': 'hours ago',

    // Additional translations for missing keys
    'adminDashboard': 'Admin Dashboard',
    'freelancerHub': 'Freelancer Hub',
    'welcomeFreelancer': 'Welcome',
    'newUserRegistration': 'New user registration',
    'joinedAsFreelancer': 'joined as freelancer',
    'paymentSubmitted': 'Payment submitted',
    'orderCompleted': 'Order completed',
    'orderMarkedCompleted': 'Order marked as completed',
    'minutesAgo': 'minutes ago',
    'requestRevision': 'Request Revision',
    'markComplete': 'Mark Complete',
    'revisionRequested': 'Revision Requested',
    'revisionNotes': 'Revision Notes',
    'enterRevisionNotes': 'Please describe what needs to be revised...',
    'submitRevision': 'Submit Revision Request',
    'revisionRequestSent': 'Revision request sent successfully',
    'editing': 'Editing',
    'cancel': 'Cancel',

    // Freelancer Profile and Payout System
    'earnings': 'Earnings',
    'availableBalance': 'Available Balance',
    'totalEarnings': 'Total Earnings',
    'withdrawn': 'Withdrawn',
    'requestWithdrawal': 'Request Withdrawal',
    'minimumWithdrawal': 'Minimum withdrawal',
    'performanceMetrics': 'Performance Metrics',
    'completedJobsCount': 'Completed Jobs',
    'ratingScore': 'Rating',
    'successRate': 'Success Rate',
    'responseTime': 'Response Time',
    'skillsAndServices': 'Skills & Services',
    'ratingsAndReviews': 'Ratings & Reviews',
    'reviewsCount': 'reviews',
    'noReviewsYet': 'No reviews yet',
    'viewAllReviews': 'View All Reviews',
    'clientUser': 'Client',
    'today': 'Today',
    'yesterday': 'Yesterday',
    'daysAgo': 'days ago',
    'verified': 'Verified',
    'jobsCount': 'jobs',
    'payoutRequest': 'Request Withdrawal',
    'newRequest': 'New Request',
    'history': 'History',
    'withdrawalAmount': 'Withdrawal Amount',
    'amount': 'Amount (SAR)',
    'invalidAmount': 'Invalid amount',
    'pleaseEnterAmount': 'Please enter amount',
    'amountExceedsBalance': 'Amount exceeds available balance',
    'payoutMethod': 'Payout Method',
    'vodafoneCash': 'Vodafone Cash',
    'instaPay': 'InstaPay',
    'accountDetails': 'Account Details',
    'phoneNumber': 'Phone Number',
    'emailAddress': 'Email Address',
    'pleaseEnterAccountDetails': 'Please enter account details',
    'invalidPhoneNumber': 'Invalid phone number',
    'invalidEmailAddress': 'Invalid email address',
    'identityConfirmation': 'Identity Confirmation',
    'pleaseEnterPassword': 'Please enter password',
    'submitting': 'Submitting...',
    'submitWithdrawalRequest': 'Submit Withdrawal Request',
    'withdrawalRequestSubmitted': 'Withdrawal request submitted successfully! It will be reviewed within 24-48 hours.',
    'error': 'Error',
    'noWithdrawalRequests': 'No withdrawal requests',
    'withdrawalRequestsWillAppear': 'Your withdrawal requests will appear here',
    'pending': 'Pending',
    'approved': 'Approved',
    'processing': 'Processing',
    'completed': 'Completed',
    'rejected': 'Rejected',
    'processed': 'Processed',
  };

  // Arabic translations
  static const Map<String, String> _arabicTexts = {
    'appTitle': 'Taskly',
    'welcomeBack': 'مرحباً بعودتك',
    'signInToContinue': 'سجل دخولك للمتابعة إلى Taskly',
    'home': 'الرئيسية',
    'email': 'البريد الإلكتروني',
    'password': 'كلمة المرور',
    'signIn': 'تسجيل الدخول',
    'signUp': 'إنشاء حساب',
    'dontHaveAccount': 'ليس لديك حساب؟',
    'alreadyHaveAccount': 'لديك حساب بالفعل؟',
    'createAccount': 'إنشاء حساب',
    'joinTaskly': 'انضم إلى Taskly',
    'createYourAccount': 'أنشئ حسابك للبدء',
    'fullName': 'الاسم الكامل',
    'iWantTo': 'أريد أن',
    'hireFreelancers': 'أوظف مستقلين (عميل)',
    'offerServices': 'أقدم خدمات (مستقل)',
    'confirmPassword': 'تأكيد كلمة المرور',
    'quickDemoLogin': 'تسجيل دخول سريع للتجربة',
    'loginAsClient': 'دخول كعميل',
    'loginAsFreelancer': 'دخول كمستقل',
    'loginAsAdmin': 'دخول كمدير',
    'dashboard': 'لوحة التحكم',
    'requests': 'الطلبات',
    'orders': 'الطلبات',
    'messages': 'الرسائل',
    'browse': 'تصفح',
    'offers': 'العروض',
    'jobs': 'الوظائف',
    'payments': 'المدفوعات',
    'users': 'المستخدمون',
    'readyToFindFreelancer': 'هل أنت مستعد للعثور على المستقل المثالي لمشروعك؟',
    'postNewRequest': 'نشر طلب جديد',
    'activeOrders': 'الطلبات النشطة',
    'totalRequests': 'إجمالي الطلبات',
    'recentRequests': 'الطلبات الأخيرة',
    'noRequestsYet': 'لا توجد طلبات بعد',
    'createFirstRequest': 'أنشئ أول طلب خدمة للبدء',
    'findNextOpportunity': 'ابحث عن فرصتك التالية وطور مسيرتك المهنية في العمل الحر.',
    'browseJobs': 'تصفح الوظائف',
    'profile': 'الملف الشخصي',
    'activeJobs': 'الوظائف النشطة',
    'pendingOffers': 'العروض المعلقة',
    'latestOpportunities': 'أحدث الفرص',
    'noOpportunitiesAvailable': 'لا توجد فرص متاحة',
    'checkBackLater': 'تحقق مرة أخرى لاحقاً للحصول على طلبات مشاريع جديدة',
    'recentOffers': 'العروض الأخيرة',
    'adminControlCenter': 'مركز التحكم الإداري',
    'managePlatform': 'إدارة المستخدمين، التحقق من المدفوعات، والإشراف على عمليات المنصة.',
    'totalUsers': 'إجمالي المستخدمين',
    'totalOrders': 'إجمالي الطلبات',
    'revenue': 'الإيرادات',
    'paymentVerificationRequired': 'مطلوب التحقق من الدفع',
    'review': 'مراجعة',
    'quickActions': 'إجراءات سريعة',
    'verifyPayments': 'التحقق من المدفوعات',
    'manageUsers': 'إدارة المستخدمين',
    'orderManagement': 'إدارة الطلبات',
    'systemMessages': 'رسائل النظام',
    'recentActivity': 'النشاط الأخير',
    'logout': 'تسجيل الخروج',
    'settings': 'الإعدادات',
    'language': 'اللغة',
    'english': 'English',
    'arabic': 'العربية',
    'darkMode': 'الوضع المظلم',
    'lightMode': 'الوضع المضيء',
    'payNow': 'ادفع الآن',
    'paymentRequired': 'مطلوب دفع',
    'paymentProcessing': 'معالجة الدفع',
    'paymentConfirmed': 'تم تأكيد الدفع',
    'uploadPaymentProof': 'رفع إثبات الدفع',
    'bankTransferDetails': 'تفاصيل التحويل البنكي',
    'paymentAmount': 'مبلغ الدفع',
    'adminSupport': 'دعم الإدارة',
    'readyToProceed': 'هل أنت مستعد للمتابعة؟',
    'clickPayNowToStart': 'انقر على ادفع الآن لبدء عملية الدفع',
    'paymentProofUploaded': 'تم رفع إثبات الدفع',
    'paymentUnderReview': 'الدفع قيد المراجعة',
    'freelancerNotified': 'تم إشعار المستقل لبدء العمل',
    'uploadProof': 'رفع الإثبات',
    'uploading': 'جاري الرفع...',
    'typeMessage': 'اكتب رسالة...',
    'noMessagesYet': 'لا توجد رسائل بعد',
    'startConversation': 'ابدأ محادثة مع مستقل أو عميل',
    'mobileAppDevelopment': 'تطوير تطبيقات الجوال',
    'logoDesign': 'تصميم الشعارات',
    'canYouStartProject': 'هل يمكنك بدء المشروع؟',
    'sendDesignsToday': 'سأرسل التصاميم اليوم',
    'johnClient': 'جون العميل',
    'sarahDesigner': 'سارة المصممة',
    'minAgo': 'منذ دقائق',
    'hourAgo': 'منذ ساعة',
    'hoursAgo': 'منذ ساعات',

    // Additional translations for missing keys
    'adminDashboard': 'لوحة تحكم المدير',
    'freelancerHub': 'مركز المستقلين',
    'welcomeFreelancer': 'مرحباً',
    'newUserRegistration': 'تسجيل مستخدم جديد',
    'joinedAsFreelancer': 'انضم كمستقل',
    'paymentSubmitted': 'تم إرسال الدفع',
    'orderCompleted': 'تم إكمال الطلب',
    'orderMarkedCompleted': 'تم وضع علامة مكتمل على الطلب',
    'minutesAgo': 'منذ دقائق',
    'requestRevision': 'طلب مراجعة',
    'markComplete': 'تحديد كمكتمل',
    'revisionRequested': 'تم طلب مراجعة',
    'revisionNotes': 'ملاحظات المراجعة',
    'enterRevisionNotes': 'يرجى وصف ما يحتاج إلى مراجعة...',
    'submitRevision': 'إرسال طلب المراجعة',
    'revisionRequestSent': 'تم إرسال طلب المراجعة بنجاح',
    'editing': 'قيد التعديل',
    'cancel': 'إلغاء',

    // Freelancer Profile and Payout System - Arabic
    'earnings': 'الأرباح',
    'availableBalance': 'الرصيد المتاح',
    'totalEarnings': 'إجمالي الأرباح',
    'withdrawn': 'تم سحبه',
    'requestWithdrawal': 'طلب سحب',
    'minimumWithdrawal': 'الحد الأدنى للسحب',
    'performanceMetrics': 'إحصائيات الأداء',
    'completedJobsCount': 'مهمة مكتملة',
    'ratingScore': 'التقييم',
    'successRate': 'معدل النجاح',
    'responseTime': 'وقت الاستجابة',
    'skillsAndServices': 'المهارات والخدمات',
    'ratingsAndReviews': 'التقييمات والمراجعات',
    'reviewsCount': 'تقييم',
    'noReviewsYet': 'لا توجد تقييمات بعد',
    'viewAllReviews': 'عرض جميع التقييمات',
    'clientUser': 'عميل',
    'today': 'اليوم',
    'yesterday': 'أمس',
    'daysAgo': 'أيام',
    'verified': 'موثق',
    'jobsCount': 'مهمة',
    'payoutRequest': 'طلب سحب الأرباح',
    'newRequest': 'طلب جديد',
    'history': 'السجل',
    'withdrawalAmount': 'مبلغ السحب',
    'amount': 'المبلغ (ريال سعودي)',
    'invalidAmount': 'مبلغ غير صحيح',
    'pleaseEnterAmount': 'يرجى إدخال المبلغ',
    'amountExceedsBalance': 'المبلغ أكبر من الرصيد المتاح',
    'payoutMethod': 'طريقة السحب',
    'vodafoneCash': 'فودافون كاش',
    'instaPay': 'إنستا باي',
    'accountDetails': 'تفاصيل الحساب',
    'phoneNumber': 'رقم الهاتف',
    'emailAddress': 'البريد الإلكتروني',
    'pleaseEnterAccountDetails': 'يرجى إدخال تفاصيل الحساب',
    'invalidPhoneNumber': 'رقم هاتف غير صحيح',
    'invalidEmailAddress': 'بريد إلكتروني غير صحيح',
    'identityConfirmation': 'تأكيد الهوية',
    'pleaseEnterPassword': 'يرجى إدخال كلمة المرور',
    'submitting': 'جاري الإرسال...',
    'submitWithdrawalRequest': 'إرسال طلب السحب',
    'withdrawalRequestSubmitted': 'تم إرسال طلب السحب بنجاح! سيتم مراجعته خلال 24-48 ساعة.',
    'error': 'حدث خطأ',
    'noWithdrawalRequests': 'لا توجد طلبات سحب',
    'withdrawalRequestsWillAppear': 'ستظهر طلبات السحب الخاصة بك هنا',
    'pending': 'قيد المراجعة',
    'approved': 'موافق عليه',
    'processing': 'قيد المعالجة',
    'completed': 'مكتمل',
    'rejected': 'مرفوض',
    'processed': 'تمت المعالجة',
  };

  String translate(String key) {
    final texts = locale.languageCode == 'ar' ? _arabicTexts : _englishTexts;
    return texts[key] ?? key;
  }

  // Convenience getters
  String get appTitle => translate('appTitle');
  String get welcomeBack => translate('welcomeBack');
  String get signInToContinue => translate('signInToContinue');
  String get home => translate('home');
  String get email => translate('email');
  String get password => translate('password');
  String get signIn => translate('signIn');
  String get signUp => translate('signUp');
  String get dontHaveAccount => translate('dontHaveAccount');
  String get alreadyHaveAccount => translate('alreadyHaveAccount');
  String get createAccount => translate('createAccount');
  String get joinTaskly => translate('joinTaskly');
  String get createYourAccount => translate('createYourAccount');
  String get fullName => translate('fullName');
  String get iWantTo => translate('iWantTo');
  String get hireFreelancers => translate('hireFreelancers');
  String get offerServices => translate('offerServices');
  String get confirmPassword => translate('confirmPassword');
  String get quickDemoLogin => translate('quickDemoLogin');
  String get loginAsClient => translate('loginAsClient');
  String get loginAsFreelancer => translate('loginAsFreelancer');
  String get loginAsAdmin => translate('loginAsAdmin');
  String get dashboard => translate('dashboard');
  String get requests => translate('requests');
  String get orders => translate('orders');
  String get messages => translate('messages');
  String get browse => translate('browse');
  String get offers => translate('offers');
  String get jobs => translate('jobs');
  String get payments => translate('payments');
  String get users => translate('users');
  String get readyToFindFreelancer => translate('readyToFindFreelancer');
  String get postNewRequest => translate('postNewRequest');
  String get activeOrders => translate('activeOrders');
  String get totalRequests => translate('totalRequests');
  String get recentRequests => translate('recentRequests');
  String get noRequestsYet => translate('noRequestsYet');
  String get createFirstRequest => translate('createFirstRequest');
  String get findNextOpportunity => translate('findNextOpportunity');
  String get browseJobs => translate('browseJobs');
  String get profile => translate('profile');
  String get activeJobs => translate('activeJobs');
  String get pendingOffers => translate('pendingOffers');
  String get latestOpportunities => translate('latestOpportunities');
  String get noOpportunitiesAvailable => translate('noOpportunitiesAvailable');
  String get checkBackLater => translate('checkBackLater');
  String get recentOffers => translate('recentOffers');
  String get adminControlCenter => translate('adminControlCenter');
  String get managePlatform => translate('managePlatform');
  String get totalUsers => translate('totalUsers');
  String get totalOrders => translate('totalOrders');
  String get revenue => translate('revenue');
  String get paymentVerificationRequired => translate('paymentVerificationRequired');
  String get review => translate('review');
  String get quickActions => translate('quickActions');
  String get verifyPayments => translate('verifyPayments');
  String get manageUsers => translate('manageUsers');
  String get orderManagement => translate('orderManagement');
  String get systemMessages => translate('systemMessages');
  String get recentActivity => translate('recentActivity');
  String get logout => translate('logout');
  String get settings => translate('settings');
  String get language => translate('language');
  String get english => translate('english');
  String get arabic => translate('arabic');
  String get darkMode => translate('darkMode');
  String get lightMode => translate('lightMode');
  String get payNow => translate('payNow');
  String get paymentRequired => translate('paymentRequired');
  String get paymentProcessing => translate('paymentProcessing');
  String get paymentConfirmed => translate('paymentConfirmed');
  String get uploadPaymentProof => translate('uploadPaymentProof');
  String get bankTransferDetails => translate('bankTransferDetails');
  String get paymentAmount => translate('paymentAmount');
  String get adminSupport => translate('adminSupport');
  String get readyToProceed => translate('readyToProceed');
  String get clickPayNowToStart => translate('clickPayNowToStart');
  String get paymentProofUploaded => translate('paymentProofUploaded');
  String get paymentUnderReview => translate('paymentUnderReview');
  String get freelancerNotified => translate('freelancerNotified');
  String get uploadProof => translate('uploadProof');
  String get uploading => translate('uploading');
  String get typeMessage => translate('typeMessage');
  String get adminDashboard => translate('adminDashboard');
  String get freelancerHub => translate('freelancerHub');
  String get newUserRegistration => translate('newUserRegistration');
  String get joinedAsFreelancer => translate('joinedAsFreelancer');
  String get paymentSubmitted => translate('paymentSubmitted');
  String get orderCompleted => translate('orderCompleted');
  String get orderMarkedCompleted => translate('orderMarkedCompleted');
  String get minutesAgo => translate('minutesAgo');
  String get requestRevision => translate('requestRevision');
  String get markComplete => translate('markComplete');
  String get revisionRequested => translate('revisionRequested');
  String get revisionNotes => translate('revisionNotes');
  String get enterRevisionNotes => translate('enterRevisionNotes');
  String get submitRevision => translate('submitRevision');
  String get revisionRequestSent => translate('revisionRequestSent');
  String get editing => translate('editing');
  String get cancel => translate('cancel');

  // Freelancer Profile and Payout System getters
  String get earnings => translate('earnings');
  String get availableBalance => translate('availableBalance');
  String get totalEarnings => translate('totalEarnings');
  String get withdrawn => translate('withdrawn');
  String get requestWithdrawal => translate('requestWithdrawal');
  String get minimumWithdrawal => translate('minimumWithdrawal');
  String get performanceMetrics => translate('performanceMetrics');
  String get completedJobsCount => translate('completedJobsCount');
  String get ratingScore => translate('ratingScore');
  String get successRate => translate('successRate');
  String get responseTime => translate('responseTime');
  String get skillsAndServices => translate('skillsAndServices');
  String get ratingsAndReviews => translate('ratingsAndReviews');
  String get reviewsCount => translate('reviewsCount');
  String get noReviewsYet => translate('noReviewsYet');
  String get viewAllReviews => translate('viewAllReviews');
  String get clientUser => translate('clientUser');
  String get today => translate('today');
  String get yesterday => translate('yesterday');
  String get daysAgo => translate('daysAgo');
  String get verified => translate('verified');
  String get jobsCount => translate('jobsCount');
  String get payoutRequest => translate('payoutRequest');
  String get newRequest => translate('newRequest');
  String get history => translate('history');
  String get withdrawalAmount => translate('withdrawalAmount');
  String get amount => translate('amount');
  String get invalidAmount => translate('invalidAmount');
  String get pleaseEnterAmount => translate('pleaseEnterAmount');
  String get amountExceedsBalance => translate('amountExceedsBalance');
  String get payoutMethod => translate('payoutMethod');
  String get vodafoneCash => translate('vodafoneCash');
  String get instaPay => translate('instaPay');
  String get accountDetails => translate('accountDetails');
  String get phoneNumber => translate('phoneNumber');
  String get emailAddress => translate('emailAddress');
  String get pleaseEnterAccountDetails => translate('pleaseEnterAccountDetails');
  String get invalidPhoneNumber => translate('invalidPhoneNumber');
  String get invalidEmailAddress => translate('invalidEmailAddress');
  String get identityConfirmation => translate('identityConfirmation');
  String get pleaseEnterPassword => translate('pleaseEnterPassword');
  String get submitting => translate('submitting');
  String get submitWithdrawalRequest => translate('submitWithdrawalRequest');
  String get withdrawalRequestSubmitted => translate('withdrawalRequestSubmitted');
  String get error => translate('error');
  String get noWithdrawalRequests => translate('noWithdrawalRequests');
  String get withdrawalRequestsWillAppear => translate('withdrawalRequestsWillAppear');
  String get pending => translate('pending');
  String get approved => translate('approved');
  String get processing => translate('processing');
  String get completed => translate('completed');
  String get rejected => translate('rejected');
  String get processed => translate('processed');

  String welcomeBackUser(String name) {
    return locale.languageCode == 'ar' ? 'مرحباً بعودتك، $name!' : 'Welcome back, $name!';
  }

  String welcomeFreelancer(String name) {
    return locale.languageCode == 'ar' ? 'مرحباً، $name!' : 'Welcome, $name!';
  }

  String paymentsAwaitingVerification(int count) {
    return locale.languageCode == 'ar' ? '$count مدفوعات في انتظار التحقق' : '$count payments awaiting verification';
  }
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['en', 'ar'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

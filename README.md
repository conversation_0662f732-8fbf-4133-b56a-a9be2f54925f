# anotherchance

A new Flutter project.

## Getting Started

# FreelanceHub - Complete Freelancing Service App

A full-featured mobile application built with **Flutter** and **Supabase** that connects clients with freelancers in a managed workflow.

## Features

### 🎯 **Client Features**
- **Account Management**: Secure registration and login
- **Post Service Requests**: Create detailed project requests with attachments
- **Receive & Review Offers**: Get proposals from freelancers
- **Real-time Chat**: Communicate with freelancers
- **Manual Payment System**: Bank transfer with proof upload
- **Order Management**: Track project progress and delivery
- **Rating System**: Rate completed work

### 💼 **Freelancer Features**
- **Profile Setup**: Showcase skills and portfolio
- **Browse Opportunities**: Find relevant project requests
- **Send Proposals**: Submit competitive offers
- **Real-time Communication**: Chat with clients
- **Work Delivery**: Submit completed projects
- **Job Management**: Track active and completed jobs

### 🛡️ **Admin Features**
- **User Management**: Manage clients and freelancers
- **Payment Verification**: Manually verify bank transfers
- **Order Control**: Assign freelancers and manage orders
- **System Messaging**: Send automated notifications
- **Activity Monitoring**: Track all platform activities
- **Data Export**: Export user and order data

### ✨ **Additional Features**
- **Dark Mode**: Toggle between light and dark themes
- **Real-time Notifications**: Stay updated on activities
- **File Attachments**: Support for images and documents
- **Responsive Design**: Works on all screen sizes
- **Secure Authentication**: Powered by Supabase Auth

## Tech Stack

- **Frontend**: Flutter (Dart)
- **Backend**: Supabase
  - Authentication
  - PostgreSQL Database
  - Real-time subscriptions
  - File Storage
- **State Management**: Provider
- **Navigation**: GoRouter
- **UI Components**: Material 3

## Getting Started

### Prerequisites
- Flutter SDK (3.7.2 or higher)
- Dart SDK
- Android Studio / VS Code
- Supabase account

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd anotherchance
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Configure Supabase**
   - Create a new project on [Supabase](https://supabase.com)
   - Update `lib/config/supabase_config.dart` with your credentials:
   ```dart
   static const String supabaseUrl = 'YOUR_SUPABASE_URL';
   static const String supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';
   ```

4. **Set up database tables** (SQL scripts needed)
   - profiles
   - service_requests
   - offers
   - orders
   - chats
   - messages

5. **Run the app**
   ```bash
   flutter run
   ```

## App Architecture

```
lib/
├── config/           # Configuration files
├── models/           # Data models
├── providers/        # State management
├── services/         # API services
├── screens/          # UI screens
│   ├── auth/         # Authentication screens
│   ├── client/       # Client-specific screens
│   ├── freelancer/   # Freelancer-specific screens
│   ├── admin/        # Admin-specific screens
│   └── chat/         # Chat functionality
└── main.dart         # App entry point
```

## User Roles

### Client Journey
1. Register/Login → Post Request → Receive Offers → Chat & Negotiate → Make Payment → Receive Delivery → Rate Work

### Freelancer Journey
1. Register/Login → Browse Requests → Send Offers → Chat & Negotiate → Deliver Work → Get Paid

### Admin Journey
1. Monitor Platform → Verify Payments → Manage Users → Handle Disputes → Export Data

## Payment Flow

1. **Client** uploads bank transfer proof
2. **Admin** manually verifies payment
3. **System** notifies freelancer to start work
4. **Freelancer** delivers completed work
5. **Client** reviews and marks as complete

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions, please contact the development team.

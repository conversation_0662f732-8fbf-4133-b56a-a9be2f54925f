import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/chat_provider.dart';
import '../../providers/language_provider.dart';
import '../../providers/demo_auth_provider.dart';
import '../../models/user_model.dart';
import '../../models/chat_model.dart';
import '../../models/message_model.dart';
import '../../models/order_model.dart';
import '../../widgets/chat/enhanced_chat_widgets.dart';

/// Demo screen to showcase enhanced chat features
class EnhancedChatDemo extends StatefulWidget {
  const EnhancedChatDemo({super.key});

  @override
  State<EnhancedChatDemo> createState() => _EnhancedChatDemoState();
}

class _EnhancedChatDemoState extends State<EnhancedChatDemo> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeDemo();
    });
  }

  void _initializeDemo() async {
    final chatProvider = Provider.of<ChatProvider>(context, listen: false);
    final authProvider = Provider.of<DemoAuthProvider>(context, listen: false);

    // Quick login as client for demo
    authProvider.quickLogin(UserRole.client);

    // Initialize chat provider
    if (authProvider.user != null) {
      await chatProvider.setUser(authProvider.user!.id);
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isArabic = languageProvider.locale.languageCode == 'ar';

    return Scaffold(
      appBar: AppBar(
        title: Text(isArabic ? 'عرض المحادثة المحسنة' : 'Enhanced Chat Demo'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Consumer<ChatProvider>(
        builder: (context, chatProvider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Demo Header
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          isArabic ? 'ميزات المحادثة المحسنة' : 'Enhanced Chat Features',
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          isArabic
                              ? 'هذا عرض توضيحي للميزات الجديدة في نظام المحادثة'
                              : 'This demo showcases the new enhanced chat system features',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Enhanced Chat List Item Demo
                Text(
                  isArabic ? 'قائمة المحادثات مع شارات عدم القراءة:' : 'Chat List with Unread Badges:',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),

                // Demo chat items
                EnhancedChatListItem(
                  chat: ChatModel(
                    id: 'demo1',
                    requestId: 'req1',
                    clientId: 'client1',
                    freelancerId: 'freelancer1',
                    lastMessage: isArabic ? 'مرحبا، كيف يمكنني مساعدتك؟' : 'Hello, how can I help you?',
                    lastMessageTime: DateTime.now().subtract(const Duration(minutes: 5)),
                    createdAt: DateTime.now(),
                  ),
                  unreadCount: 3,
                  recipientName: isArabic ? 'أحمد المطور' : 'Ahmed Developer',
                  recipientAvatar: '👨‍💻',
                  onTap: () => _showChatDemo(context),
                ),

                const SizedBox(height: 8),

                EnhancedChatListItem(
                  chat: ChatModel(
                    id: 'demo2',
                    requestId: 'req2',
                    clientId: 'client1',
                    freelancerId: 'freelancer2',
                    lastMessage: isArabic ? 'تم تسليم المشروع' : 'Project delivered',
                    lastMessageTime: DateTime.now().subtract(const Duration(hours: 2)),
                    createdAt: DateTime.now(),
                  ),
                  unreadCount: 0,
                  recipientName: isArabic ? 'فاطمة المصممة' : 'Fatima Designer',
                  recipientAvatar: '👩‍🎨',
                  onTap: () => _showChatDemo(context),
                ),

                const SizedBox(height: 24),

                // Timeline Message Demo
                Text(
                  isArabic ? 'رسائل الجدول الزمني:' : 'Timeline Messages:',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),

                TimelineMessageWidget(
                  message: MessageModel(
                    id: 'timeline1',
                    chatId: 'demo1',
                    senderId: 'system',
                    content:
                        isArabic
                            ? 'تم إنشاء الطلب #12345 لـ "تطوير تطبيق فلاتر" - \$500'
                            : 'Order #12345 created for "Flutter App Development" - \$500',
                    type: MessageType.timeline,
                    createdAt: DateTime.now().subtract(const Duration(hours: 1)),
                    metadata: {
                      'type': 'order_created',
                      'order_id': '12345',
                      'amount': 500.0,
                      'service_title': 'Flutter App Development',
                    },
                  ),
                  languageCode: languageProvider.locale.languageCode,
                ),

                const SizedBox(height: 8),

                TimelineMessageWidget(
                  message: MessageModel(
                    id: 'timeline2',
                    chatId: 'demo1',
                    senderId: 'system',
                    content:
                        isArabic ? 'تم تأكيد الدفع للطلب #12345 - \$500' : 'Payment confirmed for Order #12345 - \$500',
                    type: MessageType.timeline,
                    createdAt: DateTime.now().subtract(const Duration(minutes: 30)),
                    metadata: {'type': 'payment_confirmed', 'order_id': '12345', 'amount': 500.0},
                  ),
                  languageCode: languageProvider.locale.languageCode,
                ),

                const SizedBox(height: 24),

                // Quick Actions Demo
                Text(
                  isArabic ? 'الإجراءات السريعة:' : 'Quick Actions:',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),

                ChatQuickActions(
                  orderId: '12345',
                  orderStatus: OrderStatus.delivered,
                  isClient: true,
                  onApproveDelivery: () => _showSnackBar(context, isArabic ? 'تم قبول التسليم' : 'Delivery Approved'),
                  onRequestRevision: () => _showSnackBar(context, isArabic ? 'تم طلب المراجعة' : 'Revision Requested'),
                  onViewDelivery: () => _showSnackBar(context, isArabic ? 'عرض التسليم' : 'View Delivery'),
                ),

                const SizedBox(height: 24),

                // Order Status Indicator Demo
                Text(
                  isArabic ? 'مؤشر حالة الطلب:' : 'Order Status Indicator:',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),

                const OrderStatusIndicator(status: OrderStatus.delivered, orderId: '12345'),

                const SizedBox(height: 24),

                // Demo Instructions
                Card(
                  color: Colors.blue.withValues(alpha: 0.1),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          isArabic ? 'كيفية الاستخدام:' : 'How to Use:',
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          isArabic
                              ? '• انقر على عنصر المحادثة لفتح المحادثة\n• استخدم الإجراءات السريعة لإدارة الطلبات\n• تتبع تقدم المشروع من خلال رسائل الجدول الزمني\n• تحقق من حالة الطلب في أي وقت'
                              : '• Tap on chat item to open conversation\n• Use quick actions to manage orders\n• Track project progress through timeline messages\n• Check order status anytime',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  void _showChatDemo(BuildContext context) {
    final isArabic = Provider.of<LanguageProvider>(context, listen: false).locale.languageCode == 'ar';
    _showSnackBar(context, isArabic ? 'سيتم فتح المحادثة هنا' : 'Chat would open here');
  }

  void _showSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(message), duration: const Duration(seconds: 2)));
  }
}

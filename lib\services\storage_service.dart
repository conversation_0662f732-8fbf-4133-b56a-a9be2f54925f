import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import '../config/supabase_config.dart';

class StorageService {
  static const String _bucketName = 'uploads';

  static Future<String?> uploadFile(File file, String folder) async {
    try {
      final fileName = '${DateTime.now().millisecondsSinceEpoch}_${file.path.split('/').last}';
      final filePath = '$folder/$fileName';

      await SupabaseConfig.client.storage.from(_bucketName).upload(filePath, file);

      final url = SupabaseConfig.client.storage.from(_bucketName).getPublicUrl(filePath);

      return url;
    } catch (e) {
      throw Exception('Failed to upload file: $e');
    }
  }

  static Future<String?> uploadImage(File image, String folder) async {
    try {
      final fileName = '${DateTime.now().millisecondsSinceEpoch}_${image.path.split('/').last}';
      final filePath = '$folder/$fileName';

      await SupabaseConfig.client.storage.from(_bucketName).upload(filePath, image);

      final url = SupabaseConfig.client.storage.from(_bucketName).getPublicUrl(filePath);

      return url;
    } catch (e) {
      throw Exception('Failed to upload image: $e');
    }
  }

  static Future<File?> pickImage({ImageSource source = ImageSource.gallery}) async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(source: source);

      if (pickedFile != null) {
        return File(pickedFile.path);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to pick image: $e');
    }
  }

  static Future<File?> pickFile() async {
    try {
      final result = await FilePicker.platform.pickFiles();

      if (result != null && result.files.single.path != null) {
        return File(result.files.single.path!);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to pick file: $e');
    }
  }

  static Future<List<File>> pickMultipleFiles() async {
    try {
      final result = await FilePicker.platform.pickFiles(allowMultiple: true);

      if (result != null) {
        return result.paths.where((path) => path != null).map((path) => File(path!)).toList();
      }
      return [];
    } catch (e) {
      throw Exception('Failed to pick files: $e');
    }
  }

  static Future<List<String>> uploadMultipleFiles(List<File> files, String folder) async {
    try {
      final urls = <String>[];

      for (final file in files) {
        final url = await uploadFile(file, folder);
        if (url != null) {
          urls.add(url);
        }
      }

      return urls;
    } catch (e) {
      throw Exception('Failed to upload multiple files: $e');
    }
  }

  static Future<void> deleteFile(String filePath) async {
    try {
      await SupabaseConfig.client.storage.from(_bucketName).remove([filePath]);
    } catch (e) {
      throw Exception('Failed to delete file: $e');
    }
  }

  static String getFileNameFromUrl(String url) {
    final uri = Uri.parse(url);
    return uri.pathSegments.last;
  }

  static String getFilePathFromUrl(String url) {
    final uri = Uri.parse(url);
    final pathSegments = uri.pathSegments;
    final bucketIndex = pathSegments.indexOf(_bucketName);
    if (bucketIndex != -1 && bucketIndex < pathSegments.length - 1) {
      return pathSegments.sublist(bucketIndex + 1).join('/');
    }
    return '';
  }
}

enum PaymentRequestStatus { requested, pending, confirmed, rejected }

class PaymentRequestModel {
  final String id;
  final String requestId;
  final String clientId;
  final String freelancerId;
  final String offerId;
  final double amount;
  final PaymentRequestStatus status;
  final String? paymentProofUrl;
  final String? adminNotes;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final DateTime? confirmedAt;
  final String? confirmedByAdminId;

  PaymentRequestModel({
    required this.id,
    required this.requestId,
    required this.clientId,
    required this.freelancerId,
    required this.offerId,
    required this.amount,
    this.status = PaymentRequestStatus.requested,
    this.paymentProofUrl,
    this.adminNotes,
    required this.createdAt,
    this.updatedAt,
    this.confirmedAt,
    this.confirmedByAdminId,
  });

  factory PaymentRequestModel.fromJson(Map<String, dynamic> json) {
    return PaymentRequestModel(
      id: json['id'],
      requestId: json['request_id'],
      clientId: json['client_id'],
      freelancerId: json['freelancer_id'],
      offerId: json['offer_id'],
      amount: json['amount'].toDouble(),
      status: PaymentRequestStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
      ),
      paymentProofUrl: json['payment_proof_url'],
      adminNotes: json['admin_notes'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
      confirmedAt: json['confirmed_at'] != null ? DateTime.parse(json['confirmed_at']) : null,
      confirmedByAdminId: json['confirmed_by_admin_id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'request_id': requestId,
      'client_id': clientId,
      'freelancer_id': freelancerId,
      'offer_id': offerId,
      'amount': amount,
      'status': status.toString().split('.').last,
      'payment_proof_url': paymentProofUrl,
      'admin_notes': adminNotes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'confirmed_at': confirmedAt?.toIso8601String(),
      'confirmed_by_admin_id': confirmedByAdminId,
    };
  }

  PaymentRequestModel copyWith({
    String? id,
    String? requestId,
    String? clientId,
    String? freelancerId,
    String? offerId,
    double? amount,
    PaymentRequestStatus? status,
    String? paymentProofUrl,
    String? adminNotes,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? confirmedAt,
    String? confirmedByAdminId,
  }) {
    return PaymentRequestModel(
      id: id ?? this.id,
      requestId: requestId ?? this.requestId,
      clientId: clientId ?? this.clientId,
      freelancerId: freelancerId ?? this.freelancerId,
      offerId: offerId ?? this.offerId,
      amount: amount ?? this.amount,
      status: status ?? this.status,
      paymentProofUrl: paymentProofUrl ?? this.paymentProofUrl,
      adminNotes: adminNotes ?? this.adminNotes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      confirmedAt: confirmedAt ?? this.confirmedAt,
      confirmedByAdminId: confirmedByAdminId ?? this.confirmedByAdminId,
    );
  }
}
